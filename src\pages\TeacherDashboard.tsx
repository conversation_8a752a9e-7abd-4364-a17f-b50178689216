import React, {useState,useEffect,useRef }from "react";
import { useAuth } from "react-oidc-context"; // Updated import
import { Link } from "react-router-dom";
import { BookOpen, Users, Calendar,GraduationCap,X, MessageSquare, Bell, DollarSign, FileText, ClipboardCheck, Clock } from "lucide-react";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { PerformanceCard } from "@/components/dashboard/PerformanceCard";
import { StatsCards } from "@/components/dashboard/StatsCards";
import { ScheduleSection } from "@/components/dashboard/ScheduleSection";
import { AnnouncementsSection } from "@/components/dashboard/AnnouncementsSection";
import { NextClassAlert } from "@/components/dashboard/NextClassAlert";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { ScheduleEvent } from "@/types";
import { generateAvatarUrl } from "@/lib/utils";
import { UserRole } from "@/types";
import { useUserRole } from "@/hooks/useUserRole";
import { getTeacherNextClassAlert } from "@/services/scheduleService";
import { useSelector } from "react-redux";
export default function TeacherDashboard() {
       const { userTimezone, updateUserTimezone } = useUserRole();  
  
     const auth = useAuth();
        const [showWelcomeCard, setShowWelcomeCard] = useState(true);
  
  const isMobile = useIsMobile();
  const currentDate = new Date();
  const formattedDate = new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(currentDate);
    const [nextClasss, setNextClasss] = useState<ScheduleEvent | undefined>(undefined);
    const hasFetched = useRef(false);
const userData = useSelector((state: any) => state.user.userData);
    console.log(userData)
    //console.log(userData.timezone);
    useEffect(() => {
      if (userData?.timezone) {
        updateUserTimezone(userData.timezone);
      }
    }, [userData?.timezone, updateUserTimezone]);
    // Get user from OIDC and Redux
    const user = auth.isAuthenticated ? {
      id: auth.user?.profile.sub || "",
      name: userData?.name || auth.user?.profile.name || "User",
      email: auth.user?.profile.email || "",
      role: (auth.user?.profile["custom:role"] as UserRole) ,
      avatar: generateAvatarUrl(userData?.name || auth.user?.profile.name || "User", "3498db")
    } : null;
      
const fetchNextClassAlert = async () => {
      try {
        const next = await getTeacherNextClassAlert(auth.user.access_token);
        setNextClasss(next);
      } catch (error) {
        console.error("Error fetching next class alert:", error);
        setNextClasss(undefined);
      }
    };
 useEffect(() => {
     if (auth.user?.access_token && !hasFetched.current) {
       hasFetched.current = true;
       fetchNextClassAlert();
     }
   }, [auth.user?.access_token]);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 17) return "Good afternoon";
    return "Good evening";
  };

  const navigationItems = [{
    name: "Classes",
    path: "/classes",
    icon: BookOpen,
    color: "bg-blue-100 text-blue-600"
  }, {
    name: "Enrollments",
    path: "/enrollments",
    icon: Users,
    color: "bg-green-100 text-green-600"
  },/* {
    name: "Schedule",
    path: "/schedule/all",
    icon: Calendar,
    color: "bg-purple-100 text-purple-600"
  }, {
    name: "Chat",
    path: "/chat",
    icon: MessageSquare,
    color: "bg-amber-100 text-amber-600"
  }, {
    name: "PTM",
    path: "/parent-meetings",
    icon: Clock,
    color: "bg-red-100 text-red-600"
  }, */{
    name: "Payments",
    path: "/payments",
    icon: DollarSign,
    color: "bg-emerald-100 text-emerald-600"
  }, {
    name: "Expenses",
    path: "/expenses",
    icon: FileText,
    color: "bg-indigo-100 text-indigo-600"
  }/*, {
    name: "Attendance",
    path: "/attendance",
    icon: ClipboardCheck,
    color: "bg-pink-100 text-pink-600"
  }*/];

  return <div className="min-h-screen bg-gradient-to-br from-white to-gray-100">
      <div className="flex-grow">
        <DashboardHeader />

        <div className="container mx-auto px-4 sm:px-6 py-6">
           {/* Welcome Card */}
          {showWelcomeCard && (
            <div className="mb-6 bg-white rounded-2xl p-4 sm:p-6 shadow-lg border border-slate-200 hover:shadow-xl transition-shadow duration-300 relative">
              <button
                onClick={() => setShowWelcomeCard(false)}
                className="absolute top-4 right-4 p-1 rounded-full hover:bg-slate-100 transition-colors"
              >
                <X className="h-5 w-5 text-slate-400 hover:text-slate-600" />
              </button>
              <h1 className="text-xl sm:text-2xl font-bold text-slate-800 mb-2 flex items-center gap-2 pr-8">
                {getGreeting()}, {user?.name?.split(' ')[0]}! 
                <GraduationCap className="text-purple-600 h-6 w-6 sm:h-8 sm:w-8" />
              </h1>
              <p className="text-slate-600 text-sm sm:text-base">
                Your learning journey continues today. Let's make it extraordinary!
              </p>
            </div>
          )}
          <div className="mb-6">
            <NextClassAlert nextClass={nextClasss} />
          </div>

          <div className="mb-6">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              {navigationItems.map(item => <Link to={item.path} key={item.name}>
                  <div className={`flex items-center gap-3 p-4 rounded-xl ${item.color} hover:opacity-90 transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md h-full`}>
                    <div className="rounded-full bg-white/90 p-2 flex-shrink-0">
                      <item.icon className="h-5 w-5" />
                    </div>
                    <span className="font-medium">{item.name}</span>
                  </div>
                </Link>)}
            </div>
          </div>

      {/*   <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 mb-4">
            <div className="lg:col-span-1 bg-white rounded-xl shadow-sm  hover:shadow-md transition-shadow">
              <PerformanceCard user={user} />
            </div>
            <div className="lg:col-span-2 bg-white rounded-xl shadow-sm  hover:shadow-md transition-shadow">
              <StatsCards />
            </div>
          </div>*/}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-2">
            <div className="lg:col-span-2 order-2 lg:order-1 bg-white rounded-xl shadow-sm p-4 hover:shadow-md transition-shadow">
              <ScheduleSection />
            </div>
            <div className="order-1 lg:order-2 bg-white rounded-xl shadow-sm p-4 hover:shadow-md transition-shadow">
              <AnnouncementsSection hideCreateButton={false}/>
            </div>
          </div>
        </div>
      </div>
    </div>;
}
