import React from "react";
import { Assignment, AssignmentSubmission } from "@/types";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { format } from "date-fns";
import { Calendar, FileText, Award, MessageSquare, CheckCircle2, XCircle, ExternalLink } from "lucide-react";

interface SubmissionViewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  studentName: string;
  submission: AssignmentSubmission;
  assignment: Assignment;
}

export function SubmissionViewDialog({
  open,
  onOpenChange,
  studentName,
  submission,
  assignment,
}: SubmissionViewDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">{assignment.title}</DialogTitle>
          <DialogDescription>
            Submission by {studentName}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Submitted: {format(new Date(submission.submittedAt), "PPP 'at' p")}
              </span>
            </div>
            <Badge 
              variant={
                submission.status === "graded" 
                  ? "default" 
                  : submission.status === "complete" 
                  ? "secondary" 
                  : "outline"
              }
            >
              {submission.status === "graded" 
                ? "Graded" 
                : submission.status === "complete" 
                ? "Complete" 
                : "Pending Review"}
            </Badge>
          </div>

          <Separator />

          {/* Questions and Answers Section */}
          {assignment.questions && submission.answers && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Questions & Answers</h3>
              {assignment.questions.map((question, index) => {
                const answer = submission.answers?.find(a => a.questionIndex === index);
                return (
                  <Card key={index} className="border-l-4 border-l-primary">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base font-medium flex items-start gap-2">
                        <span className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs">
                          {index + 1}
                        </span>
                        <span>{question}</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="bg-muted/50 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <MessageSquare className="h-4 w-4 text-primary" />
                          <p className="text-sm font-medium">Student's Answer:</p>
                        </div>
                        <p className="text-sm whitespace-pre-wrap pl-6">
                          {answer?.answer || "No answer provided"}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}

          <Separator />

          {/* File Submission Section */}
          <div className="space-y-3">
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <FileText className="h-4 w-4 text-primary" />
                <h4 className="font-medium">Submitted Files</h4>
              </div>
              {submission.fileUrl ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <a 
                      href={submission.fileUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm text-primary hover:underline flex items-center gap-1"
                    >
                      <ExternalLink className="h-3 w-3" />
                      Open in new tab
                    </a>
                  </div>
                  <div className="border rounded-lg overflow-hidden bg-background">
                    <iframe
                      src={submission.fileUrl}
                      className="w-full h-96"
                      title="Submission file preview"
                    />
                  </div>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  No file attached to this submission.
                </p>
              )}
            </div>
          </div>

          {/* Score and Feedback Section */}
          <div className="grid md:grid-cols-2 gap-4">
            {submission.score !== undefined && (
              <div className="bg-muted/50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Award className="h-4 w-4 text-primary" />
                  <h4 className="font-medium">Score</h4>
                </div>
                <div className="text-4xl font-bold text-primary">
                  {submission.score}/100
                </div>
              </div>
            )}

            {submission.feedback && (
              <div className="bg-muted/50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <MessageSquare className="h-4 w-4 text-primary" />
                  <h4 className="font-medium">Teacher Feedback</h4>
                </div>
                <p className="text-sm whitespace-pre-wrap">{submission.feedback}</p>
              </div>
            )}
          </div>

        </div>
      </DialogContent>
    </Dialog>
  );
}
