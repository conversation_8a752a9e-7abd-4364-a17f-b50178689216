import { LeaveRequest } from '../types';

export const createLeaveRequest = async (accessToken: string,leaveData: Omit<LeaveRequest, 'id' | 'status' | 'appliedAt'|'studentId'|'studentName'|'createdDate'>): Promise<LeaveRequest> => {
  const response = await fetch('/api/leave/v1/leaves', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' ,
         "Authorization": `Bearer ${accessToken}`
   
    },
    body: JSON.stringify(leaveData),
  });
  
if (response.status === 401) {
    window.location.href = "/";
    //return;
  }
  if (!response.ok) {
    throw new Error("Failed to create Leaves");
  }  return response.json();
};

export const getLeaveRequestsByClass = async (accessToken: string,classId: string): Promise<LeaveRequest[]> => {
  const response = await fetch(`/api/leave/v1/leaves/class/${classId}`,{
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
});
  if (!response.ok) {
    throw new Error("Failed to fetch Leaves");
  }
  return response.json();
};
export const getLeaveRequestsForStudent = async (accessToken: string,classId: string): Promise<LeaveRequest[]> => {
  const response = await fetch(`/api/leave/v1/leaves/my-leaves/class/${classId}`,{
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
});
  if (!response.ok) {
    throw new Error("Failed to fetch Leaves");
  }
  return response.json();
};

export const updateLeaveRequestStatus = async (accessToken: string,leaveId: string, status: 'APPROVED' | 'DENIED', teacherComments?: string): Promise<LeaveRequest> => {
  const response = await fetch(`/api/leave/v1/leaves/${leaveId}/process`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json',
      "Authorization": `Bearer ${accessToken}` },
    body: JSON.stringify({ status, teacherComments }),
  });
  if (response.status === 401) {
    window.location.href = "/";
    //return;
  }
  if (!response.ok) {
    throw new Error("Failed to update Leaves");
  }return response.json();
};