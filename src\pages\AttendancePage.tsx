
import React, { useState, useEffect } from "react";
import { useAuth } from "react-oidc-context"; // Updated import
import { useApp } from "@/context/AppContext";
import { useNavigate } from "react-router-dom";
import { 
  Calendar,
  User,
  Check,
  X,
  Clock,
  ClipboardCheck,
  ArrowLeft
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { UserRole } from "@/types";
import { format } from "date-fns";
import { toast } from "sonner";
import { useSelector } from "react-redux";
import { useUserRole } from "@/hooks/useUserRole";


export default function AttendancePage() {
  const  user  = useAuth().user;
  const navigate = useNavigate();
  const { classes, recordAttendance, attendance, getAttendanceByStudent } = useApp();
  
  const [selectedClass, setSelectedClass] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [students, setStudents] = useState<Array<{id: string, name: string}>>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<Record<string, string>>({});
  const [selectedStudent, setSelectedStudent] = useState<string | null>(null);
  const { selectedRole } = useUserRole();

  const userClasses = selectedRole=== UserRole.TEACHER
    ? classes.filter(c => user?.profile.sub && c.teacherId === user.profile.sub)
    : classes.filter(c => user?.profile.sub && c.students.includes(user.profile.sub));

  useEffect(() => {
    if (!selectedClass) return;
    
    const currentClass = classes.find(c => c.id === selectedClass);
    if (!currentClass) return;
    
    const studentList = currentClass.students.map(studentId => ({
      id: studentId,
      name: `Student ${studentId}`
    }));
    
    setStudents(studentList);
    
    const records: Record<string, string> = {};
    studentList.forEach(student => {
      const dateString = format(selectedDate, 'yyyy-MM-dd');
      const existingRecord = attendance.find(
        a => a.studentId === student.id && 
             a.classId === selectedClass && 
             a.date === dateString
      );
      
      if (existingRecord) {
        records[student.id] = existingRecord.status;
      } else {
        records[student.id] = 'absent';
      }
    });
    
    setAttendanceRecords(records);
    setSelectedStudent(null); // Reset selected student when class changes
  }, [selectedClass, classes, selectedDate, attendance]);

  const handleAttendanceChange = (studentId: string, status: "present" | "absent" | "late" | "excused") => {
    setAttendanceRecords(prev => ({
      ...prev,
      [studentId]: status
    }));
    
    if (selectedClass) {
      recordAttendance({
        studentId,
        classId: selectedClass,
        date: format(selectedDate, 'yyyy-MM-dd'),
        status
      });
    }
  };

  const handleRowClick = (studentId: string) => {
    setSelectedStudent(selectedStudent === studentId ? null : studentId);
  };

  const handleSaveAll = () => {
    Object.entries(attendanceRecords).forEach(([studentId, status]) => {
      recordAttendance({
        studentId,
        classId: selectedClass,
        date: format(selectedDate, 'yyyy-MM-dd'),
        status: status as "present" | "absent" | "late" | "excused"
      });
    });
    
    toast.success("All attendance records saved successfully");
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <Check className="h-5 w-5 text-green-500" />;
      case 'absent':
        return <X className="h-5 w-5 text-red-500" />;
      case 'late':
        return <Clock className="h-5 w-5 text-amber-500" />;
      case 'excused':
        return <Calendar className="h-5 w-5 text-blue-500" />;
      default:
        return null;
    }
  };

  const handleBackToDashboard = () => {
    switch (selectedRole) {
      case UserRole.TEACHER:
        navigate('/teacher-dashboard');
        break;
      case UserRole.STUDENT:
        navigate('/student-dashboard');
        break;
      case UserRole.PARENT:
        navigate('/parent-dashboard');
        break;
      default:
        navigate('/dashboard');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex-grow">
        <header className="bg-white p-6 flex justify-between items-center h-14">
          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={handleBackToDashboard}
              className="mr-2"
            >
              <ArrowLeft className="h-6 w-6 text-gray-500" />
            </Button>
            <h1 className="text-2xl font-semibold">Attendance Management</h1>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" asChild>
              <User className="h-6 w-6 text-gray-500" />
            </Button>
          </div>
        </header>

        <div className="p-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {selectedRole === UserRole.TEACHER ? "Mark Attendance" : "View Attendance"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4">
                <div className="flex flex-wrap gap-4 mb-6">
                  <div className="w-full md:w-64">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Class</label>
                    <Select 
                      value={selectedClass} 
                      onValueChange={(value) => {
                        setSelectedClass(value);
                        setSelectedStudent(null);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a class" />
                      </SelectTrigger>
                      <SelectContent>
                        {userClasses.map(cls => (
                          <SelectItem key={cls.id} value={cls.id}>
                            {cls.className}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="w-full md:w-64">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button 
                          variant="outline"
                          className="w-full justify-start text-left"
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          {format(selectedDate, 'PPP')}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <CalendarComponent
                          mode="single"
                          selected={selectedDate}
                          onSelect={(date) => {
                            if (date) {
                              setSelectedDate(date);
                              setSelectedStudent(null);
                            }
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {selectedClass && (
                  selectedRole === UserRole.TEACHER ? (
                    <div>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Student</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {students.map(student => (
                            <TableRow 
                              key={student.id}
                              className="cursor-pointer"
                              onClick={() => handleRowClick(student.id)}
                            >
                              <TableCell>{student.name}</TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  {getStatusIcon(attendanceRecords[student.id] || 'absent')}
                                  <span className="ml-2 capitalize">
                                    {attendanceRecords[student.id] || 'absent'}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell>
                                {selectedStudent === student.id && (
                                  <div className="flex space-x-2">
                                    <Button 
                                      size="sm" 
                                      variant={attendanceRecords[student.id] === 'present' ? 'default' : 'outline'}
                                      className={attendanceRecords[student.id] === 'present' ? 'bg-green-600 hover:bg-green-700' : ''}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleAttendanceChange(student.id, 'present');
                                      }}
                                    >
                                      Present
                                    </Button>
                                    <Button 
                                      size="sm" 
                                      variant={attendanceRecords[student.id] === 'absent' ? 'default' : 'outline'}
                                      className={attendanceRecords[student.id] === 'absent' ? 'bg-red-600 hover:bg-red-700' : ''}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleAttendanceChange(student.id, 'absent');
                                      }}
                                    >
                                      Absent
                                    </Button>
                                    <Button 
                                      size="sm" 
                                      variant={attendanceRecords[student.id] === 'late' ? 'default' : 'outline'}
                                      className={attendanceRecords[student.id] === 'late' ? 'bg-amber-600 hover:bg-amber-700' : ''}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleAttendanceChange(student.id, 'late');
                                      }}
                                    >
                                      Late
                                    </Button>
                                    <Button 
                                      size="sm" 
                                      variant={attendanceRecords[student.id] === 'excused' ? 'default' : 'outline'}
                                      className={attendanceRecords[student.id] === 'excused' ? 'bg-blue-600 hover:bg-blue-700' : ''}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleAttendanceChange(student.id, 'excused');
                                      }}
                                    >
                                      Excused
                                    </Button>
                                  </div>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                      <div className="mt-4 flex justify-end">
                        <Button className="bg-purple-600 hover:bg-purple-700" onClick={handleSaveAll}>
                          Save All
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <h3 className="text-lg font-medium mb-4">Your Attendance for {userClasses.find(c => c.id === selectedClass)?.className}</h3>
                      {selectedStudent ? (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Date</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Notes</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {user?.profile.sub && getAttendanceByStudent(user.profile.sub, selectedClass).map(record => (
                              <TableRow key={record.id}>
                                <TableCell>{record.date}</TableCell>
                                <TableCell>
                                  <div className="flex items-center">
                                    {getStatusIcon(record.status)}
                                    <span className="ml-2 capitalize">{record.status}</span>
                                  </div>
                                </TableCell>
                                <TableCell>{record.notes || 'No notes'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      ) : (
                        <div className="text-center py-8">
                          <ClipboardCheck className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                          <h3 className="text-lg font-medium text-gray-600 mb-2">Click on any row to view attendance details</h3>
                        </div>
                      )}
                    </div>
                  )
                )}

                {!selectedClass && (
                  <div className="text-center py-8">
                    <ClipboardCheck className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">No Class Selected</h3>
                    <p className="text-gray-500">
                      Please select a class to {selectedRole === UserRole.TEACHER ? "mark" : "view"} attendance.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
