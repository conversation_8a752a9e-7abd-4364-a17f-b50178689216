export async function createAssignment(accessToken: string,payload: any) {

  const response = await fetch(`/api/assignment/v1/assignments`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(payload)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create assignment");
  }
  return response.json();
}

export async function gradeAssignmentByTeacher(accessToken: string,payload: any) {

  const response = await fetch(`/api/assignment/v1/submissions/mark`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(payload)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to grade assignment");
  }
  return response.json();
}

export async function updateAssignment(accessToken: string,payload: any,assignmentId: string) {

  const response = await fetch(`/api/assignment/v1/assignments/${assignmentId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(payload)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update assignment");
  }
  return response.json();
}

export async function generateAIQuestions(accessToken: string,payload: any) {

  const response = await fetch('/api/assignment/v1/ai/generate-questions', {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(payload)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create assignment");
  }
  return response.json();
}

export async function submitAssignmentByStudent(accessToken: string,payload: any) {
console.log(payload)
  const response = await fetch('/api/assignment/v1/student/submissions', {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(payload)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create assignment");
  }
  return response.json();
}

export async function getAssignmentsForTeacher(accessToken: string) {
  const response = await fetch(`/api/assignment/v1/teacher/assignments`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch Assignments");
  }
  return response.json();
}


export async function getAssignmentsForStudent(accessToken: string,classroomId: string) {
  const response = await fetch(`/api/assignment/v1/student/classroom/${classroomId}/assignments`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch Assignments");
  }
  return response.json();
}


export async function getTeacherAssignmentsForClass(accessToken: string , classId: string) {
  const response = await fetch(`/api/assignment/v1/teacher/classroom/${classId}/assignments`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch Assignments");
  }
  return response.json();
}

export async function getAllSubmisionsForAssignment(accessToken: string , AssignmentId: string) {
  const response = await fetch(`/api/assignment/v1/assignments/${AssignmentId}/submissions`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch Assignments");
  }
  return response.json();
}

export async function getStudentSubmittedAssignment(accessToken: string,assignmentId: string) {
  const response = await fetch(`/api/assignment/v1/student/assignments/${assignmentId}/submission`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch Assignments");
  }
  return response.json();
}

// Delete an assignment
export async function deleteExistingAsisgnment(accessToken: string,id: string) {
  const response = await fetch(`/api/assignment/v1/assignments/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to delete assignment");
  }
  return true;
}

// Update an existing announcement
export async function updateExistingAssignment(accessToken: string,id: string, updateData: any) {
  const response = await fetch(`/api/assignment/v1/assignments/${id}/status?status=DRAFT`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(updateData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update assignment");
  }
  return response.json();
}

// Update an assignment status
export async function updateAssignmentStatus(accessToken: string,id: string, status: any) {
  const response = await fetch(`/api/assignment/v1/assignments/${id}/status?status=${status}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
   // body: JSON.stringify(updateData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update assignment");
  }
  return response.json();
}

// Delete a question in an assignment
export async function deleteExistingQuestion(accessToken: string,aId: string, qid: string) {
  const response = await fetch(`/api/assignment/v1/assignments/${aId}/questions/${qid}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to delete assignment");
  }
  return true;
}
