// src/hooks/useUserRole.ts
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { setSelectedRole, setTimezone } from "../redux/userSlice";
import { useEffect, useState } from "react";

export const useUserRole = () => {
  const reduxRole = useSelector((state: any) => state.user.selectedRole);
  const reduxTimezone = useSelector((state: any) => state.user.timezone);
  const [localRole, setLocalRole] = useState('');
  const dispatch = useDispatch();
  
  useEffect(() => {
    const storedRole = typeof window !== 'undefined' ? sessionStorage.getItem('userRole') || '' : '';
    setLocalRole(storedRole);
    
    if (storedRole && !reduxRole) {
      dispatch(setSelectedRole(storedRole));
    }
  }, [reduxRole, dispatch]);
  
  const selectedRole = reduxRole || localRole;
  const selectedTimezone = reduxTimezone;
  
  const updateRole = (role: string) => {
    dispatch(setSelectedRole(role));
  };
  
  const updateUserTimezone = (timezone: string) => {
    dispatch(setTimezone(timezone));
  };
  
  return { selectedRole, updateRole, userTimezone: selectedTimezone, updateUserTimezone };
};