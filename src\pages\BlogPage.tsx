import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ci<PERSON>, <PERSON>, X, ArrowRight } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "react-oidc-context";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import RichTextEditor from "@/components/RichTextEditor";
import { toast } from "sonner";
import { UserRole } from "@/types";
import { generateAvatarUrl } from "@/lib/utils";
import { useSelector } from "react-redux";
import { useUserRole } from "@/hooks/useUserRole";
import Header from "@/components/layout/Header";


import Footer from "@/components/Footer"; // or your preferred toast library
import { Sparkles } from "lucide-react";
export default function BlogPage() {
  const navigate = useNavigate();
  const auth = useAuth();
  const [editingBlogId, setEditingBlogId] = useState<string | null>(null);
  
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;
    const [isSubmitting, setIsSubmitting] = useState(false);

const handleSignUp  = async () => {
   // window.location.href = "https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/signup?client_id=76u1v7el416ebllhpbhtqpmlh0&code_challenge=Cjh7j5XvSKwPZ5ahIhP5j2tuEvZiuoSm811Q62N0wFs&code_challenge_method=S256&redirect_uri=http%3A%2F%2Flocalhost%3A8081%2Flogin%2Foauth2%2Fcode%2Fcognito&response_type=code&scope=email+openid+phone&state=80e73e7091c04c30a0c4904373b2096f";
     setIsSubmitting(true);
    
    try {
      // Store form values in localStorage to access after redirect back from Cognito
    //  localStorage.setItem("registerFormData", JSON.stringify(form.getValues()));
      
      // Redirect to Cognito signup page
      await auth.signinRedirect({ prompt: "login" });
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };

  const [blogs, setBlogs] = useState([
    {
      id: "1",
      title: "Understanding Quantum Physics for Beginners",
      content: "<p>Quantum physics is often seen as a complex subject, but with the right approach, beginners can grasp the fundamental concepts...</p><p>In this blog, we'll explore the basic principles of quantum mechanics and how they differ from classical physics.</p>",
      date: "2023-05-15",
      author: "Dr. Richard Feynman",
      tags: ["Physics", "Quantum Mechanics", "Science"],
    },
    {
      id: "2",
      title: "The Importance of Practical Experiments in Physics Education",
      content: "<p>Laboratory experiments play a crucial role in physics education. They help students connect theoretical concepts with real-world observations...</p><p>This blog discusses effective approaches to designing physics experiments for high school and undergraduate students.</p>",
      date: "2023-06-22",
      author: "Dr. Richard Feynman",
      tags: ["Education", "Experiments", "Teaching"],
    },
  ]);
  
  const [tempBlog, setTempBlog] = useState({
    title: "",
    content: "",
  });

  const handleEdit = (blogId: string) => {
    const blog = blogs.find(b => b.id === blogId);
    if (blog) {
      setTempBlog({
        title: blog.title,
        content: blog.content,
      });
      setEditingBlogId(blogId);
    }
  };

  const handleCancel = () => {
    setEditingBlogId(null);
  };

  const handleSave = (blogId: string) => {
    setBlogs(prev => prev.map(blog => 
      blog.id === blogId 
        ? {...blog, title: tempBlog.title, content: tempBlog.content} 
        : blog
    ));
    setEditingBlogId(null);
    toast.success("Blog updated successfully");
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempBlog(prev => ({ ...prev, title: e.target.value }));
  };

  const handleContentChange = (content: string) => {
    setTempBlog(prev => ({ ...prev, content }));
  };
  const { selectedRole } = useUserRole();

  const isTeacher =selectedRole=== UserRole.TEACHER;

  const handleGoBack = () => {
    navigate('/');
  };

  const renderBlogContent = (content: string) => {
    const truncatedContent = content.slice(0, 200) + "...";
    return truncatedContent;
  };

  return (
    <div className="min-h-screen bg-gray-50">
            <Header />
  {/* Hero Section with gradient background */}
        <section className="py-16 md:py-24 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-blue-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto relative z-10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
              <div className="max-w-2xl text-center md:text-left">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white leading-tight">
                  Transform <span className="text-yellow-300">Education</span> Through Innovation
                </h1>
                <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
                  Join thousands of educators and students in revolutionizing the learning experience
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <Button asChild size="lg" className="w-full sm:w-auto bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full shadow-lg">
                    <Link to="/register">Get Started Free</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="w-full sm:w-auto border-2 bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full">
                    <Link to="/subscription">View Pricing</Link>
                  </Button>
                </div>
                <div className="mt-12 flex flex-col sm:flex-row items-center justify-center md:justify-start gap-6">
                  <div className="flex -space-x-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="w-12 h-12 rounded-full border-4 border-purple-600 bg-white shadow-lg flex items-center justify-center text-purple-600 font-bold text-lg">
                        {String.fromCharCode(64 + i)}
                      </div>
                    ))}
                  </div>
                  <p className="text-lg text-white/90">
                    Joined by <span className="font-bold text-yellow-300">2000+</span> educators
                  </p>
                </div>
              </div>
              <div className="w-full md:w-2/5 relative">
                <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-transform duration-500">
                  <img 
                    src="https://images.unsplash.com/photo-1571260899304-425eee4c7efc?q=80&w=2070&auto=format&fit=crop" 
                    alt="EduConnect Platform" 
                    className="w-full h-64 md:h-96 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800">Modern Learning Experience</h3>
                    <p className="text-gray-600">Interactive tools for better engagement</p>
                  </div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-xl shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <p className="text-lg font-bold flex items-center gap-2">
                    <Sparkles className="h-5 w-5" />
                    AI-Powered Learning
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      <div className="container mx-auto py-4 px-4 md:py-6 md:max-w-4xl">
        {isTeacher && (
          <Button
            className="w-full md:w-auto mb-4 bg-purple-600 hover:bg-purple-700"
            onClick={() => toast.info("Create new blog functionality would be implemented here")}
          >
            Create New Blog
          </Button>
        )}
        
        <div className="space-y-4">
          {blogs.map(blog => (
            <Card key={blog.id} className="overflow-hidden">
              <div className="p-4 md:p-6">
                {editingBlogId === blog.id ? (
                  <div className="space-y-4">
                    <div className="flex flex-col md:flex-row md:items-center gap-4">
                      <input
                        type="text"
                        value={tempBlog.title}
                        onChange={handleTitleChange}
                        className="edu-form-field flex-1"
                      />
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1 md:flex-none flex items-center gap-1 text-red-600 border-red-600 hover:bg-red-50"
                          onClick={handleCancel}
                        >
                          <X className="h-4 w-4" />
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          className="flex-1 md:flex-none flex items-center gap-1 bg-green-600 hover:bg-green-700"
                          onClick={() => handleSave(blog.id)}
                        >
                          <Check className="h-4 w-4" />
                          Save
                        </Button>
                      </div>
                    </div>
                    <RichTextEditor
                      value={tempBlog.content}
                      onChange={handleContentChange}
                      placeholder="Write your blog content here..."
                    />
                  </div>
                ) : (
                  <>
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-4">
                      <h3 className="text-xl font-semibold">{blog.title}</h3>
                      {isTeacher && (
                        <Button
                          variant="outline"
                          className="w-full md:w-auto flex items-center gap-2 text-purple-600 border-purple-600 hover:bg-purple-50"
                          onClick={() => handleEdit(blog.id)}
                        >
                          <Pencil className="h-4 w-4" />
                          Edit
                        </Button>
                      )}
                    </div>
                    <div className="flex items-center text-gray-500 text-sm mb-4">
                      <span>By {blog.author} • {blog.date}</span>
                    </div>
                    <div 
                      className="text-gray-700 blog-content mb-4"
                      dangerouslySetInnerHTML={{ __html: renderBlogContent(blog.content) }}
                    />
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                      <div className="flex flex-wrap gap-2">
                        {blog.tags.map(tag => (
                          <span key={tag} className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">
                            {tag}
                          </span>
                        ))}
                      </div>
                      <Button
                        onClick={() => navigate(`/blog/${blog.id}`)}
                        className="w-full md:w-auto flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700"
                      >
                        Read Full <ArrowRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </>
                )}
              </div>
            </Card>
          ))}
        </div>
      </div>
      <footer className="bg-gray-900 text-white py-8 md:py-12">
                    <Footer/>
                  </footer>
    </div>
  );
}
