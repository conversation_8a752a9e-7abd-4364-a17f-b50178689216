import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
// auth is passed as a prop from the parent
import { useApp } from "@/context/AppContext";
import { toast } from "sonner";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { Upload, Edit, ImageIcon, BookOpen, Users, FileText, X } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ClassType } from "@/types";
import { getClassesFromBackend, ClassData  ,updateClassOnBackend,
  getClassOverViewForTeacher,createClassNotes,getClassNotes,updateClassNotes,MarkClassAsComplete
} from "@/services/classService";
import { fileUpload, downloadAndCreateImageUrl } from "@/utils/fileUpload";

import ReactCrop, { Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
const ClassDetailsTab = ({ classId, currentClass, auth }: any) => {    
    const {
        updateClass,
    } = useApp();  
    // Add state for the downloaded image URL
      const [downloadedPosterUrl, setDownloadedPosterUrl] = useState<string | null>(null);
     const [editMode, setEditMode] = useState(false);
     const fileInputRef = React.useRef<HTMLInputElement>(null);
       const [selectedImage, setSelectedImage] = useState<string | null>(null);
       const [crop, setCrop] = useState<Crop>({ unit: '%', width: 90, height: 60, x: 5, y: 20 });
       const [isCropping, setIsCropping] = useState(false);
       const imgRef = React.useRef<HTMLImageElement>(null);
        const [newPid,setNewPid] = useState();
        const navigate = useNavigate();
 
     const [posterImage, setPosterImage] = useState<string | null>(null);
      const [isEditingPoster, setIsEditingPoster] = useState(false);
     const [editedClass, setEditedClass] = useState({
        className: "",
        subjectName: "",
        classType: ClassType.REGULAR,
        batchName: "",
        level : "",
        description :"",
        capacity :1,
        posterFileId:""
    
    
      }); 
     React.useEffect(() => {
      if ( currentClass) {
        console.log("Current class:", currentClass);
        setPosterImage(currentClass.posterFileId );
    
        setEditedClass({
          className: currentClass.className || "",
    
          subjectName: currentClass.subjectName || "",
          classType: currentClass.classType as ClassType,
          batchName: currentClass.batchName || "",
          level: currentClass.level || "",
          description: currentClass.description || "",
          capacity: currentClass.capacity || 1,
          posterFileId :currentClass.posterFileId || ""
        });
    
        // Download and display existing poster if available
        if (currentClass.posterFileId && auth.user?.access_token) {
          downloadAndCreateImageUrl(auth.user.access_token, currentClass.posterFileId)
            .then(imageUrl => {
              setDownloadedPosterUrl(imageUrl);
            })
            .catch(error => {
              console.error("Failed to download poster image:", error);
            });
        }
      }
    }, [ currentClass, auth.user?.access_token]);
    
      const handleEditToggle = async () => {
        if (editMode) {
          if (currentClass) {
            try {
            await updateClassOnBackend(auth.user.access_token, currentClass.id, editedClass);
            // update global context
            updateClass(currentClass.id, { ...currentClass, ...editedClass });
            toast.success("Class details updated successfully!");
          } catch (error) {
            toast.error("Failed to update class");
          }
          }
        }
        setEditMode(!editMode);
      };
     
    
    const handlePosterUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;
    
        const reader = new FileReader();
        reader.onload = () => {
          setSelectedImage(reader.result as string);
          setIsCropping(true);
        };
        reader.readAsDataURL(file);
      };
    
      const handleSaveWithoutCrop = async () => {
        if (!selectedImage) return;
        
        try {
          const response = await fetch(selectedImage);
          const blob = await response.blob();
          const file = new File([blob], 'image.jpg', { type: 'image/jpeg' });
          
          const uploaded = await fileUpload(auth.user?.access_token, [file], {
            relatedEntityType: "class",
            fileCategory: "image"
          });
          
          const posterFileId = uploaded[0]?.id;
          if (posterFileId) {
            setNewPid(posterFileId);
            setEditedClass(prev => ({ ...prev, posterFileId }));
            setPosterImage(posterFileId);
            const imageUrl = await downloadAndCreateImageUrl(auth.user?.access_token, posterFileId);
            setDownloadedPosterUrl(imageUrl);
            toast.success("Image uploaded successfully!");
          }
          
          setIsCropping(false);
          setSelectedImage(null);
        } catch (error) {
          console.error("Image upload failed:", error);
          toast.error("Failed to upload image");
        }
      };
    
      const getCroppedImg = (image: HTMLImageElement, crop: Crop): Promise<Blob> => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        
        const scaleX = image.naturalWidth / image.width;
        const scaleY = image.naturalHeight / image.height;
        
        canvas.width = crop.width! * scaleX;
        canvas.height = crop.height! * scaleY;
        
        ctx.drawImage(
          image,
          crop.x! * scaleX,
          crop.y! * scaleY,
          crop.width! * scaleX,
          crop.height! * scaleY,
          0,
          0,
          crop.width! * scaleX,
          crop.height! * scaleY
        );
        
        return new Promise(resolve => {
          canvas.toBlob(blob => resolve(blob!), 'image/jpeg', 0.9);
        });
      };
    
      const handleCropComplete = async () => {
        if (!imgRef.current || !crop.width || !crop.height) return;
    
        try {
          const croppedBlob = await getCroppedImg(imgRef.current, crop);
          const croppedFile = new File([croppedBlob], 'cropped-image.jpg', { type: 'image/jpeg' });
          
          const uploaded = await fileUpload(auth.user?.access_token, [croppedFile], {
            relatedEntityType: "class",
            fileCategory: "image"
          });
          
          const posterFileId = uploaded[0]?.id;
          console.log("after upload ", uploaded[0]?.id)
          if (posterFileId) {
            setNewPid(posterFileId);
            
            setEditedClass(prev => ({ ...prev, posterFileId }));
            setPosterImage(posterFileId);
            console.log("edited pid",editedClass.posterFileId)
            const imageUrl = await downloadAndCreateImageUrl(auth.user?.access_token, posterFileId);
            console.log(posterFileId)
            setDownloadedPosterUrl(imageUrl);
            toast.success("Image uploaded successfully!");
          }
          
          setIsCropping(false);
          setSelectedImage(null);
        } catch (error) {
          console.error("Image upload failed:", error);
          toast.error("Failed to upload image");
        }
      };
    
    return (
         <div className="max-w-6xl mx-auto px-4 py-6">
                 <div className="bg-gray-50 -m-4 md:-m-6 p-4 md:p-6 min-h-[400px]">
              <div className="space-y-6">
                  {/* Poster Image Section */}
                <Card className="overflow-hidden mt-4">
                  <CardContent className="p-0">
                    <div className="relative group">
                      {downloadedPosterUrl ? (
                        <div className="relative">
                          <img 
                            src={downloadedPosterUrl} 
                            alt="Class poster"
                            className="w-full h-48 md:h-64 object-cover"
                          />
                          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                          <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => fileInputRef.current?.click()}
                              className="bg-white/90 hover:bg-white text-gray-800"
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="h-48 md:h-64 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center border-2 border-dashed border-blue-200 group-hover:border-blue-300 transition-colors">
                          <div className="text-center">
                            <div className="mx-auto h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                              <ImageIcon className="h-6 w-6 text-blue-600" />
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Add Class Poster</h3>
                            <p className="text-sm text-gray-500 mb-4">Upload an image to make your class more attractive</p>
                            <Button
                              onClick={() => fileInputRef.current?.click()}
                              variant="outline"
                              className="border-blue-200 text-blue-600 hover:bg-blue-50"
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              Upload Image
                            </Button>
                          </div>
                        </div>
                      )}
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handlePosterUpload}
                        className="hidden"
                      />
                    </div>
                  </CardContent>
                </Card>
                 {/* Class Information Cards */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 ">
                  {/* Basic Information */}
                  <Card className="shadow-sm">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                        <BookOpen className="h-5 w-5 mr-2 text-primary" />
                        Basic Information
                      </h3>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Class Name</label>
                          <Input 
                            type="text" 
                            value={editedClass?.className || ""} 
                            onChange={e => setEditedClass({
                  ...editedClass,
                  className: e.target.value
                })} className="edu-form-field w-full"
                            placeholder="Enter class name"
                          />
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Course Title</label>
                          <Input 
                            type="text" 
                            value={editedClass?.subjectName || ""} 
                          onChange={e => setEditedClass({
                                            ...editedClass,
                                            subjectName: e.target.value
                                          })}                             className="edu-form-field w-full"
                            placeholder="Enter course title"
                          />
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Class Type</label>
                          <Select value={editedClass.classType} onValueChange={(value: ClassType) => setEditedClass({
                        ...editedClass,
                        classType: value
                      })}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select class type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value={ClassType.ONLINE}>Online</SelectItem>
                              <SelectItem value={ClassType.OFFLINE}>Offline</SelectItem>
                              <SelectItem value={ClassType.REGULAR}>Regular</SelectItem>
                              <SelectItem value={ClassType.ADVANCED}>Advanced</SelectItem>
                              <SelectItem value={ClassType.ELECTIVE}>Elective</SelectItem>
                            </SelectContent>
                          </Select>
                    </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Class Details */}
                  <Card className="shadow-sm">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                        <Users className="h-5 w-5 mr-2 text-primary" />
                        Class Details
                      </h3>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Batch Name</label>
                          <Input 
                            type="text" 
                            value={editedClass?.batchName || ""} 
                            onChange={e => setEditedClass({
                                              ...editedClass,
                                              batchName: e.target.value
                                            })}                            className="edu-form-field w-full"
                            placeholder="Enter batch name"
                          />
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Level</label>
                          <Input 
                            type="text" 
                           value={editedClass.level}
                            onChange={e => setEditedClass({
                                              ...editedClass,
                                              level: e.target.value
                                            })}                            className="edu-form-field w-full"
                            placeholder="Enter level"
                          />
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Capacity</label>
                          <Input
                            type="number"
                            min={1}
                            value={editedClass.capacity}
                            onChange={e => setEditedClass({
                              ...editedClass,
                              capacity: Number(e.target.value)
                            })}
                            placeholder="Enter class capacity"
                            className="edu-form-field w-full"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Description */}
                <Card className="shadow-sm">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                      <FileText className="h-5 w-5 mr-2 text-primary" />
                      Description
                    </h3>
                    <div className="space-y-2">
                      <RichTextEditor
                        value={editedClass?.description || ""}
                        onChange={(value) => setEditedClass(prev => ({ ...prev, description: value }))}
                        placeholder="Enter a detailed description of your class..."
                      />
                    </div>
                  </CardContent>
                </Card>
              {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 pt-4">
                     <Button
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                      onClick={async () => {
                        try {
                          console.log(newPid)
                          console.log(editedClass.posterFileId)
                          editedClass.posterFileId = newPid;
                          
                          console.log("Update data being sent:", editedClass);
                          await updateClassOnBackend(auth.user.access_token, currentClass.id, editedClass);
                          updateClass(currentClass.id, { ...currentClass, ...editedClass });
                        
                          toast.success("Class details updated successfully!");
                          setNewPid(undefined); // Reset after successful save
                        } catch (error) {
                          const errorMessage = error?.details || error?.reason || "Failed to update class";
                          toast.error(errorMessage);
                        }
                      }}
                    >
                      Save
                    </Button>
                    <Button
                  variant="ghost"
                  onClick={() => {
                    if (currentClass) {
                          setEditedClass({
                            className: currentClass.className || "",
                            subjectName: currentClass.subjectName || "",
                            classType: currentClass.classType as ClassType,
                            batchName: currentClass.batchName || "",
                            level: currentClass.level || "",
                            description: currentClass.description || "",
                            capacity: currentClass.capacity || 1
                            , posterFileId: currentClass.posterFileId || ""
                          });
                        }
                        setEditMode(false);
                      }}
                      className="flex items-center gap-1"
                    >
                      <X className="h-4 w-4" />
                      Cancel
                    </Button>
                     <Button 
                          type="button" 
                         variant="secondary"
                          onClick={() => navigate(`/public-class/${currentClass?.id}`)}
                        >
                          View Public Page
                        </Button>
                  </div>
                                   </div>
            </div>
            


  {/* Image Crop Dialog */}
  <Dialog open={isCropping} onOpenChange={setIsCropping}>
    <DialogContent className="max-w-4xl">
      <DialogHeader>
        <DialogTitle>Crop Image</DialogTitle>
      </DialogHeader>
      <div className="flex flex-col items-center gap-4">
        {selectedImage && (
          <ReactCrop
            crop={crop}
            onChange={setCrop}
            aspect={16/9}
          >
            <img
              ref={imgRef}
              src={selectedImage}
              alt="Crop preview"
              className="max-h-96"
            />
          </ReactCrop>
        )}
        <div className="flex gap-2">
          <Button onClick={handleCropComplete} className="bg-purple-600 hover:bg-purple-700">
            Crop & Save
          </Button>
          <Button onClick={handleSaveWithoutCrop} variant="outline">
            Save Without Crop
          </Button>
          <Button onClick={() => { setIsCropping(false); setSelectedImage(null); }} variant="outline">
            Cancel
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
                </div>    
    );
};
export default ClassDetailsTab;