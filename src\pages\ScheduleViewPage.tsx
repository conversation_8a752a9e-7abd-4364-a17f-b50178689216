
import React, { useState, useEffect } from "react";
import { useAuth } from  "react-oidc-context";
import { useApp } from "@/context/AppContext";
import { Link, useNavigate } from "react-router-dom";
import { 
  Clock, 
  Calendar,
  Bell,
  User,
  ChevronRight,
  Filter,
  ArrowLeft,
  Menu
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format, startOfToday, startOfWeek, startOfMonth, endOfToday, endOfWeek, endOfMonth, isSameDay, isWithinInterval } from "date-fns";
import { ScheduleEvent } from "@/types";
import { getAllSchedules } from "@/services/scheduleService";
import { UserRole } from "@/types";
import { useSelector } from "react-redux";

import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useIsMobile } from "@/hooks/use-mobile";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
 import { generateAvatarUrl } from "@/lib/utils";
import { useUserRole } from "@/hooks/useUserRole";

// Time filter options
type TimeFilter = "today" | "week" | "month" | "all";

export default function ScheduleViewPage() {
  const auth = useAuth();
  const { classes } = useApp();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { selectedRole } = useUserRole();
// Get user from OIDC
    const user = auth.isAuthenticated ? {
      id: auth.user?.profile.sub || "",
      name: auth.user?.profile.name || "User",
      email: auth.user?.profile.email || "",
      role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
      avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
    } : null;
  const [schedules, setSchedules] = useState<ScheduleEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [timeFilter, setTimeFilter] = useState<TimeFilter>("week");
  const [classFilter, setClassFilter] = useState<string>("all");
  
  useEffect(() => {
    const fetchSchedules = async () => {
      try {
        setIsLoading(true);
        const data = await getAllSchedules(auth.user.access_token);
        setSchedules(data);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching schedules:", error);
        setIsLoading(false);
      }
    };
    
    fetchSchedules();
  }, []);
  
  // Get classes based on user role
  const userClasses = selectedRole === UserRole.TEACHER 
    ? classes.filter(c => c.teacherId === user?.id ||'')
    : selectedRole === UserRole.STUDENT 
      ? classes.filter(c => c.students.includes(user?.id || ''))
      : [];
  
  // Filter events based on time period and class
  const getFilteredEvents = () => {
    let filteredEvents = [...schedules];
    
    // Filter by class if specific class is selected
    if (classFilter !== "all") {
      filteredEvents = filteredEvents.filter(event => event.classroomId === classFilter);
    } else {
      // If "all" is selected but user has specific classes, only show those events
      filteredEvents = filteredEvents.filter(event => 
        userClasses.some(c => c.id === event.classroomId)
      );
    }
    
    // Filter by time period
    const today = startOfToday();
    
    switch (timeFilter) {
      case "today":
        filteredEvents = filteredEvents.filter(event => 
          isSameDay(new Date(event.startTime), today)
        );
        break;
      case "week":
        const weekStart = startOfWeek(new Date());
        const weekEnd = endOfWeek(new Date());
        filteredEvents = filteredEvents.filter(event => 
          isWithinInterval(new Date(event.startTime), { start: weekStart, end: weekEnd })
        );
        break;
      case "month":
        const monthStart = startOfMonth(new Date());
        const monthEnd = endOfMonth(new Date());
        filteredEvents = filteredEvents.filter(event => 
          isWithinInterval(new Date(event.startTime), { start: monthStart, end: monthEnd })
        );
        break;
      default:
        // "all" - no additional filtering needed
        break;
    }
    
    // Sort by start time
    return filteredEvents.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  };

  const filteredEvents = getFilteredEvents();
  
  const getClassDetails = (classId: string) => {
    return classes.find(c => c.id === classId);
  };
  
  const handleViewAll = () => {
    navigate('/schedule/all');
  };

  const handleBackToDashboard = () => {
    if (selectedRole === UserRole.TEACHER) {
      navigate('/teacher-dashboard');
    } else if (selectedRole=== UserRole.STUDENT) {
      navigate('/student-dashboard');
    } else if (selectedRole === UserRole.PARENT) {
      navigate('/parent-dashboard');
    } else {
      navigate('/');
    }
  };
  
  // Filter components for both desktop and mobile
  const FiltersComponent = () => (
    <div className="flex flex-col sm:flex-row gap-3 mb-4">
      <div className="w-full sm:w-auto">
        <Select value={timeFilter} onValueChange={(value) => setTimeFilter(value as TimeFilter)}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Time Period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="all">All Events</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="w-full sm:w-auto">
        <Select value={classFilter} onValueChange={setClassFilter}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select Class" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Classes</SelectItem>
            {userClasses.map(cls => (
              <SelectItem key={cls.id} value={cls.id}>
                {cls.className}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex-grow">
        <header className="bg-white p-4 flex justify-between items-center border-b border-gray-200">
          <div className="flex items-center gap-2 sm:gap-4">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={handleBackToDashboard}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-lg sm:text-xl font-semibold">My Schedule</h1>
          </div>
          
          <div className="flex items-center gap-2 sm:gap-4">
            {!isMobile && selectedRole === UserRole.TEACHER && (
              <Button className="bg-purple-600 hover:bg-purple-700 flex gap-2" onClick={() => navigate('/schedule/create')}>
                Add Event
              </Button>
            )}
            <Link to="/announcements">
              <button className="relative">
                <Bell className="h-5 w-5 sm:h-6 sm:w-6 text-gray-500" />
                <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
              </button>
            </Link>
            <button onClick={() => navigate('/profile')}>
              <User className="h-5 w-5 sm:h-6 sm:w-6 text-gray-500" />
            </button>
            {isMobile && (
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[80vw] sm:max-w-sm">
                  <div className="py-4">
                    <h3 className="text-lg font-medium mb-4">Menu</h3>
                    <nav className="space-y-2">
                      {selectedRole === UserRole.TEACHER ? (
                        <>
                          <Link to="/teacher-dashboard" className="block p-2 hover:bg-gray-100 rounded-md">Dashboard</Link>
                          <Link to="/classes" className="block p-2 hover:bg-gray-100 rounded-md">Classes</Link>
                          <Link to="/students" className="block p-2 hover:bg-gray-100 rounded-md">Students</Link>
                        </>
                      ) : (
                        <>
                          <Link to="/student-dashboard" className="block p-2 hover:bg-gray-100 rounded-md">Dashboard</Link>
                          <Link to="/student-classes" className="block p-2 hover:bg-gray-100 rounded-md">My Classes</Link>
                        </>
                      )}
                      <Link to="/announcements" className="block p-2 hover:bg-gray-100 rounded-md">Announcements</Link>
                    </nav>
                  </div>
                </SheetContent>
              </Sheet>
            )}
          </div>
        </header>

        <div className="p-4 sm:p-6">
          <div className="max-w-screen-xl mx-auto">
            {isMobile && selectedRole === UserRole.TEACHER && (
              <Button 
                className="bg-purple-600 hover:bg-purple-700 w-full mb-4 flex gap-2 items-center justify-center" 
                onClick={() => navigate('/schedule/create')}
              >
                Add New Schedule
              </Button>
            )}
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg sm:text-xl">My Schedule</CardTitle>
                <Button 
                  variant="outline" 
                  className="text-purple-600 border-purple-200 hover:bg-purple-50 flex items-center text-sm" 
                  size="sm"
                  onClick={handleViewAll}
                >
                  View All <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </CardHeader>
              <CardContent>
                {isMobile ? (
                  <Sheet>
                    <SheetTrigger asChild>
                      <Button variant="outline" className="w-full flex justify-between items-center mb-4">
                        <span>Filter Options</span>
                        <Filter className="h-4 w-4 ml-2" />
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="bottom" className="h-[40vh]">
                      <div className="py-4 space-y-4">
                        <h3 className="text-lg font-medium mb-2">Filter Schedules</h3>
                        <FiltersComponent />
                      </div>
                    </SheetContent>
                  </Sheet>
                ) : (
                  <FiltersComponent />
                )}
                
                {isLoading ? (
                  <div className="text-center py-12">
                    <p className="text-gray-500">Loading schedules...</p>
                  </div>
                ) : filteredEvents.length === 0 ? (
                  <div className="text-center py-12">
                    <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">No Scheduled Events</h3>
                    <p className="text-gray-500">
                      {timeFilter === "today" ? "You don't have any events scheduled for today." :
                       timeFilter === "week" ? "You don't have any events scheduled for this week." :
                       timeFilter === "month" ? "You don't have any events scheduled for this month." :
                       "You don't have any scheduled events."}
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredEvents.map(event => {
                      const classDetails = getClassDetails(event.classroomId);
                      const isToday = isSameDay(new Date(event.startTime), new Date());
                      
                      return (
                        <div key={event.id} className={`bg-white border ${isToday ? 'border-purple-200' : 'border-gray-200'} rounded-lg overflow-hidden`}>
                          <div className="p-4">
                            <div className="flex items-center mb-2 text-xs text-gray-500">
                              <span>{classDetails?.courseTitle || "Course"}</span>
                              <span className="mx-2">•</span>
                              <span>{classDetails?.className || "Unknown class"}</span>
                              <span className="mx-2">•</span>
                              <span>
                                <Clock className="inline h-3 w-3 mr-1" />
                                {format(event.endTime, "h:mm") === format(event.startTime, "h:mm") 
                                  ? "1h" 
                                  : `${Math.round((event.endTime.getTime() - event.startTime.getTime()) / (1000 * 60 * 60))}h`}
                              </span>
                            </div>
                            <h3 className="font-medium mb-4">{event.classroomName}</h3>
                            <div className="flex items-center text-xs text-gray-500 mb-3">
                              <Calendar className="h-3 w-3 mr-1" />
                              <span>{format(event.startTime, "MMM d")}</span>
                              <span className="mx-1">•</span>
                              <span>{format(event.startTime, "h:mm a")} - {format(event.endTime, "h:mm a")}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              {event.recurring && (
                                <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                                  {event.recurrenceType || "Recurring"}
                                </Badge>
                              )}
                              <div className="flex-grow"></div>
                              <Button 
                                size="sm" 
                                className={`${isToday ? 'bg-purple-600 hover:bg-purple-700' : 'bg-white text-purple-600 border border-purple-200 hover:bg-purple-50'} text-xs py-1`}
                                onClick={() => navigate(`/classroom/${event.classroomId}`)}
                              >
                                {isToday ? "Join Now" : "View Class"}
                              </Button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
