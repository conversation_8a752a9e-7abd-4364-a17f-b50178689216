import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { ChevronRight } from "lucide-react";
import { ClassCard } from "./ClassCard";
import { useIsMobile } from "@/hooks/use-mobile";
import { ScheduleEvent } from "@/types";
import {getAllSchedules} from "@/services/scheduleService";
import { toast } from "sonner";
import { useAuth } from "react-oidc-context"; 
import { generateAvatarUrl } from "@/lib/utils";
import { convertUTCToLocalTime } from "@/utils/convertFromUTC";
import { UserRole } from "@/types";
import class1Img from "@/assets/image1.jpg";
import { getClassesFromBackend ,ClassData} from "@/services/classService";

import { useUserRole } from "@/hooks/useUserRole";
import { time } from "console";

export const ScheduleSection: React.FC = () => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();
   const [schedule, setSchedules] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const hasFetchedSchedules = useRef(false);
    const hasFetchedClasses = useRef(false);
  const auth = useAuth(); // Use OIDC auth
          const [availableClasses, setAvailableClasses] = useState<ClassData[]>([]);
     const { selectedRole, userTimezone } = useUserRole();  
      const timezone = userTimezone || 'UTC';
    
      // Get user from OIDC
      const user = auth.isAuthenticated ? {
        id: auth.user?.profile.sub || "",
        name: auth.user?.profile.name || "User",
        email: auth.user?.profile.email || "",
        role: (auth.user?.profile["custom:role"] as UserRole) ,
        avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
      } : null;
    
    useEffect(() => {
      const fetchSchedules = async () => {
        try {
          setIsLoading(true);
          const schedules = await getAllSchedules(auth.user.access_token);        
            setSchedules(schedules);
          setIsLoading(false);
        } catch (error) {
          toast.error("Failed to load schedules");
          setIsLoading(false);
        }
      };
  
      if (auth.user?.access_token && !hasFetchedSchedules.current) {
        hasFetchedSchedules.current = true;
        fetchSchedules();
      }
    }, [auth.user?.access_token]);
  
    return (
    <div className="col-span-2">
      <div className="mb-4">
        <h2 className="text-lg sm:text-xl font-semibold">My Schedule ({schedule.length})</h2>
      </div>
      
         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        
  {schedule.length === 0 && !isLoading && (
    <div className="col-span-full text-center text-gray-500">No schedules found.</div>
  )}
  {schedule.map((item) => (
    <ClassCard
      key={item.id}
      image={item.imageUrl || class1Img}
      subject={"Meeting Link"}
      grade={item.meetingLink || "Unknown"}
      duration={item.duration || ""}
      endTime=  {convertUTCToLocalTime(item.startDate,item.sessionEndTime,timezone).time}
      title= {item.classroomName || "Subject"}
        timezone={timezone}                  
      date={item.startDate ? convertUTCToLocalTime(item.startDate ,item.sessionStartTime,timezone).date : convertUTCToLocalTime(item.startDate,item.sessionStartTime ,timezone).date }
      time={
        item.sessionStartTime && item.sessionEndTime
          ? `${convertUTCToLocalTime(item.startDate,item.sessionStartTime, timezone).time} - ${convertUTCToLocalTime(item.startDate,item.sessionEndTime,timezone).time}`
          : ""
      }
      studentCount={ schedule.length }
      isNow={new Date(item.sessionStartTime) <= new Date() && new Date(item.sessionEndTime) >= new Date()}
      onClick={() =>{
        
          if(selectedRole === UserRole.TEACHER) {
            navigate(`/class/${item.classroomId}`)
          } 
          else { 

            navigate(`/student-classes`);
          }
      } }
    />
  ))}
</div>
      
      <div className="flex justify-end mt-4">
        <Link to="/schedule/all" className="text-sm flex items-center text-gray-500 hover:text-gray-700">
          View All <ChevronRight className="h-4 w-4" />
        </Link>
      </div>
    </div>
  );
};
