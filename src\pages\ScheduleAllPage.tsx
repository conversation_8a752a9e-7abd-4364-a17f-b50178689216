
import React, { useState, useEffect, useRef } from "react";
import { useAuth } from  "react-oidc-context";
import { useApp } from "@/context/AppContext";
import { Link, useNavigate } from "react-router-dom";
import {
  Clock,
  Search,
  Filter,
  ChevronLeft,
  Grid,
  List,
  Bell,
  User,
  CalendarIcon,
  Menu,
  X,
  FilterX
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { format, isAfter, isBefore, isSameDay, startOfDay, endOfDay, addDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns";
import { ScheduleEvent } from "@/types";
import { getAllSchedules, getAllSchedulesofStudent } from "@/services/scheduleService";
import { useIsMobile } from "@/hooks/use-mobile";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { UserRole } from "@/types";
import { useSelector } from "react-redux";
import { useUserRole } from "@/hooks/useUserRole";
import { getClassesFromBackend, ClassData } from "@/services/classService";
import { useDispatch } from "react-redux";
import { clearUserData } from '@/redux/userSlice';

import { generateAvatarUrl } from "@/lib/utils";
export default function ScheduleAllPage() {
const auth = useAuth();
 
     const dispatch = useDispatch();
   
 // Get user from OIDC
   const user = auth.isAuthenticated ? {
     id: auth.user?.profile.sub || "",
     name: auth.user?.profile.name || "User",
     email: auth.user?.profile.email || "",
     role: (auth.user?.profile["custom:role"] as UserRole) ,
     avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
   } : null;
    const { classes } = useApp();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
      const [availableClasses, setAvailableClasses] = useState<ClassData[]>([]);

  const [schedules, setSchedules] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<"grid" | "list">(isMobile ? "grid" : "list");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedClass, setSelectedClass] = useState<string>("all");
  const dateFilterOptions = new Map([["all", "All Dates"], ["today", "Today"], ["week", "Week"], ["month", "Month"]]);
  const { selectedRole } = useUserRole();

  const [dateFilter, setDateFilter] = useState<string>("all");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const hasInitialized = useRef(false);

  const fetchInitialData = async () => {
    if (!auth.user?.access_token || hasInitialized.current) return;
    
    hasInitialized.current = true;
    try {
      setIsLoading(true);
      const scheduleService = selectedRole === UserRole.TEACHER 
        ? getAllSchedules(auth.user.access_token)
        : getAllSchedulesofStudent(auth.user.access_token);
      
      const [schedulesData, classesData] = await Promise.all([
        scheduleService,
        getClassesFromBackend(auth.user.access_token)
      ]);
      
      setSchedules(schedulesData);
      setAvailableClasses(classesData.content || []);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (isMobile && viewMode === "list") {
      setViewMode("grid");
    }
  }, [isMobile]);

  // Get classes based on user role
  const userClasses = selectedRole === UserRole.TEACHER
    ? classes.filter(c => c.teacherId === auth.user?.profile.sub)
    : selectedRole === UserRole.STUDENT
      ? classes.filter(c => c.students.includes(auth.user?.profile.sub || ''))
      : [];
  const resetDateFilters = () => {
    setDateFilter("all");
    setSelectedDate(undefined);
  };
const handleSwitchRoleClick=()=>{
    navigate('/role-selection');
  };
  const filteredSchedules = schedules.filter(schedule => {
    // Text search filter
    const matchesSearch = schedule.classroomId.includes(searchQuery.toLowerCase()) ||
      schedule.description.toLowerCase().includes(searchQuery.toLowerCase());

    // Class filter
    const matchesClass = selectedClass === "all"
      ? userClasses.some(c => c.id === schedule.classroomId)
      : schedule.classroomId === selectedClass;

    // Date filter
    let matchesDate = true;
    const today = new Date();

    if (dateFilter === "today") {
      matchesDate = isSameDay(schedule.sessionStartTime, today);
    } else if (dateFilter === "week") {
      const weekStart = startOfWeek(today);
      const weekEnd = endOfWeek(today);
      matchesDate = isAfter(schedule.sessionStartTime, weekStart) && isBefore(schedule.sessionStartTime, weekEnd);
    } else if (dateFilter === "month") {
      const monthStart = startOfMonth(today);
      const monthEnd = endOfMonth(today);
      matchesDate = isAfter(schedule.sessionStartTime, monthStart) && isBefore(schedule.sessionStartTime, monthEnd);
    } else if (dateFilter === "custom" && selectedDate) {
      const dayStart = startOfDay(selectedDate);
      const dayEnd = endOfDay(selectedDate);
      matchesDate = isAfter(schedule.sessionStartTime, dayStart) && isBefore(schedule.sessionStartTime, dayEnd);
    }

    return matchesSearch && matchesClass && matchesDate;
  });

  const sortedSchedules = [...filteredSchedules].sort((a, b) => {
    return a.sessionStartTime - b.sessionStartTime;
  });

  const getClassDetails = (classId: string) => {
    
    return availableClasses.find(c => c.id === classId);
  };

  const handleBack = () => {
    if (selectedRole === UserRole.TEACHER) {
      navigate('/teacher-dashboard');
    } else if (selectedRole === UserRole.STUDENT) {
      navigate('/student-dashboard');
    } else {
      navigate('/');
    }
  };

  const handleLogout = async () => {
              await auth.removeUser();
              dispatch(clearUserData()); // Clears user data from Redux
              window.location.href = window.location.origin + "/logout";
            };
     const handleProfileClick = () => {
      navigate('/profile');
    };
    const handleMembershipClick=()=>{
    navigate('/membershipPage');
  };  
  const DateFilterComponent = () => (

    <div className=" flex items-center gap-2">
      <Select  
          value={dateFilter}
          onValueChange={ setDateFilter}
        >
        <SelectTrigger className="edu-form-field">
          <SelectValue placeholder="Filter by Date" />
        </SelectTrigger>
        <SelectContent>
         {Array.from(dateFilterOptions.keys()).map((key) => (
            <SelectItem key={key} value={key}>
              {dateFilterOptions.get(key)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <div className="flex items-center gap-2">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={dateFilter === "custom" ? "default" : "outline"}
              size="sm"
              className={cn(
                "justify-start text-left",
                dateFilter === "custom" && !selectedDate && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {dateFilter === "custom" && selectedDate ? (
                format(selectedDate, "PPP")
              ) : (
                "Pick a date"
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={(date) => {
                setSelectedDate(date);
                setDateFilter("custom");
              }}
              initialFocus
            />
          </PopoverContent>
        </Popover>

        {(dateFilter !== "all" || selectedDate) && (
          <Button
            variant="outline"
            size="sm"
            onClick={resetDateFilters}
            className="gap-1"
          >
            <FilterX className="h-4 w-4" />
            Reset
          </Button>
        )}
      </div>
    </div>



  );

  const FilterComponent = () => (
    <div className="w-full">
      <Select
        value={selectedClass}
        onValueChange={setSelectedClass}
      >
        <SelectTrigger className="edu-form-field">
          <SelectValue placeholder="Filter by class" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Classes</SelectItem>
          {userClasses.map(cls => (
            <SelectItem key={cls.id} value={cls.id}>
              {cls.className}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white p-4 sm:p-6 flex justify-between items-center border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={handleBack} className="mr-0 sm:mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl sm:text-2xl font-semibold">All Schedules</h1>
        </div>
        <div className="flex items-center gap-2 sm:gap-4">

          <Link to="/announcements">
            <button className="relative">
              <Bell className="h-5 w-5 sm:h-6 sm:w-6 text-gray-500" />
              <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
            </button>
          </Link>
             <DropdownMenu>
              <DropdownMenuTrigger asChild>
              <button >
                <User className="h-6 w-6 text-gray-500" />
              </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{user?.name}</p>
                    <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
                    <p className="text-xs leading-none text-muted-foreground capitalize">{selectedRole}</p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleProfileClick}>
                  Profile
                </DropdownMenuItem>
                 {!isMobile && selectedRole === UserRole.TEACHER && (
                  <DropdownMenuItem onClick={handleMembershipClick}>
                    Membership
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={handleSwitchRoleClick}>
                  Switch Role
                </DropdownMenuItem>
                
                <DropdownMenuItem asChild>
                  <Link to="/blogs">Blogs</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/profile">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>Log out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          
          {isMobile && (
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[80vw] sm:max-w-sm">
                <div className="py-4">
                  <h3 className="text-lg font-medium mb-4">Menu</h3>
                  <nav className="space-y-2">
                    {selectedRole === UserRole.TEACHER ? (
                      <>
                        <Link to="/teacher-dashboard" className="block p-2 hover:bg-gray-100 rounded-md">Dashboard</Link>
                        <Link to="/classes" className="block p-2 hover:bg-gray-100 rounded-md">Classes</Link>
                        <Link to="/students" className="block p-2 hover:bg-gray-100 rounded-md">Students</Link>
                      </>
                    ) : (
                      <>
                        <Link to="/student-dashboard" className="block p-2 hover:bg-gray-100 rounded-md">Dashboard</Link>
                        <Link to="/student-classes" className="block p-2 hover:bg-gray-100 rounded-md">My Classes</Link>
                      </>
                    )}
                    <Link to="/announcements" className="block p-2 hover:bg-gray-100 rounded-md">Announcements</Link>
                  </nav>
                </div>
              </SheetContent>
            </Sheet>
          )}
        </div>
      </header>

      <div className="p-4 sm:p-6">
        <div className="max-w-screen-xl mx-auto">
          {isMobile && selectedRole=== UserRole.TEACHER && (
            <Button
              className="bg-purple-600 hover:bg-purple-700 w-full mb-4 flex gap-2 items-center justify-center"
              onClick={() => navigate('/schedule/create')}
            >
              Add New Schedule
            </Button>
          )}

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg sm:text-xl">All Schedules</CardTitle>
              {!isMobile && (
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setViewMode("list")}
                    className={viewMode === "list" ? "bg-gray-100" : ""}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setViewMode("grid")}
                    className={viewMode === "grid" ? "bg-gray-100" : ""}
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row sm:flex-wrap gap-4 mb-6">
                <div className="w-full sm:flex-1 min-w-[200px]">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search schedules..."
                      className="edu-form-field pl-10"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
                {!isMobile ? (
                  <div className="w-full flex flex-col sm:flex-row gap-4">
                    <div className="sm:w-64">
                      <FilterComponent />
                    </div>
                    <div className="sm:w-64">
                      <DateFilterComponent />
                    </div>
                  </div>
                ) : (
                  <Sheet>
                    <SheetTrigger asChild>
                      <Button variant="outline" className="w-full flex justify-between items-center">
                        <span>Filter Options</span>
                        <Filter className="h-4 w-4 ml-2" />
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="bottom" className="h-[60vh]">
                      <div className="py-4 space-y-6">
                        <h3 className="text-lg font-medium mb-2">Filter Schedules</h3>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium mb-2">By Class</h4>
                            <FilterComponent />
                          </div>
                          <div>
                            <h4 className="text-sm font-medium mb-2">By Date</h4>
                            <DateFilterComponent />
                          </div>
                        </div>
                      </div>
                    </SheetContent>
                  </Sheet>
                )}
              </div>

              {isLoading ? (

                <div className="text-center py-12">
                  <p className="text-gray-500">Loading schedules...</p>
                </div>
              ) : schedules.length === 0 ? (
                <div className="text-center py-12">
                  <CalendarIcon className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                  <h3 className="text-lg font-medium text-gray-600 mb-2">No Schedules Found</h3>
                  <p className="text-gray-500">
                    There are no scheduled events matching your criteria.
                  </p>
                </div>
              ) : viewMode === "list" && !isMobile ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Class</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Time</TableHead>
                        <TableHead>Recurring</TableHead>
                        <TableHead>Action</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {schedules.map(schedule => {
                        return (
                          <TableRow key={schedule.id}>
                            <TableCell className="font-medium">{schedule.classroomName}</TableCell>
                            <TableCell>
                             {availableClasses.find(c => c.id === schedule.classroomId)?.subjectName || 
                            <span className="text-gray-400">Unknown class</span>}
                            </TableCell>
                            <TableCell>{schedule.sessionStartTime}</TableCell>
                            <TableCell>{schedule.sessionStartTime} - {schedule.sessionEndTime}</TableCell>
                            <TableCell>
                              {schedule.recurring ? (
                                <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                                  {schedule.recurrenceType || "Yes"}
                                </Badge>
                              ) : (
                                <Badge variant="outline">No</Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="link"
                                className="p-0 h-auto text-purple-600"
                                onClick={() => navigate(`/classroom/${schedule.classroomId}`)}
                              >
                                Join
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {schedules.map(schedule => {
                    return (
                      <div key={schedule.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div className="p-4">
                          <div className="flex items-center mb-2 text-xs text-gray-500">
                            <span>{availableClasses.find(c => c.id === schedule.classroomId)?.subjectName || "Course"}</span>
                            <span className="mx-2">•</span>
                            {availableClasses.find(c => c.id === schedule.classroomId)?.subjectName || 
                            <span className="text-gray-400">Unknown class</span>}
                            <span className="mx-2">•</span>
                            <span><Clock className="inline h-3 w-3 mr-1" />{schedule.sessionEndTime === schedule.sessionStartTime ? "1h" : `${Math.round((schedule.sessionEndTime - schedule.sessionStartTime) / (1000 * 60 * 60))}h`}</span>
                          </div>
                          <h3 className="font-medium mb-4">{schedule.classroomName}</h3>
                          <div className="flex items-center text-xs text-gray-500 mb-3">
                            <CalendarIcon className="h-3 w-3 mr-1" />
                            <span>{schedule.sessionStartTime}</span>
                            <span className="mx-1">•</span>
                            <span>{schedule.sessionStartTime} - {schedule.sessionEndTime}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            {schedule.recurring && (
                              <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                                {schedule.recurrenceType || "Recurring"}
                              </Badge>
                            )}
                            <div className="flex-grow"></div>
                            <Button
                              size="sm"
                              className="bg-purple-600 hover:bg-purple-700 text-xs py-1"
                              onClick={() => navigate(`/classroom/${schedule.classroomId}`)}
                            >
                              Join Now
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
