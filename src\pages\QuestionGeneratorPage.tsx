
import React, { useState } from "react";
import { Head<PERSON> } from "@/components/layout/Header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Send, Settings2 } from "lucide-react";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import LLMSettings from "@/components/llm/LLMSettings";
import { LLMConfig } from "@/utils/llmService";

const QuestionGeneratorPage = () => {
  const [prompt, setPrompt] = useState("");
  const [output, setOutput] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [llmConfig, setLLMConfig] = useState<LLMConfig>({
    provider: 'openai',
    temperature: 0.7,
    maxTokens: 1000
  });

  const handleGenerateQuestions = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt first");
      return;
    }

    setIsGenerating(true);
    
    try {
      // Mock response for now - this would be replaced with actual LLM API call
      setTimeout(() => {
        const mockQuestions = generateMockQuestions(prompt);
        setOutput(mockQuestions);
        setIsGenerating(false);
        toast.success("Questions generated successfully");
      }, 1500);
    } catch (error) {
      console.error("Error generating questions:", error);
      toast.error("Failed to generate questions");
      setIsGenerating(false);
    }
  };
  
  // This function simulates an LLM response
  const generateMockQuestions = (prompt: string) => {
    const topics = prompt.split(" ").filter(word => word.length > 4);
    let questions = `<h3>Questions based on your prompt:</h3><ol>`;
    
    for (let i = 0; i < Math.min(5, Math.max(3, topics.length)); i++) {
      questions += `<li>What are the key concepts related to ${topics[i % topics.length]}?</li>`;
      questions += `<li>How does ${topics[(i+1) % topics.length]} relate to ${topics[i % topics.length]}?</li>`;
    }
    
    questions += `</ol><p>Note: These are mock questions. In a production environment, these would be generated by an actual LLM API.</p>`;
    return questions;
  };

  const handleSaveConfig = (config: LLMConfig) => {
    setLLMConfig(config);
    // In a real application, you might store this in localStorage or a database
   // localStorage.setItem('llmConfig', JSON.stringify(config));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6">Question Generator</h1>
        
        <Tabs defaultValue="generator" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="generator">Generate Questions</TabsTrigger>
            <TabsTrigger value="settings">LLM Settings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="generator">
            <Card>
              <CardHeader>
                <CardTitle>Question Generator</CardTitle>
                <CardDescription>
                  Enter a topic or text prompt and generate relevant questions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Enter your prompt:
                  </label>
                  <Textarea
                    placeholder="E.g., 'Generate questions about photosynthesis for a high school biology class'"
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
                
                <Button 
                  onClick={handleGenerateQuestions}
                  disabled={isGenerating || !prompt.trim()}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Generate Questions
                    </>
                  )}
                </Button>
                
                {!llmConfig.apiKey && (
                  <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded-md flex items-center">
                    <Settings2 className="h-4 w-4 mr-2" />
                    <span>
                      No LLM API key configured. Switch to the Settings tab to configure your LLM provider.
                      Using mock data for now.
                    </span>
                  </div>
                )}
                
                {output && (
                  <div className="mt-6">
                    <h3 className="text-lg font-medium mb-2">Generated Questions:</h3>
                    <div className="p-4 bg-gray-50 border rounded-md">
                      <div dangerouslySetInnerHTML={{ __html: output }} />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="settings">
            <LLMSettings 
              onSave={handleSaveConfig}
              initialConfig={llmConfig}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default QuestionGeneratorPage;
