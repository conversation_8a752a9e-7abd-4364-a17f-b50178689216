
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";
import { Class, ClassType } from "@/types";
import { useAuth } from "react-oidc-context"; // Updated import
import { UserRole } from "@/types";
import { 
  Share2, 
  Mail, 
  Copy, 
  Smartphone 
} from "lucide-react";
import { useState } from "react";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { useApp } from "@/context/AppContext";
import { toast } from "sonner";
import { generateAvatarUrl } from "@/lib/utils";
import { useUserRole } from "@/hooks/useUserRole";

interface ClassCardProps {
  classData: Class;
}

export function ClassCard({ classData }: ClassCardProps) {
  const auth = useAuth(); // Use OIDC auth
  const { shareClass } = useApp();
  
  // Get user from OIDC
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;
   const { selectedRole } = useUserRole();  

  const isTeacher = selectedRole === UserRole.TEACHER;
  const isStudentOrParent = selectedRole === UserRole.STUDENT || selectedRole === UserRole.PARENT;
  
  const getBadgeColor = (type: ClassType) => {
    switch (type) {
      case ClassType.REGULAR:
        return "bg-blue-100 text-blue-800";
      case ClassType.ADVANCED:
        return "bg-purple-100 text-purple-800";
      case ClassType.ELECTIVE:
        return "bg-green-100 text-green-800";
      case ClassType.ONLINE:
        return "bg-cyan-100 text-cyan-800";
      case ClassType.OFFLINE:
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleShare = (method: "email" | "whatsapp" | "copy" | "other") => {
    shareClass(classData.id, method);
  };

  const handleCopyJoinCode = () => {
    navigator.clipboard.writeText(classData.joinCode);
    toast("Join code copied to clipboard!");
  };

  return (
    <Card className="edu-card h-full flex flex-col">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl font-bold">{classData.className}</CardTitle>
            <CardDescription className="mt-1">{classData.courseTitle}</CardDescription>
          </div>
          <Badge className={getBadgeColor(classData.classType)}>
            {classData.classType}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        {isTeacher && (
          <div className="space-y-2 mb-4">
            <div className="flex justify-between items-center text-sm">
              <span className="font-medium">Join Code:</span>
              <div className="flex items-center gap-1">
                <span className="font-mono bg-gray-100 px-2 py-1 rounded">{classData.joinCode}</span>
                <Button 
                  variant="ghost" 
                  size="icon"
                  className="h-6 w-6" 
                  onClick={handleCopyJoinCode}
                  title="Copy join code"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="font-medium">Students:</span>
              <span>{classData.students.length}</span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="font-medium">Parents:</span>
              <span>{classData.parents.length}</span>
            </div>
            
            <div className="flex justify-end mt-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="flex items-center gap-1 text-xs"
                  >
                    <Share2 className="h-3 w-3" /> Share Class
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleShare("email")}>
                    <Mail className="h-4 w-4 mr-2" />
                    <span>Email</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleShare("whatsapp")}>
                    <Smartphone className="h-4 w-4 mr-2" />
                    <span>WhatsApp</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleShare("copy")}>
                    <Copy className="h-4 w-4 mr-2" />
                    <span>Copy link</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        )}
        
        {isStudentOrParent && (
          <div className="space-y-2 mb-4">
            <div className="text-sm mb-2">
              <span className="font-medium">Created:</span>{" "}
              <span>{classData.createdAt.toLocaleDateString()}</span>
            </div>
          </div>
        )}
        
        {/* Display class image if available */}
        {classData.image && (
          <div className="mt-2 mb-3">
            <img 
              src={classData.image} 
              alt={classData.className}
              className="w-full h-32 object-cover rounded-md"
            />
          </div>
        )}
        
        {/* Display fee information */}
        {classData.feeStructures && classData.feeStructures.length > 0 && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <p className="text-sm font-medium mb-1">Fee: </p>
            {classData.feeStructures.slice(0, 1).map(fee => (
              <div className="text-sm" key={fee.id}>
                <span>{fee.country}: ${fee.feeAmount}</span>
                {fee.discountPercentage > 0 && (
                  <span className="text-green-600 ml-2">({fee.discountPercentage}% off)</span>
                )}
              </div>
            ))}
            {classData.feeStructures.length > 1 && (
              <span className="text-xs text-gray-500">+{classData.feeStructures.length - 1} more fee structures</span>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" asChild>
          <Link to={`/class/${classData.id}/announcements`}>
            Announcements
          </Link>
        </Button>
        <Button className="edu-btn-primary" asChild>
          <Link to={`/class/${classData.id}`}>
            {isTeacher ? "Manage" : "View"}
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

export default ClassCard;
