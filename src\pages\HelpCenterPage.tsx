import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, BookOpen, Users, Calendar, CreditCard, Settings, MessageCircle,
  ChevronRight,ArrowLeft,CheckCircle2,Sparkles } from 'lucide-react';
import Header from "@/components/layout/Header";
import Footer from "@/components/Footer";
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetDescription } from '@/components/ui/sheet';

import { Link, useNavigate } from "react-router-dom";
import { sendContactMessage } from "@/services/contactService";
import { toast } from "sonner";
import { useAuth } from "react-oidc-context";
interface Article {
  id: string;
  title: string;
  content: string;
  steps?: string[];
  tips?: string[];
}

interface HelpCategory {
  title: string;
  icon: React.ElementType;
  articles: Article[];
  color: string;
}

const helpArticles: Record<string, Article> = {
  'create-class': {
    id: 'create-class',
    title: 'How to create your first class',
    content: 'Creating a class is the first step to organizing your teaching. A class acts as a container for your students, schedules, assignments, and announcements.',
    steps: [
      'Navigate to "Classes" from the sidebar menu',
      'Click the "Create Class" button in the top right',
      'Fill in the class details: name, subject, description, and type (online/offline/hybrid)',
      'Set the class schedule and fee structure if applicable',
      'Click "Create" to finish setting up your class',
      'Share the unique join code with your students so they can enroll'
    ],
    tips: [
      'Choose a descriptive name that helps students identify the class easily',
      'Add a detailed description to set expectations for the course',
      'You can always edit class details later from the class settings'
    ]
  },
  'setup-profile': {
    id: 'setup-profile',
    title: 'Setting up your profile',
    content: 'Your profile is your identity on the platform. A complete profile helps students and parents trust you as an educator.',
    steps: [
      'Click on your avatar in the top right corner',
      'Select "Profile" from the dropdown menu',
      'Upload a professional profile picture',
      'Fill in your name, bio, and contact information',
      'Add your qualifications and teaching experience',
      'Save your changes'
    ],
    tips: [
      'Use a clear, professional photo for your profile picture',
      'Write a bio that highlights your teaching expertise and style',
      'Keep your contact information up to date for parent communication'
    ]
  },
  'user-roles': {
    id: 'user-roles',
    title: 'Understanding user roles',
    content: 'EduConnect supports three user roles: Teacher, Student, and Parent. Each role has different permissions and access to features.',
    steps: [
      'Teacher: Create classes, manage students, take attendance, create assignments, and track payments',
      'Student: Join classes, view schedules, submit assignments, and track progress',
      'Parent: View child\'s classes, attendance, assignments, and communicate with teachers'
    ],
    tips: [
      'You can switch between roles if you have multiple accounts',
      'Teachers have full control over their classes',
      'Parents can link to multiple children\'s accounts'
    ]
  },
  'platform-overview': {
    id: 'platform-overview',
    title: 'Platform overview and navigation',
    content: 'EduConnect is designed to make teaching and learning management simple. The platform is organized into several key areas.',
    steps: [
      'Dashboard: Your home page with quick stats and recent activity',
      'Classes: Manage all your classes, students, and class-specific settings',
      'Schedule: View and manage your teaching schedule across all classes',
      'Assignments: Create, distribute, and grade student assignments',
      'Attendance: Track student attendance for each class session',
      'Payments: Manage fee structures and track payment status'
    ],
    tips: [
      'Use the sidebar navigation to quickly access any section',
      'The dashboard shows your most important information at a glance',
      'Check announcements regularly for important updates'
    ]
  },
  'add-students': {
    id: 'add-students',
    title: 'Adding students to your class',
    content: 'Students can join your class using a unique join code, or you can add them manually. Each class has its own join code.',
    steps: [
      'Go to your class detail page',
      'Find the "Join Code" displayed on the class card',
      'Share this code with students via email, WhatsApp, or any messaging platform',
      'Students enter the code on the "Join Class" page to enroll',
      'Alternatively, click "Add Student" to manually add students by entering their details'
    ],
    tips: [
      'You can regenerate the join code if needed for security',
      'Review pending join requests before approving',
      'Students will see the class in their dashboard once approved'
    ]
  },
  'manage-schedules': {
    id: 'manage-schedules',
    title: 'Managing class schedules',
    content: 'Set up regular class schedules so students know when to attend. You can create one-time or recurring schedules.',
    steps: [
      'Navigate to the "Schedule" section from the sidebar',
      'Click "Add Schedule" to create a new class time',
      'Select the class, day(s) of the week, and time slot',
      'Add a description or notes for the session',
      'Save the schedule - it will appear on student calendars automatically'
    ],
    tips: [
      'Use recurring schedules for regular weekly classes',
      'Add meeting links for online classes',
      'Students and parents receive notifications for schedule changes'
    ]
  },
  'create-assignments': {
    id: 'create-assignments',
    title: 'Creating and grading assignments',
    content: 'Assignments help assess student learning. Create various types of assignments and track submissions easily.',
    steps: [
      'Go to "Assignments" from the sidebar',
      'Click "Create Assignment" button',
      'Select the class and fill in assignment details (title, description, due date)',
      'Add questions or upload attachments',
      'Set the maximum marks and submission deadline',
      'Publish the assignment - students will be notified',
      'Review submissions and grade them from the assignment detail page'
    ],
    tips: [
      'Set clear instructions and expectations in the description',
      'Use the AI question generator for quick question creation',
      'Provide feedback along with grades to help students improve'
    ]
  },
  'class-announcements': {
    id: 'class-announcements',
    title: 'Setting up class announcements',
    content: 'Announcements are a great way to communicate important information to all students and parents in a class.',
    steps: [
      'Navigate to "Announcements" from the sidebar or class page',
      'Click "Create Announcement"',
      'Select which class(es) should receive the announcement',
      'Write your announcement with a clear title and message',
      'Choose whether to notify via email/push notifications',
      'Publish the announcement'
    ],
    tips: [
      'Use announcements for schedule changes, exam dates, and important updates',
      'Keep announcements concise and actionable',
      'Pin important announcements so they stay at the top'
    ]
  },
  'create-schedules': {
    id: 'create-schedules',
    title: 'Creating class schedules',
    content: 'A well-organized schedule helps students plan their learning. Create schedules that work for both you and your students.',
    steps: [
      'Access the Schedule section from the main navigation',
      'Click on "Add New Schedule"',
      'Choose the class for which you\'re creating the schedule',
      'Select the date and time for the class',
      'Add the topic or lesson plan for that session',
      'Include any joining links for online classes',
      'Save and publish the schedule'
    ],
    tips: [
      'Plan your schedule at least a week in advance',
      'Include buffer time between consecutive classes',
      'Update schedules promptly if there are any changes'
    ]
  },
  'take-attendance': {
    id: 'take-attendance',
    title: 'Taking attendance',
    content: 'Track student attendance for each class session. Attendance records help monitor student engagement and are visible to parents.',
    steps: [
      'Go to "Attendance" from the sidebar',
      'Select the class and date for which you want to record attendance',
      'You\'ll see a list of all enrolled students',
      'Mark each student as Present, Absent, or Late',
      'Add any notes if needed (e.g., reason for absence)',
      'Save the attendance record'
    ],
    tips: [
      'Take attendance at the beginning of each class',
      'Parents are notified when their child is marked absent',
      'View attendance reports to identify patterns'
    ]
  },
  'leave-requests': {
    id: 'leave-requests',
    title: 'Managing leave requests',
    content: 'Students can request leave in advance. Teachers can approve or reject these requests and track them easily.',
    steps: [
      'Students submit leave requests from their dashboard',
      'Teachers receive notifications for new leave requests',
      'Go to the Attendance section to view pending requests',
      'Review the reason and dates for the leave',
      'Approve or reject the request with optional comments',
      'The student is notified of your decision'
    ],
    tips: [
      'Respond to leave requests promptly',
      'Set clear policies about acceptable leave reasons',
      'Approved leaves are automatically marked in attendance'
    ]
  },
  'recurring-sessions': {
    id: 'recurring-sessions',
    title: 'Setting up recurring sessions',
    content: 'For regular classes, set up recurring sessions to automatically populate your schedule without manual entry each week.',
    steps: [
      'Go to Meetings or Schedule section',
      'Click "Schedule Meeting" or "Add Schedule"',
      'Enable the "Recurring" option',
      'Select the frequency (daily, weekly, monthly)',
      'Choose which days of the week for weekly recurrence',
      'Set the start and end dates for the recurrence',
      'Save - all sessions will be created automatically'
    ],
    tips: [
      'Review recurring schedules periodically for accuracy',
      'You can edit individual sessions without affecting the series',
      'Cancel individual sessions for holidays without deleting the series'
    ]
  },
  'fee-structure': {
    id: 'fee-structure',
    title: 'Setting up fee structure',
    content: 'Define how much students pay for your classes. You can set different fee structures for different classes or payment plans.',
    steps: [
      'Navigate to "Payments" from the sidebar',
      'Go to "Fee Structure" tab',
      'Click "Add Fee Structure"',
      'Select the class and define the fee amount',
      'Choose the payment frequency (monthly, quarterly, one-time)',
      'Add any discounts or late fee policies',
      'Save the fee structure'
    ],
    tips: [
      'Clearly communicate fee structures to parents upfront',
      'Consider offering early payment discounts',
      'Set up payment reminders for due dates'
    ]
  },
  'process-payments': {
    id: 'process-payments',
    title: 'Processing student payments',
    content: 'Track and record payments from students. Mark payments as received and maintain accurate financial records.',
    steps: [
      'Go to the "Payments" section',
      'View the list of pending payments',
      'When a student makes a payment, click on their entry',
      'Enter the payment details (amount, date, method)',
      'Mark the payment as "Paid"',
      'A receipt is automatically generated for the student'
    ],
    tips: [
      'Record payments promptly to keep records accurate',
      'Use the payment history to track outstanding dues',
      'Send payment reminders for overdue amounts'
    ]
  },
  'payment-history': {
    id: 'payment-history',
    title: 'Viewing payment history',
    content: 'Access complete payment records for all your students. Filter by date, class, or payment status.',
    steps: [
      'Navigate to "Payments" in the sidebar',
      'Click on the "History" tab',
      'Use filters to narrow down by date range, class, or student',
      'View individual payment details by clicking on any entry',
      'Export payment reports for accounting purposes'
    ],
    tips: [
      'Regularly review payment history for discrepancies',
      'Use reports for tax and accounting purposes',
      'Track payment trends to forecast income'
    ]
  },
  'manage-refunds': {
    id: 'manage-refunds',
    title: 'Managing refunds',
    content: 'Process refunds when students withdraw or for other valid reasons. Keep clear records of all refund transactions.',
    steps: [
      'Go to the "Payments" section',
      'Find the original payment that needs to be refunded',
      'Click "Process Refund"',
      'Enter the refund amount and reason',
      'Confirm the refund - it will be recorded in the payment history',
      'Notify the student/parent about the refund'
    ],
    tips: [
      'Have a clear refund policy in your class terms',
      'Process refunds promptly once approved',
      'Keep documentation for all refund requests'
    ]
  },
  'update-profile': {
    id: 'update-profile',
    title: 'Updating profile information',
    content: 'Keep your profile up to date so students and parents can reach you and learn about your qualifications.',
    steps: [
      'Click your avatar in the top right corner',
      'Select "Profile" from the menu',
      'Click "Edit Profile" button',
      'Update any fields you wish to change',
      'Upload a new profile picture if needed',
      'Click "Save Changes" to confirm'
    ],
    tips: [
      'Update your bio when you gain new qualifications',
      'Ensure your contact information is current',
      'Add your teaching specialties to attract the right students'
    ]
  },
  'change-password': {
    id: 'change-password',
    title: 'Changing passwords',
    content: 'Regularly update your password to keep your account secure. Choose a strong, unique password.',
    steps: [
      'Go to Settings from your profile menu',
      'Navigate to the "Security" tab',
      'Click "Change Password"',
      'Enter your current password for verification',
      'Enter your new password twice',
      'Click "Update Password" to save'
    ],
    tips: [
      'Use a mix of letters, numbers, and symbols',
      'Don\'t reuse passwords from other sites',
      'Consider using a password manager'
    ]
  },
  'privacy-settings': {
    id: 'privacy-settings',
    title: 'Privacy settings',
    content: 'Control who can see your information and how your data is used on the platform.',
    steps: [
      'Access Settings from your profile menu',
      'Go to the "Privacy" tab',
      'Review each privacy option',
      'Toggle settings for profile visibility, contact sharing, etc.',
      'Review data sharing preferences',
      'Save your privacy preferences'
    ],
    tips: [
      'Review privacy settings periodically',
      'Decide what information should be visible to students vs parents',
      'Read the privacy policy for complete information'
    ]
  },
  'notification-preferences': {
    id: 'notification-preferences',
    title: 'Notification preferences',
    content: 'Customize which notifications you receive and how you receive them (email, push, in-app).',
    steps: [
      'Go to Settings from your profile',
      'Navigate to "Notifications" tab',
      'Review each notification category',
      'Toggle on/off for email, push, or in-app notifications',
      'Set quiet hours if you don\'t want notifications at certain times',
      'Save your preferences'
    ],
    tips: [
      'Keep important notifications like attendance and payments enabled',
      'Use quiet hours during teaching to avoid distractions',
      'Enable email notifications as a backup for important updates'
    ]
  },
  'chat-feature': {
    id: 'chat-feature',
    title: 'Using the chat feature',
    content: 'Communicate directly with students and parents through the built-in chat. Keep all communication in one place.',
    steps: [
      'Click on "Chat" in the sidebar navigation',
      'View your existing conversations or start a new one',
      'Click on a conversation to open it',
      'Type your message and press Enter or click Send',
      'Attach files or images if needed',
      'View message read receipts'
    ],
    tips: [
      'Keep communication professional',
      'Respond to messages within a reasonable time',
      'Use chat for quick updates, email for formal communication'
    ]
  },
  'send-announcements': {
    id: 'send-announcements',
    title: 'Sending announcements',
    content: 'Broadcast important information to all members of a class at once through announcements.',
    steps: [
      'Navigate to "Announcements" from the sidebar',
      'Click "Create Announcement"',
      'Write a clear, descriptive title',
      'Compose your announcement message',
      'Select which classes should receive it',
      'Choose notification options (email, push)',
      'Click "Publish" to send immediately'
    ],
    tips: [
      'Use announcements for class-wide information',
      'Keep titles short and descriptive',
      'Avoid sending too many announcements - it can cause fatigue'
    ]
  },
  'parent-teacher-comm': {
    id: 'parent-teacher-comm',
    title: 'Parent-teacher communication',
    content: 'Stay connected with parents about their child\'s progress. Schedule meetings and send updates easily.',
    steps: [
      'Parents can message teachers through the Chat feature',
      'Schedule parent-teacher meetings through the Meetings section',
      'Send progress reports via the student profile',
      'Use announcements for group updates to all parents',
      'Respond to parent queries in a timely manner'
    ],
    tips: [
      'Be proactive in communicating student progress',
      'Schedule regular check-in meetings',
      'Keep parents informed about attendance and assignment issues'
    ]
  },
  'student-messaging': {
    id: 'student-messaging',
    title: 'Student messaging guidelines',
    content: 'Guidelines for appropriate and effective communication between teachers and students.',
    steps: [
      'Students can message teachers with academic questions',
      'Use professional language in all communications',
      'Respond to student queries during reasonable hours',
      'Keep messages focused on academic matters',
      'Report any inappropriate messages immediately'
    ],
    tips: [
      'Set clear boundaries about communication hours',
      'Encourage students to ask questions in class first',
      'Document important discussions for reference'
    ]
  },
  'reset-password': {
    id: 'reset-password',
    title: 'How to reset your password',
    content: 'Forgot your password? Here\'s how to reset it and regain access to your account.',
    steps: [
      'Go to the login page',
      'Click "Forgot Password?" link',
      'Enter your registered email address',
      'Check your email for the reset link',
      'Click the link and enter a new password',
      'Log in with your new password'
    ],
    tips: [
      'Check your spam folder if you don\'t see the reset email',
      'Reset links expire after 24 hours',
      'Contact support if you don\'t receive the email'
    ]
  },
  'login-issues': {
    id: 'login-issues',
    title: 'Troubleshooting login issues',
    content: 'Having trouble logging in? Here are common issues and their solutions.',
    steps: [
      'Verify you\'re using the correct email address',
      'Check if Caps Lock is on when typing your password',
      'Try resetting your password if you\'ve forgotten it',
      'Clear your browser cache and cookies',
      'Try a different browser or device',
      'Contact support if issues persist'
    ],
    tips: [
      'Bookmark the login page to avoid phishing sites',
      'Keep your browser updated for best compatibility',
      'Enable "Remember me" on trusted devices only'
    ]
  },
  'payment-processing': {
    id: 'payment-processing',
    title: 'Understanding payment processing',
    content: 'Learn how payments work on the platform, including payment methods, processing times, and records.',
    steps: [
      'Payments can be recorded manually by teachers',
      'Each payment is linked to a specific student and class',
      'Payment status shows as Pending, Paid, or Overdue',
      'Receipts are generated automatically for each payment',
      'Payment history is available for both teachers and parents'
    ],
    tips: [
      'Keep payment records updated in real-time',
      'Send reminders before payment due dates',
      'Use the reports feature for financial tracking'
    ]
  },
  'student-permissions': {
    id: 'student-permissions',
    title: 'Managing student permissions',
    content: 'Control what students can access and do within your class. Set appropriate permissions for different activities.',
    steps: [
      'Go to your class settings',
      'Navigate to "Student Permissions" section',
      'Review default permissions for students',
      'Toggle permissions for chat, submissions, viewing grades, etc.',
      'Set individual permissions for specific students if needed',
      'Save your permission settings'
    ],
    tips: [
      'Start with restrictive permissions and open up as needed',
      'Explain permission policies to students',
      'Review permissions periodically'
    ]
  },
  'first-class-setup': {
    id: 'first-class-setup',
    title: 'Setting up your first class',
    content: 'A comprehensive guide to setting up your first class from start to finish.',
    steps: [
      'Create your teacher profile with all details',
      'Click "Create Class" from the Classes page',
      'Fill in class name, subject, and description',
      'Set the class type (online, offline, or hybrid)',
      'Configure the fee structure for the class',
      'Create a schedule for class sessions',
      'Share the join code with students',
      'Post a welcome announcement',
      'You\'re ready to start teaching!'
    ],
    tips: [
      'Prepare your class materials before the first session',
      'Send a welcome message to enrolled students',
      'Have a backup plan for technical issues'
    ]
  }
};
export default function HelpCenterPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const auth = useAuth();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);
const handleSignUp  = async () => {
   // window.location.href = "https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/signup?client_id=76u1v7el416ebllhpbhtqpmlh0&code_challenge=Cjh7j5XvSKwPZ5ahIhP5j2tuEvZiuoSm811Q62N0wFs&code_challenge_method=S256&redirect_uri=http%3A%2F%2Flocalhost%3A8081%2Flogin%2Foauth2%2Fcode%2Fcognito&response_type=code&scope=email+openid+phone&state=80e73e7091c04c30a0c4904373b2096f";
     setIsSubmitting(true);
    
    try {
      // Store form values in localStorage to access after redirect back from Cognito
    //  localStorage.setItem("registerFormData", JSON.stringify(form.getValues()));
      
      // Redirect to Cognito signup page
      await auth.signinRedirect({ prompt: "login" });
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };

  const helpCategories: HelpCategory[] = [
    {
      title: 'Getting Started',
      icon: BookOpen,
      articles: [
        helpArticles['create-class'],
        helpArticles['setup-profile'],
        helpArticles['user-roles'],
        helpArticles['platform-overview']
      ],
      color: 'bg-blue-500'
    },
    {
      title: 'Class Management',
      icon: Users,
      articles: [
        helpArticles['add-students'],
        helpArticles['manage-schedules'],
        helpArticles['create-assignments'],
        helpArticles['class-announcements']
      ],
      color: 'bg-green-500'
    },
    {
      title: 'Scheduling & Attendance',
      icon: Calendar,
      articles: [
        helpArticles['create-schedules'],
        helpArticles['take-attendance'],
        helpArticles['leave-requests'],
        helpArticles['recurring-sessions']
      ],
      color: 'bg-purple-500'
    },
    {
      title: 'Payments & Billing',
      icon: CreditCard,
      articles: [
        helpArticles['fee-structure'],
        helpArticles['process-payments'],
        helpArticles['payment-history'],
        helpArticles['manage-refunds']
      ],
      color: 'bg-orange-500'
    },
    {
      title: 'Account Settings',
      icon: Settings,
      articles: [
        helpArticles['update-profile'],
        helpArticles['change-password'],
        helpArticles['privacy-settings'],
        helpArticles['notification-preferences']
      ],
      color: 'bg-gray-500'
    },
    {
      title: 'Communication',
      icon: MessageCircle,
      articles: [
        helpArticles['chat-feature'],
        helpArticles['send-announcements'],
        helpArticles['parent-teacher-comm'],
        helpArticles['student-messaging']
      ],
      color: 'bg-pink-500'
    }
  ];

  const popularArticles: Article[] = [
    helpArticles['reset-password'],
    helpArticles['login-issues'],
    helpArticles['payment-processing'],
    helpArticles['student-permissions'],
    helpArticles['first-class-setup']
  ];


  const filteredCategories = helpCategories.map(category => ({
    ...category,
    articles: category.articles.filter(article =>
      article.title.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.articles.length > 0);

  return (
    <div className=" flex flex-col bg-gradient-to-b from-white to-edu-light">
      <Header/>
      {/* Hero Section with gradient background */}
        <section className="py-16 md:py-24 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-blue-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto relative z-10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
              <div className="max-w-2xl text-center md:text-left">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white leading-tight">
                  Transform <span className="text-yellow-300">Education</span> Through Innovation
                </h1>
                <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
                  Join thousands of educators and students in revolutionizing the learning experience
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <Button asChild size="lg" className="w-full sm:w-auto bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full shadow-lg">
                    <Link to="/register">Get Started Free</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="w-full sm:w-auto border-2 bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full">
                    <Link to="/subscription">View Pricing</Link>
                  </Button>
                </div>
                <div className="mt-12 flex flex-col sm:flex-row items-center justify-center md:justify-start gap-6">
                  <div className="flex -space-x-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="w-12 h-12 rounded-full border-4 border-purple-600 bg-white shadow-lg flex items-center justify-center text-purple-600 font-bold text-lg">
                        {String.fromCharCode(64 + i)}
                      </div>
                    ))}
                  </div>
                  <p className="text-lg text-white/90">
                    Joined by <span className="font-bold text-yellow-300">2000+</span> educators
                  </p>
                </div>
              </div>
              <div className="w-full md:w-2/5 relative">
                <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-transform duration-500">
                  <img 
                    src="https://images.unsplash.com/photo-1571260899304-425eee4c7efc?q=80&w=2070&auto=format&fit=crop" 
                    alt="EduConnect Platform" 
                    className="w-full h-64 md:h-96 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800">Modern Learning Experience</h3>
                    <p className="text-gray-600">Interactive tools for better engagement</p>
                  </div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-xl shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <p className="text-lg font-bold flex items-center gap-2">
                    <Sparkles className="h-5 w-5" />
                    AI-Powered Learning
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12 mt-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">Help Center</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Find answers to your questions and learn how to make the most of our platform
          </p>
          
          {/* Search Bar */}
          <div className="relative max-w-2xl mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search for help articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="edu-form-field  pl-10 py-6 text-lg"
            />
          </div>
        </div>

        {/* Popular Articles */}
        {!searchQuery && (
          <div className="mb-12">
            <h2 className="text-2xl font-semibold mb-6">Popular Articles</h2>
            <div className="grid gap-4">
              {popularArticles.map((article, index) => (
                <Card 
                  key={index} 
                  className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => setSelectedArticle(article)}
                >
                  <CardContent className="p-4 flex items-center justify-between">
                    <p className="font-medium">{article.title}</p>
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

      {/* Help Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">
            {searchQuery ? 'Search Results' : 'Browse by Category'}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {(searchQuery ? filteredCategories : helpCategories).map((category, index) => {
              const IconComponent = category.icon;
              return (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${category.color} text-white`}>
                        <IconComponent className="h-5 w-5" />
                      </div>
                      {category.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {category.articles.map((article, articleIndex) => (
                        <div 
                          key={articleIndex} 
                          className="cursor-pointer hover:text-primary transition-colors flex items-center justify-between group"
                          onClick={() => setSelectedArticle(article)}
                        >
                          <p className="text-sm">{article.title}</p>
                          <ChevronRight className="h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
        {/* Contact Support */}
        <Card className="bg-primary/5 border-primary/20">
          <CardHeader>
            <CardTitle className="text-center">Still Need Help?</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              Can't find what you're looking for? Our support team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                className="default bg-purple-600 hover:bg-purple-700"
                onClick={() => navigate('/contactus')}
              >
                Contact Support
              </Button>
              <Button className="default bg-purple-600 hover:bg-purple-700">
                Schedule a Demo
              </Button>
            </div>
            <div className="flex flex-wrap justify-center gap-2 mt-4">
              <Badge variant="secondary">24/7 Support</Badge>
              <Badge variant="secondary">Live Chat</Badge>
              <Badge variant="secondary">Video Tutorials</Badge>
            </div>
          </CardContent>
        </Card>
      </div>
                 <footer className="bg-gray-900 text-white py-8 md:py-12 mt-6">
              <Footer/>
            </footer>
       {/* Article Detail Sheet */}
      <Sheet open={!!selectedArticle} onOpenChange={(open) => !open && setSelectedArticle(null)}>
        <SheetContent className="w-full sm:max-w-xl overflow-y-auto">
          {selectedArticle && (
            <>
              <SheetHeader className="mb-6">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-fit mb-4 -ml-2"
                  onClick={() => setSelectedArticle(null)}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Help Center
                </Button>
                <SheetTitle className="text-2xl">{selectedArticle.title}</SheetTitle>
                <SheetDescription className="text-base mt-4">
                  {selectedArticle.content}
                </SheetDescription>
              </SheetHeader>

              {selectedArticle.steps && selectedArticle.steps.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-primary" />
                    Step-by-Step Guide
                  </h3>
                  <div className="space-y-3">
                    {selectedArticle.steps.map((step, index) => (
                      <div key={index} className="flex gap-3 items-start">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/10 text-primary flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <p className="text-sm text-muted-foreground pt-0.5">{step}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {selectedArticle.tips && selectedArticle.tips.length > 0 && (
                <div className="bg-muted/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                    Pro Tips
                  </h3>
                  <ul className="space-y-2">
                    {selectedArticle.tips.map((tip, index) => (
                      <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                        <span className="text-green-500 mt-1">•</span>
                        {tip}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="mt-8 pt-6 border-t">
                <p className="text-sm text-muted-foreground text-center">
                  Was this article helpful? <Button variant="link" className="p-0 h-auto">Contact Support</Button> if you need more help.
                </p>
              </div>
            </>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
}