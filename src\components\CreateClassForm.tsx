import { useState } from "react";
import { useAuth } from "react-oidc-context";
import { useApp } from "@/context/AppContext";
import { ClassType } from "@/types";
import Button from 'react-bootstrap/Button';
import Col from 'react-bootstrap/Col';
import Form from 'react-bootstrap/Form';
import Row from 'react-bootstrap/Row';
import { Video } from "lucide-react";
import { toast } from "sonner";
import { UserRole } from "@/types";
import { generateAvatarUrl } from "@/lib/utils";

export function CreateClassForm() {
  const auth = useAuth();
  const { createClass } = useApp();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validated, setValidated] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    courseTitle: "",
    classType: ClassType.OFFLINE,
    platform: "",
    meetingLink: ""
  });

  // Get user from OIDC
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;

  const handleSubmit = (event) => {
    const form = event.currentTarget;
    if (form.checkValidity() === false) {
      event.preventDefault();
      event.stopPropagation();
    } else {
      event.preventDefault();
      if (!user) {
        toast.error("You must be logged in to create a class");
        return;
      }
      
      setIsSubmitting(true);
      
      setTimeout(() => {
        createClass({
          className: formData.name,
          courseTitle: formData.courseTitle,
          classType: formData.classType,
          description: "",
          teacherId: user.id,
          students: [],
          parents: [],
          assignments: [],
          announcements: [],
          onlineMeeting: formData.classType === ClassType.ONLINE ? 
            { 
              platform: formData.platform, 
              link: formData.meetingLink 
            } : undefined
        });
        
        setIsSubmitting(false);
        setFormData({
          name: "",
          courseTitle: "",
          classType: ClassType.OFFLINE,
          platform: "",
          meetingLink: ""
        });
        setValidated(false);
        toast.success("Class created successfully!");
      }, 1000);
    }

    setValidated(true);
  };

  return (
    <Form noValidate validated={validated} onSubmit={handleSubmit}>
      <Row className="mb-3">
        <Form.Group as={Col} md="6" controlId="validationCustom01">
          <Form.Label>Class Name</Form.Label>
          <Form.Control
            required
            type="text"
            placeholder="Enter class name"
            value={formData.name}
            onChange={(e) => setFormData({...formData, name: e.target.value})}
          />
          <Form.Control.Feedback type="invalid">
            Please provide a valid class name.
          </Form.Control.Feedback>
        </Form.Group>
        
        <Form.Group as={Col} md="6" controlId="validationCustom02">
          <Form.Label>Course Title</Form.Label>
          <Form.Control
            required
            type="text"
            placeholder="Introduction to Algebra"
            value={formData.courseTitle}
            onChange={(e) => setFormData({...formData, courseTitle: e.target.value})}
          />
          <Form.Control.Feedback type="invalid">
            Please provide a valid course title.
          </Form.Control.Feedback>
        </Form.Group>
      </Row>
      
      <Row className="mb-3">
        <Form.Group as={Col} md="6" controlId="validationCustom03">
          <Form.Label>Class Type</Form.Label>
          <Form.Select
            required
            value={formData.classType}
            onChange={(e) => setFormData({...formData, classType: e.target.value as ClassType})}
          >
            <option value="">Select class type</option>
            <option value={ClassType.OFFLINE}>Offline</option>
            <option value={ClassType.ONLINE}>Online</option>
          </Form.Select>
          <Form.Control.Feedback type="invalid">
            Please select a class type.
          </Form.Control.Feedback>
        </Form.Group>
      </Row>

      {formData.classType === ClassType.ONLINE && (
        <div className="mb-3 p-3 border rounded">
          <div className="d-flex align-items-center mb-3 text-primary">
            <Video className="me-2" size={20} />
            <h6 className="mb-0">Online Meeting Details</h6>
          </div>
          
          <Row className="mb-3">
            <Form.Group as={Col} md="6" controlId="validationCustom04">
              <Form.Label>Platform</Form.Label>
              <Form.Select
                required
                value={formData.platform}
                onChange={(e) => setFormData({...formData, platform: e.target.value})}
              >
                <option value="">Select platform</option>
                <option value="zoom">Zoom</option>
                <option value="google-meet">Google Meet</option>
                <option value="teams">Microsoft Teams</option>
                <option value="other">Other</option>
              </Form.Select>
              <Form.Control.Feedback type="invalid">
                Please select a platform.
              </Form.Control.Feedback>
            </Form.Group>
            
            <Form.Group as={Col} md="6" controlId="validationCustom05">
              <Form.Label>Meeting Link</Form.Label>
              <Form.Control
                required
                type="url"
                placeholder="Enter meeting link or ID"
                value={formData.meetingLink}
                onChange={(e) => setFormData({...formData, meetingLink: e.target.value})}
              />
              <Form.Control.Feedback type="invalid">
                Please provide a valid meeting link.
              </Form.Control.Feedback>
            </Form.Group>
          </Row>
        </div>
      )}
      
      <Button type="submit" className="w-100" disabled={isSubmitting}>
        {isSubmitting ? "Creating..." : "Create Class"}
      </Button>
    </Form>
  );
}

export default CreateClassForm;