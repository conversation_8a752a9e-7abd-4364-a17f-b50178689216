
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>t, <PERSON>ci<PERSON>, Star, ThumbsUp, Calendar } from "lucide-react";
import { Link } from "react-router-dom";
import { useAuth } from "react-oidc-context";

import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import ProfileSidebar from "@/components/profile/ProfileSidebar";
import { getTeacherReviewSummary ,getTeacherReviews} from "@/services/reviewService";

export default function ReviewsPage() {
  const  user  = useAuth().user;
  const [reviewData, setReviewData] = useState([]);
  const [filteredReviews, setFilteredReviews] = useState([]);
  const [activeFilter, setActiveFilter] = useState('all');
  const [summaryData, setSummaryData] = useState(null);
 const dispatch = useDispatch();
  const userData = useSelector((state: any) => state.user.userData);
  useEffect(() => {
    const fetchReviews = async () => {
      try {
        const reviews = await getTeacherReviews(user?.access_token);
        setReviewData(reviews);
        setFilteredReviews(reviews);
      } catch (error) {
        console.error('Error fetching reviews:', error);
      }
    };

    const fetchSummary = async () => {
      try {
        const summary = await getTeacherReviewSummary(user?.access_token);
    
        setSummaryData(summary);
      } catch (error) {
        console.error('Error fetching summary:', error);
      }
    };
    
    if (user?.access_token && userData?.id) {
      fetchReviews();
      fetchSummary();
    }
  }, [user?.access_token, userData?.id]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="p-4 border-b bg-white">
        <div className="flex items-center gap-2">
          <Link to="/teacher-dashboard" className="flex items-center">
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </Link>
          <h1 className="text-lg font-medium">Reviews</h1>
        </div>
      </div>

      <div className="container mx-auto max-w-4xl py-6 px-4">
        <div className="grid grid-cols-12 gap-6">
          {/* Sidebar */}
          <div className="col-span-12 md:col-span-3">
            <ProfileSidebar activePage="reviews" />
          </div>

          {/* Main Content */}
          <div className="col-span-12 md:col-span-9 space-y-6">
            <Card className="overflow-hidden">
              <div className="p-6">
                <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6">
                  <div>
                    <h2 className="text-xl font-semibold">Student Reviews</h2>
                    <p className="text-gray-500 text-sm">See what students say about your teaching</p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <div className="flex items-center">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className="w-5 h-5"
                            fill="#FBBF24"
                            color="#FBBF24"
                          />
                        ))}
                      </div>
                      <span className="ml-2 font-medium">{summaryData?.averageRating || (reviewData.length > 0 ? (reviewData.reduce((sum, r) => sum + r.rating, 0) / reviewData.length).toFixed(1) : 0)}</span>
                    </div>
                    <span className="text-sm text-gray-500">{summaryData?.totalReviews || reviewData.length} reviews</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-4 mb-8">
                  <Button
                    variant="outline"
                    className={activeFilter === 'all' ? "bg-purple-600 text-white hover:bg-purple-700 border-purple-600" : "border-gray-300 text-gray-700 hover:bg-gray-100"}
                    onClick={() => {
                      setActiveFilter('all');
                      setFilteredReviews(reviewData);
                    }}
                  >
                    All
                  </Button>
                  <Button 
                    variant="outline" 
                    className={activeFilter === '5' ? "bg-purple-600 text-white hover:bg-purple-700 border-purple-600" : "border-gray-300 text-gray-700 hover:bg-gray-100"}
                    onClick={() => {
                      setActiveFilter('5');
                      setFilteredReviews(reviewData.filter(r => r.rating === 5));
                    }}
                  >
                    5 ⭐ ({summaryData?.ratingDistribution?.fiveStars || reviewData.filter(r => r.rating === 5).length})
                  </Button>
                  <Button 
                    variant="outline" 
                    className={activeFilter === '4' ? "bg-purple-600 text-white hover:bg-purple-700 border-purple-600" : "border-gray-300 text-gray-700 hover:bg-gray-100"}
                    onClick={() => {
                      setActiveFilter('4');
                      setFilteredReviews(reviewData.filter(r => r.rating === 4));
                    }}
                  >
                    4 ⭐ ({summaryData?.ratingDistribution?.fourStars || reviewData.filter(r => r.rating === 4).length})
                  </Button>
                  <Button 
                    variant="outline" 
                    className={activeFilter === '3' ? "bg-purple-600 text-white hover:bg-purple-700 border-purple-600" : "border-gray-300 text-gray-700 hover:bg-gray-100"}
                    onClick={() => {
                      setActiveFilter('3');
                      setFilteredReviews(reviewData.filter(r => r.rating === 3));
                    }}
                  >
                    3 ⭐ ({summaryData?.ratingDistribution?.threeStars || reviewData.filter(r => r.rating === 3).length})
                  </Button>
                </div>

                {/* Reviews List */}
                <div className="space-y-8">
                  {filteredReviews && filteredReviews.length > 0 ? (
                    filteredReviews.map((review) => (
                      <div key={review.id} className="border-0 pb-6">
                        <div className="flex justify-between mb-2">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 rounded-full bg-blue-200 flex items-center justify-center">
                              <span className="text-blue-700 font-medium">
                                {review.studentName ? review.studentName.split(' ').map(n => n[0]).join('') : 'S'}
                              </span>
                            </div>
                            <div>
                              <h4 className="font-medium">{review.studentName || 'Anonymous Student'}</h4>
                              <div className="flex items-center text-sm text-gray-500">
                                <span>{review.reviewTitle}</span>
                                <span className="mx-2">•</span>
                                <span>{review.className}</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-sm text-gray-500">
                            <Calendar className="inline h-3 w-3 mr-1" />
                            <span>{new Date(review.createdDate).toLocaleDateString()}</span>
                          </div>
                        </div>
                        <div className="flex items-center mb-3 mt-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className="w-4 h-4"
                              fill={star <= review.rating ? "#FBBF24" : "#E5E7EB"}
                              color={star <= review.rating ? "#FBBF24" : "#E5E7EB"}
                            />
                          ))}
                        </div>
                        <p className="text-gray-700 mb-3">
                          {review.reviewText}
                        </p>
                      {/*  <div className="flex items-center text-gray-500 text-sm">
                          <Button variant="ghost" size="sm" className="text-gray-500 hover:text-purple-600">
                            <ThumbsUp className="h-4 w-4 mr-1" /> Helpful
                          </Button>
                        </div>*/}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      No reviews available
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
