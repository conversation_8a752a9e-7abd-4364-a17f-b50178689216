
export interface Student {
  id: string;
  studentName: string;
  userName: string;
  status: "ACTIVE" | "INACTIVE" | "GRADUATED" | "DROPPED_OUT";
  email: string;
  joinedDate: string;
  progress: number;
  classes: string[];
  lastActivity: string;
  payments: {
    status: "paid" | "pending" | "overdue";
    amount: number;
  };
  classroomName: string;
  phoneNumber: string;
  dateOfBirth: string;
  parentName: string;
  country : string;
  parentPhone: string;
  enrollmentStatus :string;
}
export async function addStudentToClass(classId: string, studentData: { studentName: string; email: string }) {
  const response = await fetch(`/api/enrollmentManagement/v1/classrooms/${classId}/students`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(studentData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to add student");
  }
  return response.json();
}
export async function addStudent(studentData: any, accessToken: string) {
  const response = await fetch("/api/studentManagement/v1/students", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(studentData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to add student");
  }
  return response.json();
}

export async function getAllStudentsFromBackend(accessToken: string) {
  const response = await fetch("/api/studentManagement/v1/students", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch students");
  }
  return response.json();
}
const _pendingEnrolledRequests: Map<string, Promise<any>> = new Map();

export async function getEnrolledStudentsForClass(accessToken: string, classroomId: string) {
  const key = `${accessToken}|${classroomId}`;
  if (_pendingEnrolledRequests.has(key)) {
    return _pendingEnrolledRequests.get(key);
  }

  const p = (async () => {
    const response = await fetch(`/api/enrollmentManagement/v1/classrooms/${classroomId}/enrollments`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${accessToken}`
      }
    });
    if (response.status === 401) {
      window.location.href = "/";
      return;
    }
    if (!response.ok) {
      throw new Error("Failed to fetch students");
    }
    return response.json();
  })();

  _pendingEnrolledRequests.set(key, p);
  // Ensure we remove the pending promise once resolved/errored
  p.finally(() => {
    _pendingEnrolledRequests.delete(key);
  });
  return p;
};
export async function unEnrollStudentfrmClassroom(accessToken: string, classroomId: string, studentId: number | string,role: string) {
  const response = await fetch(`/api/enrollmentManagement/v1/teacher/unenroll/classroom/${classroomId}/student/${studentId}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify({ classroomId, studentId, role })
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to unenroll student from classroom");
  }
  return response.json();
}
export async function getStudents(): Promise<Student[]> {
  try {
    const response = await fetch('/src/data/students.json');
    if (!response.ok) {
      throw new Error('Failed to fetch students data');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching students:', error);
    return [];
  }
}
export async function reEnrollStudentToClassroom(accessToken: string, classroomId: string, studentId: number | string,role: string) {
  const response = await fetch(`/api/enrollmentManagement/v1/teacher/reenroll/classroom/${classroomId}/student/${studentId}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify({ classroomId, studentId, role })
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to unenroll student from classroom");
  }
  return response.json();
}

