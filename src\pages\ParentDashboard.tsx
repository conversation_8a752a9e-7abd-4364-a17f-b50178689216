
import React, { useState, useEffect } from "react";
import { useAuth } from "react-oidc-context";
import { useApp } from "@/context/AppContext";
import { Link, useNavigate } from "react-router-dom";
import { 
  Bell,
  User,
  MessageSquare,
  BookOpen,
  Home,
  Calendar,
  FileText
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Announcement } from "@/types";
import { getAllAnnouncements } from "@/services/announcementService";
import { toast } from "sonner";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import {  UserRole } from "@/types";
 import { generateAvatarUrl } from "@/lib/utils";

export default function ParentDashboard() {
  const auth = useAuth();
  const { classes } = useApp();
  // Get user from OIDC
    const user = auth.isAuthenticated ? {
      id: auth.user?.profile.sub || "",
      name: auth.user?.profile.name || "User",
      email: auth.user?.profile.email || "",
      role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
      avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
    } : null;
  const navigate = useNavigate();
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Filter classes where parent's children are enrolled
  const parentClasses = classes.filter(c => c.parents?.includes(user?.id as string) || []);

  // Navigation items configuration
  const navigationItems = [
    {
      name: "Dashboard",
      path: "/parent-dashboard",
      icon: Home,
      color: "bg-blue-100 text-blue-600"
    },
    {
      name: "Teacher Meetings",
      path: "/parent-meetings",
      icon: Calendar,
      color: "bg-purple-100 text-purple-600"
    },
    {
      name: "Announcements",
      path: "/announcements",
      icon: Bell,
      color: "bg-amber-100 text-amber-600"
    },
    {
      name: "Message Teachers",
      path: "/chat",
      icon: MessageSquare,
      color: "bg-green-100 text-green-600"
    },
    {
      name: "Student Progress",
      path: "/student-progress",
      icon: FileText,
      color: "bg-pink-100 text-pink-600"
    }
  ];
  
  // Fetch announcements when component mounts
  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        setIsLoading(true);
        const data = await getAllAnnouncements( auth.user.access_token);  
        const sortedAnnouncements = data
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
          .slice(0, 3);
        setAnnouncements(sortedAnnouncements);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching announcements:", error);
        toast.error("Failed to load announcements");
        setIsLoading(false);
      }
    };

    fetchAnnouncements();
  }, []);
  
  const currentDate = new Date();
  const formattedDate = new Intl.DateTimeFormat('en-US', { 
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(currentDate);

  // Helper functions
  const getAnnouncementIcon = (iconType: string) => {
    switch (iconType) {
      case "announcement":
        return "📢";
      case "book":
        return "📚";
      case "award":
        return "🏆";
      case "celebration":
        return "🎉";
      default:
        return "📝";
    }
  };

  const getIconColorClass = (color: string) => {
    switch (color) {
      case "red":
        return "text-red-500";
      case "blue":
        return "text-blue-500";
      case "amber":
        return "text-amber-500";
      case "green":
        return "text-green-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <div className="min-h-screen bg-slate-50">
      <DashboardHeader variant="parent" />

      <div className="container mx-auto px-4 py-4 sm:py-8">
        {/* Welcome Card */}
        <div className="mb-4 sm:mb-6 bg-white rounded-2xl p-4 sm:p-6 shadow-lg border border-slate-200 hover:shadow-xl transition-shadow duration-300">
          <h1 className="text-xl sm:text-3xl font-bold text-slate-800 mb-2 flex items-center gap-2">
            Welcome back, {user?.name?.split(' ')[0]}!
          </h1>
          <p className="text-slate-600 text-sm sm:text-base">
            Stay updated with your child's educational journey
          </p>
        </div>

        {/* Navigation Grid */}
        <div className="grid grid-cols-2 gap-2 sm:grid-cols-5 sm:gap-4 mb-4 sm:mb-6">
          {navigationItems.map((item) => (
            <Link 
              to={item.path} 
              key={item.name}
              className="transform transition-all duration-300 hover:scale-105 hover:shadow-lg"
            >
              <div className="flex items-center gap-2 p-3 rounded-xl bg-white border border-slate-200 hover:border-purple-300 transition-all duration-300 shadow-sm h-full">
                <div className={`rounded-full p-1.5 sm:p-2 flex-shrink-0 ${item.color}`}>
                  <item.icon className="h-4 w-4 sm:h-5 sm:w-5" />
                </div>
                <span className="font-medium text-xs sm:text-sm text-slate-700">{item.name}</span>
              </div>
            </Link>
          ))}
        </div>

        {/* Performance and Assignments Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
          {/* Performance Card */}
          <Card className="bg-gray-800 text-white">
            <CardContent className="p-4 sm:p-6">
              <h2 className="text-lg font-medium mb-4">Student Progress</h2>
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 sm:h-16 sm:w-16 rounded-full bg-white/10 p-1 mr-4">
                  <img 
                    src={user?.avatar} 
                    alt={user?.name}
                    className="h-full w-full object-cover rounded-full"
                  />
                </div>
                <div>
                  <div className="text-3xl sm:text-4xl font-bold">86.5%</div>
                  <div className="text-xs sm:text-sm text-gray-300">Child's Progress</div>
                </div>
              </div>

              <div className="mb-5">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xl sm:text-2xl font-bold">98%</span>
                  <div className="flex">
                    <span className="text-green-400 text-xs sm:text-sm">↑ 5% from last month</span>
                  </div>
                </div>
                <p className="text-xs sm:text-sm">Your child's attendance has been excellent. They've also submitted all assignments on time.</p>
              </div>
            </CardContent>
          </Card>

          {/* Stats Grid */}
          <div className="col-span-1 md:col-span-2 grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4 sm:p-6">
                <h3 className="text-gray-500 text-sm sm:text-base mb-2">Classes Enrolled</h3>
                <div className="flex justify-between items-center">
                  <div>
                    <div className="text-2xl sm:text-3xl font-bold">{parentClasses.length}</div>
                    <div className="text-xs sm:text-sm text-purple-600">Current Enrollment</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 sm:p-6">
                <h3 className="text-gray-500 text-sm sm:text-base mb-2">Upcoming Tests</h3>
                <div className="flex justify-between items-center">
                  <div>
                    <div className="text-2xl sm:text-3xl font-bold">3</div>
                    <div className="text-xs sm:text-sm text-amber-600">Next: Science on Friday</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 sm:p-6">
                <h3 className="text-gray-500 text-sm sm:text-base mb-2">Teacher Meetings</h3>
                <div className="flex justify-between items-center">
                  <div>
                    <div className="text-2xl sm:text-3xl font-bold">1</div>
                    <div className="text-xs sm:text-sm text-green-600">Next: Tomorrow at 3 PM</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 sm:p-6">
                <h3 className="text-gray-500 text-sm sm:text-base mb-2">Payment Status</h3>
                <div className="flex justify-between items-center">
                  <div>
                    <div className="text-2xl sm:text-3xl font-bold">Paid</div>
                    <div className="text-xs sm:text-sm text-green-600">Last payment: March 1</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Classes and Announcements */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
          {/* Classes Section */}
          <div className="md:col-span-2">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg sm:text-xl font-semibold">Child's Classes</h2>
              <Link to="/student-classes" className="text-xs sm:text-sm flex items-center text-gray-500 hover:text-gray-700">
                View All Classes 
              </Link>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {parentClasses.length > 0 ? (
                parentClasses.slice(0, 3).map((cls) => (
                  <div key={cls.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <div className="relative">
                      <img 
                        src={cls.image || "https://images.unsplash.com/photo-1633613286991-611fe299c4be?q=80&w=2070&auto=format&fit=crop"} 
                        alt={cls.className}
                        className="w-full h-32 sm:h-40 object-cover"
                      />
                      <div className="absolute top-2 left-2 bg-white rounded px-2 py-1 text-xs">Online</div>
                    </div>
                    <div className="p-3 sm:p-4">
                      <div className="flex items-center mb-2 text-xs text-gray-500">
                        <span>{cls.courseTitle}</span>
                        <span className="mx-2">•</span>
                        <span>{cls.classType}</span>
                      </div>
                      <h3 className="font-medium text-sm sm:text-base mb-3 sm:mb-4">{cls.className}</h3>
                      <div className="flex items-center text-xs text-gray-500 mb-3">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span>{formattedDate}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-500">{cls.students.length} Students</span>
                        <Button size="sm" className="bg-purple-600 hover:bg-purple-700 text-xs py-1">
                          <Link to={`/classroom/${cls.id}`} className="text-white">View Details</Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="col-span-full text-center py-8">
                  <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-3" />
                  <h3 className="text-lg font-medium text-gray-600 mb-2">No Classes Yet</h3>
                  <p className="text-gray-500 mb-4 text-sm">Your child hasn't joined any classes yet.</p>
                  <Button className="bg-purple-600 hover:bg-purple-700">
                    Enroll in a Class Now
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Announcements Section */}
          <div className="md:col-span-1">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg sm:text-xl font-semibold">Announcements</h2>
            </div>
            
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 mb-4">
              {isLoading ? (
                <div className="py-4 text-center">
                  <p className="text-gray-500 text-sm">Loading announcements...</p>
                </div>
              ) : announcements.length > 0 ? (
                announcements.map((announcement, index) => (
                  <div key={announcement.id} className={index < announcements.length - 1 ? "mb-3 sm:mb-4" : ""}>
                    <div className="flex items-center mb-1">
                      <span className={`${getIconColorClass(announcement.iconColor)} mr-2`}>
                        {getAnnouncementIcon(announcement.icon)}
                      </span>
                      <h3 className="font-medium text-sm sm:text-base">{announcement.title}</h3>
                    </div>
                    <p className="text-xs sm:text-sm text-gray-600">
                      {announcement.content}
                    </p>
                    <div className="mt-2">
                      <Button variant="link" className="p-0 h-auto text-purple-600 text-xs sm:text-sm">
                        View Details
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="py-4 text-center">
                  <p className="text-gray-500 text-sm">No announcements available</p>
                </div>
              )}
            </div>
            
            <Button variant="link" className="w-full text-purple-600" asChild>
              <Link to="/announcements">View All Announcements</Link>
            </Button>

            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 mt-4 flex items-center justify-between">
              <div>
                <Button variant="link" className="p-0 h-auto text-purple-600 flex items-center gap-1" asChild>
                  <Link to="/chat">
                    <MessageSquare className="h-4 w-4 sm:h-5 sm:w-5" />
                    Message Teachers
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
