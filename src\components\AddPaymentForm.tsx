 
 import React, { useState, useEffect } from "react";
 import { Payment } from "@/types";
 import { useApp } from "@/context/AppContext";
 import { Button } from "@/components/ui/button";
 import { Input } from "@/components/ui/input";
 import { Textarea } from "@/components/ui/textarea";
 import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue
 } from "@/components/ui/select";
 import { toast } from "sonner";
 import { createClassPayment } from "@/services/paymentService";
 import { useAuth } from "react-oidc-context";
 import {getClassIdsAndNames} from "@/services/announcementService";
 import { getEnrolledStudentsForClass } from "@/services/studentService"; // <-- import your service
 import { useSelector } from "react-redux";
 import { getStudentsFeeSelections } from "@/services/feeServices";


 interface AddPaymentFormProps {
   existingPayment?: Payment;
   studentId?: string;
   classId?: string;
   onSuccess: () => void;
   onCancel: () => void;
 }

 export default function AddPaymentForm({ existingPayment, studentId, classId, onSuccess, onCancel }: AddPaymentFormProps) {
   const { classes, createPayment, updatePayment } = useApp();
     const { user } = useAuth(); // get access_token
   const [studentOptions, setStudentOptions] = useState<{ id: string; userName: string ;userId:string }[]>([]);
   const userData = useSelector((state: any) => state.user.userData);
  const [studentFeeSelections,setStudentFeeSelections] = useState<any[]>([]);
  const [matchingFeeFound, setMatchingFeeFound] = useState(false);
  const [noFeeOptedError, setNoFeeOptedError] = useState(false);

   const [payment, setPayment] = useState({
     classId: "",
     studentId: "",
     userId:"",
     amount: "0",
     paymentDate: new Date().toISOString().substring(0, 10), // UTC date in YYYY-MM-DD format
     paymentMethod:  "",
     receiptNumber:  "",
     notes:  "",
     scheduleId : "",
     finalAmount: "0",
     discountPercentage: "0"
   });
   const [discountPercentage, setDiscountPercentage] = useState(0);
   const [finalAmount, setFinalAmount] = useState(0);
     const [classOptions, setClassOptions] = useState<{ id: string; className: string }[]>([]);
     const auth = useAuth();
   
   const fetchClasses = async () => {
       try {
         const data = await getClassIdsAndNames(auth.user.access_token);
         setClassOptions(data);
       } catch (error) {
         toast.error("Failed to load classes");
       }
     }
     useEffect(() => {
         fetchClasses();
       }, []);
       // Fetch students for dropdown
   useEffect(() => {
     const fetchStudents = async () => {
       try {
         const data = await getEnrolledStudentsForClass(auth.user.access_token ,classId);
         setStudentOptions(data);
       } catch (error) {
         toast.error("Failed to load students");
       }
     };
     fetchStudents();
   }, [auth.user.access_token]);
  useEffect(() => {
       async function fetchStudentsFeeSelections() {
         try {
           // You can replace this with actual API call when ready
           const data = await getStudentsFeeSelections(auth.user.access_token,classId);
           // Ensure data is an array before setting it
           if (Array.isArray(data)) {
             setStudentFeeSelections(data);
             console.log("studentFeeSelections array:", data);
           } else {
             // If data is not an array, wrap it in an array
             const dataArray = Array.isArray(data) ? data : [data].filter(Boolean);
             setStudentFeeSelections(dataArray);
             console.log("studentFeeSelections wrapped:", dataArray);
           }
         } catch (error) {
           console.error("Fee selection error:", error);
           toast.error("Failed to load students fee selections");
         }
       }
       if (auth.user?.access_token && classId) {
         fetchStudentsFeeSelections();
       }
     }, [auth.user?.access_token, classId]);
  
       
   const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
     const { name, value } = e.target;
     
     // Update the payment state
     setPayment(prev => {
       const updatedPayment = { ...prev, [name]: value };
       
       // If amount or discount percentage changes, recalculate final amount
       if (name === 'amount' || name === 'discountPercentage') {
         const amount = parseFloat(updatedPayment.amount) || 0;
         const discount = parseFloat(updatedPayment.discountPercentage) || 0;
         const calculatedFinalAmount = amount - (amount * discount / 100);
         
         // Update final amount as string
         updatedPayment.finalAmount = calculatedFinalAmount.toFixed(2);
       }
       
       return updatedPayment;
     });
   };

   const handleSelectChange = (name: string, value: string) => {
     setPayment(prev => ({
       ...prev,
       [name]: value
     }));
     
     // If student is selected, check for matching fee selection
     if (name === "studentId") {
       console.log(value)
       console.log(studentFeeSelections)
       const matchingFee = studentFeeSelections.find(fee => fee.studentId === value);
       console.log("Matching fee:", matchingFee);
       if (matchingFee) {
         
         setPayment(prev => ({
           ...prev,
           userId: value,
           amount: String(matchingFee.amount || 0),
           discountPercentage: String(matchingFee.discountValue || 0),
           finalAmount: String(matchingFee.finalAmount || 0),
           paymentDate: new Date().toISOString().split('T')[0],
           notes: `Fee payment for ${matchingFee.feeStructureName || 'selected fee structure'}`,
           paymentMethod: "ONLINE_PAYMENT" // Set a default payment method
         }));
         
         // Set matching fee found to true to disable form fields
         setMatchingFeeFound(true);
         setNoFeeOptedError(false);
         toast.info(`Fee structure found for this student: ${matchingFee.feeStructureName || 'Fee structure'}`);
       } else {
         // Reset matching fee found to false to enable form fields
         setMatchingFeeFound(false);
         setNoFeeOptedError(true);
       }
     }
   };

   const handleSubmit = async (e: React.FormEvent) => {
     e.preventDefault();
     
     // Use the provided classId or the one from the form
     const paymentData = {
       ...payment,
       classId: classId || payment.classId,
       studentId: payment.studentId,
       // Always use the calculated final amount if discount is applied
       amount: parseFloat(payment.finalAmount) > 0 ? parseFloat(payment.finalAmount) : parseFloat(payment.amount)
     };
     
     console.log("Payment data being submitted:", paymentData);
     // Validate all required fields
     if (!paymentData.studentId) {
       toast.error("Please select a student");
       return;
     }
     
     if (parseFloat(payment.amount) <= 0) {
       toast.error("Amount must be greater than zero");
       return;
     }
     
     if (!paymentData.paymentMethod) {
       toast.error("Please select a payment method");
       return;
     }
     
     if (!paymentData.paymentDate) {
       toast.error("Please select a payment date");
       return;
     }
     
   
     
     try {
       // Create new payment via API
       await createClassPayment(user.access_token, paymentData.classId, paymentData);
       toast.success("Payment recorded successfully");
       onSuccess();
     } catch (error) {
       toast.error("Failed to record payment");
       console.error(error);
     }
   };

   return (
     <form onSubmit={handleSubmit} className="space-y-4">
       
       <div className="space-y-2">
         <label className="text-sm font-medium">Student <span className="text-red-500">*</span></label>
         <Select
           value={payment.studentId}
           onValueChange={(value) => handleSelectChange("studentId", value)}
           disabled={!!existingPayment}
         >
           <SelectTrigger className="edu-form-field">
             <SelectValue placeholder="Select a student" />
           </SelectTrigger>
           <SelectContent>
             {studentOptions.map(student => (
               <SelectItem key={student.userId} value={student.userId}>
                 {student.userName}
               </SelectItem>
             ))}
           </SelectContent>
         </Select>
         {noFeeOptedError && (
           <p className="text-sm text-red-500 mt-1">Student did not Opt any Fee</p>
         )}
       </div>

       <div className="space-y-2">
         <div className={`grid ${matchingFeeFound ? "grid-cols-3" : "grid-cols-1"} gap-2`}>
           <div>
             <label className="text-xs font-medium">Amount <span className="text-red-500">*</span></label>
             <Input
               type="number"
               name="amount"
               className="edu-form-field"
               min="0"
               step="0.01"
               value={payment.amount}
               onChange={handleChange}
               placeholder="0.00"
             />
           </div>
           {matchingFeeFound && (
             <>
               <div>
                 <label className="text-xs font-medium">Discount %</label>
                 <Input
                   type="number"
                   name="discountPercentage"
                   min="0"
                   className="edu-form-field"
                   max="100"
                   step="1"
                   value={payment.discountPercentage}
                   placeholder="0"
                   onChange={handleChange}
                 />
               </div>
               <div>
                 <label className="text-xs font-medium">Final Amount</label>
                 <Input
                   type="number"
                   className="edu-form-field"
                   name="finalAmount"
                   min="0"
                   step="0.01"
                   value={payment.finalAmount}
                   placeholder="0.00"
                   readOnly
                 />
               </div>
             </>
           )}
         </div>
       </div>
       
       <div className="space-y-2">
         <label className="text-sm font-medium">Date</label>
         <Input
           type="date"
           
           className="edu-form-field"
           name="paymentDate"
           value={payment.paymentDate || new Date().toISOString().split('T')[0]}
           onChange={handleChange}
           //disabled={matchingFeeFound}
         />
       </div>

      
       
      
       
       <div className="space-y-2">
         <label className="text-sm font-medium">Payment Method</label>
         <Select 
           value={payment.paymentMethod || ""} 
           onValueChange={(value) => handleSelectChange("paymentMethod", value)}
           //disabled={matchingFeeFound}
         >
           <SelectTrigger className="edu-form-field"
                   >
             <SelectValue placeholder="Select payment method" />
           </SelectTrigger>
           <SelectContent>
             <SelectItem value="CASH">Cash</SelectItem>
             <SelectItem value="CREDIT_CARD">Credit Card</SelectItem>
             <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
             <SelectItem value="DEBIT_CARD">Debit Card</SelectItem>
             <SelectItem value="CHECK">Check</SelectItem>
             <SelectItem value="OTHER">Other</SelectItem>
             <SelectItem value="MOBILE_PAYMENT">Mobile Payment</SelectItem>
             <SelectItem value="ONLINE_PAYMENT">Online Payment</SelectItem>
           </SelectContent>
         </Select>
       </div>
       
       <div className="space-y-2">
         <label className="text-sm font-medium">Receipt Number (Optional)</label>
         <Input
           name="receiptNumber"
           value={payment.receiptNumber || ""}
           onChange={handleChange}
           placeholder="Receipt or transaction number"
           //disabled={matchingFeeFound}
           className="edu-form-field"
                   
         />
       </div>
       
       <div className="space-y-2">
         <label className="text-sm font-medium">Memo / Notes (Optional)</label>
         <Textarea
           name="notes"
           className="edu-form-field"
           value={payment.notes || ""}
           onChange={handleChange}
           placeholder="Additional notes"
           rows={3}
           //disabled={matchingFeeFound}
         />
       </div>
       
       <div className="flex justify-end gap-3 pt-2">
         <Button
           type="button"
           variant="outline"
           onClick={onCancel}
         >
           Cancel
         </Button>
         <Button
           type="submit"
           className="bg-purple-600 hover:bg-purple-700"
           disabled={ noFeeOptedError}
         >
           {existingPayment ? "Update Payment" : "Record Payment"}
         </Button>
       </div>
     </form>
   );
 }
