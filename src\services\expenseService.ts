// POST: Create a new Expense
export async function createNewExpense(accessToken: string, data: any)
 {
  const response = await fetch(`/api/expense/v1/expenses`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(data)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) throw new Error("Failed to create Expense");
  return response.json();
}
// GET: Get all expenses
export async function getAllExpenses(accessToken: string) {
  const response = await fetch(`/api/expense/v1/expenses`, {
    method: "GET",
    headers: {
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) throw new Error("Failed to get expenses");
  return response.json();
}

// Update an existing expense
export async function updateExistingExpense(accessToken: string,id: string, updateData: any) {
  const response = await fetch(`/api/expense/v1/expenses/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(updateData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update Expense");
  }
  return response.json();
}
// Delete an announcement
export async function deleteExistingExpense(accessToken: string,id: string) {
  const response = await fetch(`/api/expense/v1/expenses/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to delete Expense");
  }
  return true;
}

// GET: Get monthly expenses
export async function getMonthlyExpenses(accessToken: string) {
  const response = await fetch(`/api/expense/v1/expenses/chart/monthly`, {
    method: "GET",
    headers: {
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) throw new Error("Failed to get expenses");
  return response.json();
}

// GET: Get monthly expenses
export async function getExpensesByCategory(accessToken: string) {
  const response = await fetch(`/api/expense/v1/expenses/chart/category`, {
    method: "GET",
    headers: {
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) throw new Error("Failed to get expenses");
  return response.json();
}
