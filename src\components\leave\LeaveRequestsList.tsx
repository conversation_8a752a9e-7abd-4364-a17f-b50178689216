import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Check, X, Calendar, User } from "lucide-react";
import { format } from "date-fns";
import { LeaveRequest } from "@/types";
import { getLeaveRequestsByClass, updateLeaveRequestStatus ,getLeaveRequestsForStudent} from "@/services/leaveService";
import { toast } from "sonner";
import { useAuth } from "react-oidc-context"; // Updated import



interface LeaveRequestsListProps {
  classId: string;
  isTeacher?: boolean;
  studentId?: string;
  refreshTrigger?: number;
}

export function LeaveRequestsList({ classId, isTeacher = false, studentId, refreshTrigger }: LeaveRequestsListProps) {
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [teacherComments, setTeacherComments] = useState<{ [key: string]: string }>({});
 const auth = useAuth();
  const loadLeaveRequests = async () => {
    try {
      setLoading(true);
      const requests = isTeacher 
        ? await getLeaveRequestsByClass(auth.user.access_token, classId)
        : await getLeaveRequestsForStudent(auth.user.access_token, classId);
      
      setLeaveRequests(requests);
    } catch (error) {
      console.error("Error loading leave requests:", error);
      toast.error("Failed to load leave requests");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (classId) {
      loadLeaveRequests();
    }
  }, [classId, isTeacher, refreshTrigger]);

  const handleStatusUpdate = async (leaveId: string, status: "APPROVED" | "DENIED") => {
    try {
      const notes = teacherComments[leaveId] || "";
      await updateLeaveRequestStatus(auth.user.access_token,leaveId, status, notes);
      
      setLeaveRequests(prev => 
        prev.map(req => 
          req.id === leaveId
            ? { ...req, status, reviewedAt: new Date(), teacherComments: notes }
            : req
        )
      );
      
      setTeacherComments(prev => ({ ...prev, [leaveId]: "" }));
      toast.success(`Leave request ${status}`);
    } catch (error) {
      console.error("Error updating leave status:", error);
      toast.error("Failed to update leave status");
    }
  };

  const getStatusBadgeVariant = (status: LeaveRequest['status']) => {
    switch (status) {
      case "APPROVED": return "default";
      case "DENIED": return "destructive";
      case "PENDING": return "secondary";
      default: return "secondary";
    }
  };

  if (loading) {
    return <div className="text-center py-4">Loading leave requests...</div>;
  }

  return (
    <Card  className="mt-4">
      <CardHeader>
        <CardTitle>
          {isTeacher ? "Leave Requests Management" : "My Leave Requests"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {leaveRequests.length === 0 ? (
          <p className="text-muted-foreground text-center py-8">
            No leave requests found.
          </p>
        ) : (
          <div className="space-y-4 ">
            {leaveRequests.map((request) => (
              <div key={request.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                  <div className="flex items-center gap-2">
                    {isTeacher && (
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <User className="h-4 w-4" />
                        Student Name: <span title={request.studentId}>{request.studentName}</span>
                      </div>
                    )}
                    <Badge 
                      variant={getStatusBadgeVariant(request.status)}
                      className={request.status === "APPROVED" ? "bg-green-500 text-white hover:bg-green-600" : ""}
                    >
                      {request.status?.charAt(0).toUpperCase() + request.status?.slice(1)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    Applied: {request.createdDate ? format(new Date(request.createdDate), "MMM dd, yyyy") : "N/A"}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Leave Period</Label>
                    <p className="text-sm">
                      {request.startDate ? format(new Date(request.startDate), "MMM dd, yyyy") : "N/A"} - {request.endDate ? format(new Date(request.endDate), "MMM dd, yyyy") : "N/A"}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Reason</Label>
                    <p className="text-sm">{request.reason}</p>
                  </div>
                </div>

                {request.teacherComments && (
                  <div>
                    <Label className="text-sm font-medium">Teacher Comments</Label>
                    <p className="text-sm text-muted-foreground">{request.teacherComments}</p>
                  </div>
                )}

                {isTeacher && request.status?.toLowerCase() === "pending" && (
                  <div className="space-y-3 pt-3 border-t">
                    <div>
                      <Label htmlFor={`notes-${request.id}`}>Teacher Comments (Optional)</Label>
                      <Textarea
                        id={`notes-${request.id}`}
                        placeholder="Add any notes about this leave request..."
                        value={teacherComments[request.id] || ""}
                        onChange={(e) => setTeacherComments(prev => ({
                          ...prev,
                          [request.id]: e.target.value
                        }))}
                        rows={2}
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleStatusUpdate(request.id, "APPROVED")}
                        className="flex items-center gap-2"
                        size="sm"
                      >
                        <Check className="h-4 w-4" />
                        Approve
                      </Button>
                      <Button
                        onClick={() => handleStatusUpdate(request.id, "DENIED")}
                        variant="destructive"
                        className="flex items-center gap-2"
                        size="sm"
                      >
                        <X className="h-4 w-4" />
                        Deny
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}