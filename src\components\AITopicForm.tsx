import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { addSyllabusWithAI, addSyllabusToClassroom } from '@/services/syllabusService';
import { useAuth } from 'react-oidc-context';
import { useParams } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface AITopicFormProps {
  onCancel: () => void;
  onSuccess?: () => void;
  className?: string;
  gradeLevel?: string;
}

const AITopicForm: React.FC<AITopicFormProps> = ({ onCancel, onSuccess, className, gradeLevel }) => {
  const auth = useAuth();
  const { classId } = useParams<{ classId: string }>();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    subject: className || "",
    gradeLevel: gradeLevel || "", 
    additionalRequirements: "based on New jersey Edison school district course"
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const aiResponse = await addSyllabusWithAI(auth.user?.access_token, classId, formData);
      console.log('AI Response:', aiResponse);
      
      if (aiResponse && Array.isArray(aiResponse)) {
        if (aiResponse.length === 0) {
          toast.error('No topics generated. Please try again after sometime.');
          return;
        }
        for (const topic of aiResponse) {
          const syllabusData = {
            title: topic.topic,
            content: topic.description,
            attachedFileIds: [],
            referenceUrls: topic.referenceUrls
          };
          console.log('Syllabus Data:', syllabusData);
          await addSyllabusToClassroom(auth.user?.access_token, classId, syllabusData);
        }
        onSuccess?.();
      }
      onCancel();
    } catch (error) {
      console.error('Failed to generate AI syllabus:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="subject">Subject</Label>
        <Input
          id="subject"
          className='edu-form-field'
          value={formData.subject}
          onChange={(e) => setFormData({...formData, subject: e.target.value})}
        />
      </div>
      
      <div>
        <Label htmlFor="gradeLevel">Grade Level</Label>
        <Input
          id="gradeLevel"
          className='edu-form-field'
          
          value={formData.gradeLevel}
          onChange={(e) => setFormData({...formData, gradeLevel: e.target.value})}
        />
      </div>
      
      <div>
        <Label htmlFor="additionalRequirements">Additional Requirements</Label>
        <Input
          id="additionalRequirements"
          className='edu-form-field'
          
          value={formData.additionalRequirements}
          onChange={(e) => setFormData({...formData, additionalRequirements: e.target.value})}
        />
      </div>
      
      <div className="flex gap-2">
        <Button type="submit"className='bg-purple-600 hover:bg-purple-700 ' disabled={isLoading}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isLoading ? 'Generating...' : 'Generate'}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>Cancel</Button>
      </div>
    </form>
  );
};

export default AITopicForm;