
import React, { createContext, useContext, ReactNode } from 'react';
import { useAuth as useOidcAuth } from 'react-oidc-context';
import { UserRole } from '@/types';
import { generateAvatarUrl } from '@/lib/utils';

// Define the User type that our app expects
export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar: string;
}

// Define the Auth context value type
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string, role: UserRole) => Promise<void>;
  logout: () => Promise<void>;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType | null>(null);

// Provider component that wraps your app and makes auth object available to any
// child component that calls useAuth().
export function AuthProvider({ children }: { children: ReactNode }) {
  // Get auth from OIDC
  const auth = useOidcAuth();
  
  // Convert OIDC user to our User type
  const user = auth.isAuthenticated && auth.user ? {
    id: auth.user.profile.sub || "",
    name: auth.user.profile.name || "User",
    email: auth.user.profile.email || "",
    role: (auth.user.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user.profile.name || "User", "3498db")
  } : null;
  
  // Adapt OIDC methods to our expected interface
  const login = async (email: string, password: string) => {
    // This is a compatibility layer - in the future we should update components to use OIDC directly
    console.warn("Legacy auth.login called - should migrate to OIDC");
    await auth.signinRedirect();
  };
  
  const register = async (name: string, email: string, password: string, role: UserRole) => {
    // Compatibility layer
    console.warn("Legacy auth.register called - should migrate to OIDC");
    // Registration in Cognito would typically be handled separately
  };
  
  const logout = async () => {
    // Compatibility layer
    console.warn("Legacy auth.logout called - should migrate to OIDC");
    auth.removeUser();
  };
  
  // Create the context value
  const value = {
    user,
    isLoading: auth.isLoading,
    error: auth.error,
    login,
    register,
    logout
  };
  
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
