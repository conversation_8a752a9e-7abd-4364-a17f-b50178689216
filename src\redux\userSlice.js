// src/store/userSlice.ts
import { createSlice } from '@reduxjs/toolkit';

const getPersistedProfile = () => {
  if (typeof window === 'undefined') return null;
  try {
    const stored = localStorage.getItem('userProfile');
    return stored ? JSON.parse(stored) : null;
  } catch {
    return null;
  }
};

const initialState = {
  selectedRole: typeof window !== 'undefined' ? sessionStorage.getItem('userRole') || '' : '',
  timezone: typeof window !== 'undefined' ? sessionStorage.getItem('userTimezone') || Intl.DateTimeFormat().resolvedOptions().timeZone : 'UTC',
  userData: null,
  userId: null,
  userProfile: getPersistedProfile(),
  isProfileLoaded: !!getPersistedProfile()
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUserId: (state, action) => {
      state.userId = action.payload;
    },
    setSelectedRole: (state, action) => {
      const role = action.payload;
      const currentRole = state.selectedRole;
      
      // Only update if the role is actually changing
      if (role !== currentRole) {
        state.selectedRole = role;
        
        // Set in sessionStorage
        if (role && typeof window !== 'undefined') {
          try {
            sessionStorage.setItem('userRole', role);
          } catch (e) {
            console.error('Failed to set sessionStorage', e);
          }
        }
      }
    },
    setUserData: (state, action) => {
      state.userData = action.payload;
    },
    setUserProfile: (state, action) => {
      state.userProfile = action.payload;
      state.isProfileLoaded = true;
      // Persist to localStorage
      if (typeof window !== 'undefined') {
        try {
          localStorage.setItem('userProfile', JSON.stringify(action.payload));
        } catch (e) {
          console.error('Failed to persist user profile', e);
        }
      }
    },
    setTimezone: (state, action) => {
      state.timezone = action.payload;
      if (typeof window !== 'undefined') {
        try {
          sessionStorage.setItem('userTimezone', action.payload);
        } catch (e) {
          console.error('Failed to set timezone in sessionStorage', e);
        }
      }
    },
    clearUserData: (state) => {
      state.selectedRole = '';
      state.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      state.userData = null;
      state.userProfile = null;
      state.isProfileLoaded = false;
      
      // Remove from sessionStorage and localStorage
      if (typeof window !== 'undefined') {
        try {
          sessionStorage.removeItem('userRole');
          sessionStorage.removeItem('userTimezone');
          localStorage.removeItem('userProfile');
        } catch (e) {
          console.error('Failed to remove from storage', e);
        }
      }
    },
  }
});

export const { setSelectedRole, setUserData, clearUserData, setUserId, setUserProfile, setTimezone } = userSlice.actions;
export default userSlice.reducer;