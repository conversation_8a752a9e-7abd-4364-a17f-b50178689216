
import React, { useState, useEffect } from "react";
import { Payment } from "@/types";
import { useApp } from "@/context/AppContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { toast } from "sonner";
import { createClassPayment } from "@/services/paymentService";
import { useAuth } from "react-oidc-context";
import {getClassIdsAndNames} from "@/services/announcementService";
import { getAllStudentsFromBackend, getEnrolledStudentsForClass } from "@/services/studentService"; // <-- import your service

interface PaymentFormProps {
  existingPayment?: Payment;
  studentId?: string;
  classId?: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function PaymentForm({ existingPayment, studentId, classId, onSuccess, onCancel }: PaymentFormProps) {
  const { classes, createPayment, updatePayment } = useApp();
    const { user } = useAuth(); // get access_token
  const [studentOptions, setStudentOptions] = useState<{ id: string; studentName: string }[]>([]);

  const [payment, setPayment] = useState<{
    classId: string;
    studentId: string;
    amount: number;
    paymentDate: string; // Add the required date field
    description: string;
    paymentMethod?: string;
    receiptNumber?: string;
    notes?: string;
  }>({
    classId: existingPayment?.classId || classId || "",
    studentId: existingPayment?.studentId || studentId || "",
    amount: existingPayment?.amount || 0,
    paymentDate: existingPayment?.date || new Date().toISOString().split('T')[0], // Default to today
    description: existingPayment?.description || "",
    paymentMethod: existingPayment?.paymentMethod || "",
    receiptNumber: existingPayment?.receiptNumber || "",
    notes: existingPayment?.memo || ""
  });
    const [classOptions, setClassOptions] = useState<{ id: string; className: string }[]>([]);
    const auth = useAuth();
  
  const fetchClasses = async () => {
      try {
        const data = await getClassIdsAndNames(auth.user.access_token);
        setClassOptions(data);
      } catch (error) {
        toast.error("Failed to load classes");
      }
    }
    useEffect(() => {
        fetchClasses();
      }, []);
      // Fetch students for dropdown
  useEffect(() => {
    const fetchStudents = async () => {
      try {
        const data = await getEnrolledStudentsForClass(auth.user.access_token,classId);
        setStudentOptions(data);
      } catch (error) {
        toast.error("Failed to load students");
      }
    };
    fetchStudents();
  }, [auth.user.access_token]);

      
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setPayment(prev => ({
      ...prev,
      [name]: name === "amount" ? parseFloat(value) || 0 : value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setPayment(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!payment.classId  || payment.amount <= 0 || !payment.description) {
      toast.error("Please fill all required fields");
      return;
    }

    try {
      if (existingPayment) {
        // Update existing payment
        await updatePayment(existingPayment.id, payment);
      } else {
        // Create new payment via API
        await createClassPayment(user.access_token, payment.classId, payment);
      }
      onSuccess();
    } catch (error) {
      toast.error("Failed to record payment");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium">Class <span className="text-red-500">*</span></label>
        <Select 
          value={payment.classId} 
          onValueChange={(value) => handleSelectChange("classId", value)}
          disabled={!!existingPayment}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a class" />
          </SelectTrigger>
          <SelectContent>
            {classOptions.map(classItem => (
              <SelectItem key={classItem.id} value={classItem.id}>
                {classItem.className}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <label className="text-sm font-medium">Student <span className="text-red-500">*</span></label>
        <Select
          value={payment.studentId}
          onValueChange={(value) => handleSelectChange("studentId", value)}
          disabled={!!existingPayment}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a student" />
          </SelectTrigger>
          <SelectContent>
            {studentOptions.map(student => (
              <SelectItem key={student.id} value={student.id}>
                {student.studentName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Amount <span className="text-red-500">*</span></label>
        <div className="relative">
          <span className="absolute left-3 top-2.5">$</span>
          <Input
            type="number"
            name="amount"
            className="edu-form-field pl-7"
                   
            min="0"
            step="0.01"
            value={payment.amount}
            onChange={handleChange}
            
            placeholder="0.00"
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <label className="text-sm font-medium">Date</label>
        <Input
          type="date"
          className="edu-form-field"
                   
          name="date"
          value={payment.paymentDate}
          onChange={handleChange}
        />
      </div>
      
      <div className="space-y-2">
        <label className="text-sm font-medium">Description <span className="text-red-500">*</span></label>
        <Input
          name="description"
          className="edu-form-field"
                   
          value={payment.description}
          onChange={handleChange}
          placeholder="e.g., Monthly fee, Textbooks, etc."
        />
      </div>
      
      <div className="space-y-2">
        <label className="text-sm font-medium">Payment Method</label>
        <Select 
          value={payment.paymentMethod || ""} 
          onValueChange={(value) => handleSelectChange("paymentMethod", value)}
        >
          <SelectTrigger className="edu-form-field"
                   >
            <SelectValue placeholder="Select payment method" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="CASH">Cash</SelectItem>
            <SelectItem value="CREDIT_CARD">Credit Card</SelectItem>
            <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
            <SelectItem value="DEBIT_CARD">Debit Card</SelectItem>
            <SelectItem value="CHECK">Check</SelectItem>
            <SelectItem value="OTHER">Other</SelectItem>
            <SelectItem value="MOBILE_PAYMENT">Mobile Payment</SelectItem>
            <SelectItem value="ONLINE_PAYMENT">Online Payment</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <label className="text-sm font-medium">Receipt Number (Optional)</label>
        <Input
        className="edu-form-field"
                   
          name="receiptNumber"
          value={payment.receiptNumber || ""}
          onChange={handleChange}
          placeholder="Receipt or transaction number"
        />
      </div>
      
      <div className="space-y-2">
        <label className="text-sm font-medium">Memo / Notes (Optional)</label>
        <Textarea
          name="memo"
          value={payment.notes || "khgkjh"}
          onChange={handleChange}
          placeholder="Additional notes"
          rows={3}
        />
      </div>
      
      <div className="flex justify-end gap-3 pt-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          className="bg-purple-600 hover:bg-purple-700"
        >
          {existingPayment ? "Update Payment" : "Record Payment"}
        </Button>
      </div>
    </form>
  );
}
