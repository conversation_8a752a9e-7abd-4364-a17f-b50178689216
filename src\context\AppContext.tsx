import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "react-oidc-context";  // Use OIDC auth
import { Class, ClassType, Announcement, ScheduleEvent, ChatMessage, Payment, PaymentStatus, Assignment, FeeStructure, Attendance, ParentTeacherMeeting, MeetingSlot, User, UserRole } from "@/types";
import { nanoid, generateAvatarUrl } from "@/lib/utils";
import { toast } from "sonner";

interface Expense {
  id: string;
  title: string;
  description: string;
  amount: number;
  date: Date;
  category: string;
  createdAt: Date;
  updatedAt: Date;
}

const MOCK_CLASSES: Class[] = [
  {
    id: "cls-001",
    teacherId :"sdsf",
    className: "Mathematics 101",
    courseTitle: "Introduction to Algebra",
    subjectName: "sdfsf",
    description: "Learn the fundamentals of algebra",
    level : "sfsf",
    batchName: "adafdaf",
    classType: ClassType.REGULAR,
    capacity :3,
    students: ["2"],
    parents: ["3"],
    assignments: [],
    announcements: [],
    joinCode: "MATH101",
    joinUrl: "/join/MATH101",
    createdAt: new Date(),
    fee: 50.00,
    feeStructures: [
      {
        id: "fee1",
        country: "United States",
        paymentType: "Monthly",
        feeAmount: 50,
        discountPercentage: 0,
        description :"ss",status: "active"
      },
      {
        id: "fee2",
        country: "United Kingdom",
        paymentType: "Monthly",
        feeAmount: 40,
        discountPercentage: 5,
        description :"ss",status: "active"
      }
    ]
  },
  {
    id: "cls-002",
    teacherId :"sdsf",
    className: "Mathematics 101",
    courseTitle: "Introduction to Algebra",
    subjectName: "sdfsf",
    description: "Learn the fundamentals of algebra",
    level : "sfsf",
    batchName: "adafdaf",
    classType: ClassType.REGULAR,
    capacity :3,
    students: ["2"],
    parents: ["3"],
    assignments: [],
    announcements: [],
    joinCode: "SCI101",
    joinUrl: "/join/SCI101",
    createdAt: new Date(),
    fee: 75.00,
    feeStructures: [
      {
        id: "fee3",
        country: "United States",
        paymentType: "Monthly",
        feeAmount: 75,
        discountPercentage: 0,
        description :"ss",status: "active"
      },
      {
        id: "fee4",
        country: "Canada",
        paymentType: "Yearly",
        feeAmount: 800,
        discountPercentage: 10,
        description :"ss",status: "active"
      }
    ]
  }
];

const MOCK_ANNOUNCEMENTS: Announcement[] = [
];

const MOCK_SCHEDULE: ScheduleEvent[] = [];

const MOCK_CHAT_MESSAGES: ChatMessage[] = [];

const MOCK_PAYMENTS: Payment[] = [
  
];

const MOCK_EXPENSES: Expense[] = [];

const MOCK_ASSIGNMENTS: Assignment[] = [
];

const MOCK_ATTENDANCE: Attendance[] = [];

const MOCK_MEETINGS: ParentTeacherMeeting[] = [];

type AppContextType = {
  classes: Class[];
  announcements: Announcement[];
  schedules: ScheduleEvent[];
  messages: ChatMessage[];
  payments: Payment[];
  expenses: Expense[];
  assignments: Assignment[];
  attendance: Attendance[];
  meetings: ParentTeacherMeeting[];
  createClass: (classData: Omit<Class,  "joinCode" | "joinUrl" | "createdAt">) => void;
  updateClass: (classId: string, classData: Partial<Class>) => void;
  joinClass: (joinCode: string) => boolean;
  createAnnouncement: (announcementData: Omit<Announcement, "id" | "createdAt">) => void;
  updateAnnouncement: (announcementId: string, announcementData: Partial<Omit<Announcement, "id" | "createdAt" | "authorId">>) => void;
  deleteAnnouncement: (announcementId: string) => void;
  createScheduleEvent: (eventData: Omit<ScheduleEvent, "id">) => void;
  sendMessage: (messageData: Omit<ChatMessage, "id" | "timestamp" | "read">) => void;
  createPayment: (paymentData: Omit<Payment, "id" | "createdAt" | "updatedAt" | "status">) => void;
  updatePaymentStatus: (paymentId: string, status: PaymentStatus) => void;
  updatePayment: (paymentId: string, paymentData: Partial<Omit<Payment, "id" | "createdAt">>) => void;
  deletePayment: (paymentId: string) => void;
  createExpense: (expenseData: Omit<Expense, "id" | "createdAt" | "updatedAt">) => void;
  updateExpense: (expenseId: string, expenseData: Partial<Expense>) => void;
  deleteExpense: (expenseId: string) => void;
  createAssignment: (assignmentData: Omit<Assignment, "id" | "createdAt">) => void;
  submitAssignment: (assignmentId: string, studentId: string) => void;
  gradeSubmission: (assignmentId: string, studentId: string, submission: AssignmentSubmission) => void;
  shareClass: (classId: string, method: "email" | "whatsapp" | "copy" | "other") => void;
  recordAttendance: (attendanceData: Omit<Attendance, "id" | "createdAt" | "updatedAt">) => void;
  updateAttendance: (attendanceId: string, status: "present" | "absent" | "late" | "excused", notes?: string) => void;
  getAttendanceByStudent: (studentId: string, classId?: string) => Attendance[];
  createFeeStructure: (classId: string, feeData: Omit<FeeStructure, "id" | "status">) => void;
  updateFeeStructure: (classId: string, feeId: string, feeData: Partial<Omit<FeeStructure, "id">>) => void;
  deleteFeeStructure: (classId: string, feeId: string) => void;
  createMeeting: (meetingData: Omit<ParentTeacherMeeting, "id" | "createdAt" | "isActive" | "slots">, slots: Omit<MeetingSlot, "id" | "isBooked" | "bookedBy" | "studentId">[]) => ParentTeacherMeeting;
  bookMeetingSlot: (meetingId: string, slotId: string, parentId: string, studentId: string) => boolean;
  cancelMeetingSlot: (meetingId: string, slotId: string) => boolean;
  updateMeeting: (meetingId: string, meetingData: Partial<Omit<ParentTeacherMeeting, "id" | "createdAt" | "slots">>) => void;
  deleteMeeting: (meetingId: string) => void;
  addMeetingSlot: (meetingId: string, slotData: Omit<MeetingSlot, "id" | "isBooked" | "bookedBy" | "studentId">) => void;
  getMeetingsByTeacher: (teacherId: string) => ParentTeacherMeeting[];
  getMeetingsByClass: (classId: string) => ParentTeacherMeeting[];
  getMeetingsByParent: (parentId: string) => { meeting: ParentTeacherMeeting, bookedSlots: MeetingSlot[] }[];
};

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const auth = useAuth();  // Use OIDC auth
  
  // Convert OIDC user to our User type
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;
  
  const [classes, setClasses] = useState<Class[]>(MOCK_CLASSES);
  const [announcements, setAnnouncements] = useState<Announcement[]>(MOCK_ANNOUNCEMENTS);
  const [schedules, setSchedules] = useState<ScheduleEvent[]>(MOCK_SCHEDULE);
  const [messages, setMessages] = useState<ChatMessage[]>(MOCK_CHAT_MESSAGES);
  const [payments, setPayments] = useState<Payment[]>(MOCK_PAYMENTS);
  const [expenses, setExpenses] = useState<Expense[]>(MOCK_EXPENSES);
  const [assignments, setAssignments] = useState<Assignment[]>(MOCK_ASSIGNMENTS);
  const [attendance, setAttendance] = useState<Attendance[]>(MOCK_ATTENDANCE);
  const [meetings, setMeetings] = useState<ParentTeacherMeeting[]>(MOCK_MEETINGS);

  // Fix for infinite update loop - add proper dependency array and prevent unnecessary updates
  useEffect(() => {
    if (!user) return;
    
    // Use an additional check to prevent unnecessary updates
    setClasses(prev => {
      // Only filter if needed based on user role
      if (user.role === UserRole.TEACHER) {
        return prev.filter(c => c.teacherId === user.id);
      } else if (user.role === UserRole.STUDENT) {
        return prev.filter(c => c.students.includes(user.id));
      } else if (user.role === UserRole.PARENT) {
        return prev.filter(c => c.parents.includes(user.id));
      }
      return prev; // Return unchanged if no filtering needed
    });
  }, [user?.id, user?.role]); // Only depend on stable properties, not the entire user object

  const createClass = (classData: Omit<Class, "id" | "joinCode" | "joinUrl" | "createdAt">) => {
    const joinCode = generateJoinCode();
    const newClass: Class = {
      id: nanoid(),
      joinCode,
      
      className : "",
      subjectName : "",
      description: "",
      level: "",
      batchName: "",
      classType: ClassType,
      capacity : "",
    
      joinUrl: `/join/${joinCode}`,
      createdAt: new Date(),
      ...classData,
      assignments: classData.assignments || [],
     announcements: classData.announcements || []
    };
    
    setClasses(prev => [...prev, newClass]);
    
    return newClass;
  };

  const updateClass = (classId: string, classData: Partial<Class>) => {
    setClasses(prev => 
      prev.map(classItem => 
        classItem.id === classId 
          ? { ...classItem, ...classData } 
          : classItem
      )
    );
  };

  const joinClass = (joinCode: string) => {
    if (!user) return false;
    
    const classIndex = classes.findIndex(c => c.joinCode === joinCode);
    if (classIndex === -1) return false;
    
    setClasses(prev => {
      const updatedClasses = [...prev];
      const classToUpdate = { ...updatedClasses[classIndex] };
      
      if (user?.role ===  UserRole.STUDENT && !classToUpdate.students.includes(user.id)) {
        classToUpdate.students = [...classToUpdate.students, user.id];
      } else if (user?.role ===  UserRole.PARENT && !classToUpdate.parents.includes(user.id)) {
        classToUpdate.parents = [...classToUpdate.parents, user.id];
      } else {
        return prev;
      }
      
      updatedClasses[classIndex] = classToUpdate;
      return updatedClasses;
    });
    
    return true;
  };

  const shareClass = (classId: string, method: "email" | "whatsapp" | "copy" | "other") => {
    const classToShare = classes.find(c => c.id === classId);
    if (!classToShare) return;
    
    const shareMsg = `Join my class "${classToShare.className}" using this code: ${classToShare.joinCode} or link: ${window.location.origin}${classToShare.joinUrl}`;
    
    switch (method) {
      case "email":
        window.open(`mailto:?subject=Join my class: ${classToShare.className}&body=${encodeURIComponent(shareMsg)}`);
        break;
      case "whatsapp":
        window.open(`https://wa.me/?text=${encodeURIComponent(shareMsg)}`);
        break;
      case "copy":
        navigator.clipboard.writeText(shareMsg).then(() => {
          toast("Join link copied to clipboard!");
        });
        break;
      default:
        navigator.clipboard.writeText(shareMsg);
    }
  };

  const createAnnouncement = (announcementData: Omit<Announcement, "id" | "createdAt">) => {
    const newAnnouncement: Announcement = {
      id: nanoid(),
      createdDate: new Date(),
      ...announcementData
    };
    
    setAnnouncements(prev => [...prev, newAnnouncement]);
  };

  const updateAnnouncement = (announcementId: string, announcementData: Partial<Omit<Announcement, "id" | "createdAt" | "authorId">>) => {
    setAnnouncements(prev => 
      prev.map(announcement => 
        announcement.id === announcementId 
          ? { ...announcement, ...announcementData, isEditing: false }
          : announcement
      )
    );
    toast.success("Announcement updated successfully");
  };

  const deleteAnnouncement = (announcementId: string) => {
    setAnnouncements(prev => prev.filter(announcement => announcement.id !== announcementId));
  };

  const createScheduleEvent = (eventData: Omit<ScheduleEvent, "id">) => {
    const newEvent: ScheduleEvent = {
      id: nanoid(),
      ...eventData
    };
    
    setSchedules(prev => [...prev, newEvent]);
  };

  const sendMessage = (messageData: Omit<ChatMessage, "id" | "timestamp" | "read">) => {
    const newMessage: ChatMessage = {
      id: nanoid(),
      timestamp: new Date(),
      read: false,
      ...messageData
    };
    
    setMessages(prev => [...prev, newMessage]);
  };

  const createPayment = (paymentData: Omit<Payment, "id" | "createdAt" | "updatedAt" | "status">) => {
    const newPayment: Payment = {
      id: nanoid(),
      status: PaymentStatus.PENDING,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...paymentData
    };
    
    setPayments(prev => [...prev, newPayment]);
    toast.success("Payment recorded successfully");
    return newPayment;
  };

  const updatePaymentStatus = (paymentId: string, status: PaymentStatus) => {
    setPayments(prev => 
      prev.map(payment => 
        payment.id === paymentId 
          ? { ...payment, status, updatedAt: new Date() } 
          : payment
      )
    );
    toast.success(`Payment status updated to ${status}`);
  };

  const updatePayment = (paymentId: string, paymentData: Partial<Omit<Payment, "id" | "createdAt">>) => {
    setPayments(prev => 
      prev.map(payment => 
        payment.id === paymentId 
          ? { ...payment, ...paymentData, updatedAt: new Date() } 
          : payment
      )
    );
    toast.success("Payment updated successfully");
  };
  
  const deletePayment = (paymentId: string) => {
    setPayments(prev => prev.filter(payment => payment.id !== paymentId));
    toast.success("Payment deleted successfully");
  };

  const createExpense = (expenseData: Omit<Expense, "id" | "createdAt" | "updatedAt">) => {
    const newExpense: Expense = {
      id: nanoid(),
      createdAt: new Date(),
      updatedAt: new Date(),
      ...expenseData
    };
    
    setExpenses(prev => [...prev, newExpense]);
  };

  const updateExpense = (expenseId: string, expenseData: Partial<Expense>) => {
    setExpenses(prev => 
      prev.map(expense => 
        expense.id === expenseId 
          ? { ...expense, ...expenseData, updatedAt: new Date() } 
          : expense
      )
    );
  };

  const deleteExpense = (expenseId: string) => {
    setExpenses(prev => prev.filter(expense => expense.id !== expenseId));
  };

  const createAssignment = (assignmentData: Omit<Assignment, "id" | "createdAt">) => {
    const newAssignment: Assignment = {
      id: nanoid(),
      createdAt: new Date(),
      ...assignmentData
    };
    
    setAssignments(prev => [...prev, newAssignment]);
    toast.success("Assignment created successfully");
    return newAssignment;
  };

  const submitAssignment = (assignmentId: string, studentId: string) => {
    setAssignments(prev => 
      prev.map(assignment => 
        assignment.id === assignmentId 
          ? { 
              ...assignment, 
              submittedBy: assignment.submittedBy.includes(studentId) 
                ? assignment.submittedBy 
                : [...assignment.submittedBy, studentId] 
            } 
          : assignment
      )
    );
    toast.success("Assignment marked as submitted");
  };

  const gradeSubmission = (assignmentId: string, studentId: string, submission: AssignmentSubmission) => {
    setAssignments(prev => 
      prev.map(assignment => {
        if (assignment.id === assignmentId) {
          const existingSubmissions = assignment.submissions || [];
          const updatedSubmissions = existingSubmissions.some(s => s.studentId === studentId)
            ? existingSubmissions.map(s => s.studentId === studentId ? submission : s)
            : [...existingSubmissions, submission];
          
          return {
            ...assignment,
            submissions: updatedSubmissions
          };
        }
        return assignment;
      })
    );
    toast.success("Submission graded successfully");
  };

  const recordAttendance = (attendanceData: Omit<Attendance, "id" | "createdAt" | "updatedAt">) => {
    const existingRecord = attendance.find(
      a => a.studentId === attendanceData.studentId && 
           a.classId === attendanceData.classId &&
           a.date === attendanceData.date
    );
    
    if (existingRecord) {
      setAttendance(prev => 
        prev.map(a => 
          a.id === existingRecord.id 
            ? { ...a, ...attendanceData, updatedAt: new Date() } 
            : a
        )
      );
      toast.success("Attendance record updated");
    } else {
      const newAttendance: Attendance = {
        id: nanoid(),
        createdAt: new Date(),
        updatedAt: new Date(),
        ...attendanceData
      };
      
      setAttendance(prev => [...prev, newAttendance]);
      toast.success("Attendance recorded successfully");
    }

    return existingRecord?.id || nanoid();
  };
  
  const updateAttendance = (attendanceId: string, status: "present" | "absent" | "late" | "excused", notes?: string) => {
    setAttendance(prev => 
      prev.map(a => 
        a.id === attendanceId 
          ? { ...a, status, notes, updatedAt: new Date() } 
          : a
      )
    );
    toast.success("Attendance updated");
  };
  
  const getAttendanceByStudent = (studentId: string, classId?: string) => {
    return attendance.filter(
      a => a.studentId === studentId && (classId ? a.classId === classId : true)
    );
  };
  
  const createFeeStructure = (classId: string, feeData: Omit<FeeStructure, "id">) => {
    const newFeeStructure: FeeStructure = {
      id: nanoid(),
      ...feeData
    };
    
    setClasses(prev => 
      prev.map(classItem => {
        if (classItem.id === classId) {
          const updatedFeeStructures = classItem.feeStructures 
            ? [...classItem.feeStructures, newFeeStructure] 
            : [newFeeStructure];
          
          return {
            ...classItem,
            feeStructures: updatedFeeStructures
          };
        }
        return classItem;
      })
    );
    
    toast.success("Fee structure added successfully");
  };
  
  const updateFeeStructure = (classId: string, feeId: string, feeData: Partial<Omit<FeeStructure, "id">>) => {
    setClasses(prev => 
      prev.map(classItem => {
        if (classItem.id === classId && classItem.feeStructures) {
          const updatedFeeStructures = classItem.feeStructures.map(fee => 
            fee.id === feeId ? { ...fee, ...feeData } : fee
          );
          
          return {
            ...classItem,
            feeStructures: updatedFeeStructures
          };
        }
        return classItem;
      })
    );
    
    toast.success("Fee structure updated successfully");
  };
  
  const deleteFeeStructure = (classId: string, feeId: string) => {
    setClasses(prev => 
      prev.map(classItem => {
        if (classItem.id === classId && classItem.feeStructures) {
          const updatedFeeStructures = classItem.feeStructures.filter(fee => fee.id !== feeId);
          
          return {
            ...classItem,
            feeStructures: updatedFeeStructures
          };
        }
        return classItem;
      })
    );
    
    toast.success("Fee structure deleted");
  };

  const createMeeting = (
    meetingData: Omit<ParentTeacherMeeting, "id" | "createdAt" | "isActive" | "slots">,
    slotsData: Omit<MeetingSlot, "id" | "isBooked" | "bookedBy" | "studentId">[]
  ) => {
    const slots = slotsData.map(slot => ({
      id: nanoid(),
      startTime: slot.startTime,
      endTime: slot.endTime,
      isBooked: false
    }));

    const newMeeting: ParentTeacherMeeting = {
      id: nanoid(),
      createdAt: new Date(),
      isActive: true,
      slots,
      ...meetingData
    };
    
    setMeetings(prev => [...prev, newMeeting]);
    toast.success("Parent-teacher meeting schedule created successfully");
    return newMeeting;
  };

  const bookMeetingSlot = (meetingId: string, slotId: string, parentId: string, studentId: string) => {
    let success = false;
    
    setMeetings(prev => {
      const updatedMeetings = prev.map(meeting => {
        if (meeting.id === meetingId) {
          const updatedSlots = meeting.slots.map(slot => {
            if (slot.id === slotId && !slot.isBooked) {
              success = true;
              return {
                ...slot,
                isBooked: true,
                bookedBy: parentId,
                studentId
              };
            }
            return slot;
          });
          
          return {
            ...meeting,
            slots: updatedSlots
          };
        }
        return meeting;
      });
      
      return updatedMeetings;
    });
    
    if (success) {
      toast.success("Meeting slot booked successfully");
    } else {
      toast.error("Failed to book meeting slot. It may already be taken.");
    }
    
    return success;
  };
  
  const cancelMeetingSlot = (meetingId: string, slotId: string) => {
    let success = false;
    
    setMeetings(prev => {
      const updatedMeetings = prev.map(meeting => {
        if (meeting.id === meetingId) {
          const updatedSlots = meeting.slots.map(slot => {
            if (slot.id === slotId && slot.isBooked) {
              success = true;
              return {
                ...slot,
                isBooked: false,
                bookedBy: undefined,
                studentId: undefined
              };
            }
            return slot;
          });
          
          return {
            ...meeting,
            slots: updatedSlots
          };
        }
        return meeting;
      });
      
      return updatedMeetings;
    });
    
    if (success) {
      toast.success("Meeting slot booking cancelled");
    } else {
      toast.error("Failed to cancel meeting slot booking");
    }
    
    return success;
  };
  
  const updateMeeting = (meetingId: string, meetingData: Partial<Omit<ParentTeacherMeeting, "id" | "createdAt" | "slots">>) => {
    setMeetings(prev => 
      prev.map(meeting => 
        meeting.id === meetingId 
          ? { ...meeting, ...meetingData } 
          : meeting
      )
    );
    toast.success("Meeting updated successfully");
  };
  
  const deleteMeeting = (meetingId: string) => {
    setMeetings(prev => prev.filter(meeting => meeting.id !== meetingId));
    toast.success("Meeting deleted successfully");
  };
  
  const addMeetingSlot = (meetingId: string, slotData: Omit<MeetingSlot, "id" | "isBooked" | "bookedBy" | "studentId">) => {
    const newSlot: MeetingSlot = {
      id: nanoid(),
      isBooked: false,
      ...slotData
    };
    
    setMeetings(prev => 
      prev.map(meeting => {
        if (meeting.id === meetingId) {
          return {
            ...meeting,
            slots: [...meeting.slots, newSlot]
          };
        }
        return meeting;
      })
    );
    
    toast.success("Meeting slot added");
  };
  
  const getMeetingsByTeacher = (teacherId: string) => {
    return meetings.filter(meeting => meeting.teacherId === teacherId);
  };
  
  const getMeetingsByClass = (classId: string) => {
    return meetings.filter(meeting => meeting.classId === classId);
  };
  
  const getMeetingsByParent = (parentId: string) => {
    return meetings
      .map(meeting => {
        const bookedSlots = meeting.slots.filter(slot => slot.bookedBy === parentId);
        return { meeting, bookedSlots };
      })
      .filter(item => item.bookedSlots.length > 0);
  };

  const generateJoinCode = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let code = "";
    for (let i = 0; i < 6; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
  };

  return (
    <AppContext.Provider value={{
      classes,
      announcements,
      schedules,
      messages,
      payments,
      expenses,
      assignments,
      attendance,
      meetings,
      createClass,
      updateClass,
      joinClass,
      createAnnouncement,
      updateAnnouncement,
      deleteAnnouncement,
      createScheduleEvent,
      sendMessage,
      createPayment,
      updatePaymentStatus,
      updatePayment,
      deletePayment,
      createExpense,
      updateExpense,
      deleteExpense,
      createAssignment,
      submitAssignment,
      gradeSubmission,
      shareClass,
      recordAttendance,
      updateAttendance,
      getAttendanceByStudent,
      createFeeStructure,
      updateFeeStructure,
      deleteFeeStructure,
      createMeeting,
      bookMeetingSlot,
      cancelMeetingSlot,
      updateMeeting,
      deleteMeeting,
      addMeetingSlot,
      getMeetingsByTeacher,
      getMeetingsByClass,
      getMeetingsByParent
    }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useApp must be used within an AppProvider");
  }
  return context;
};
