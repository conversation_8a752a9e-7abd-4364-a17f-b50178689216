import React, { useState } from 'react';
import { Check, X, Users, Calendar, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { recordBulkAttendance } from "@/services/attendanceService";
import { toast } from "sonner";
import { useAuth } from "react-oidc-context"; // Updated import
import { useParams } from "react-router-dom";
import { useLocation } from "react-router-dom";
import  {useEffect} from "react";
import { getEnrolledStudentsForClass} from "@/services/studentService";
import { useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { Input } from "@/components/ui/input";
import { getAttendanceStatsSummary } from "@/services/attendanceService"; // Make sure this import exists


interface Student {
  id: string;
  studentName: string;
  phoneNumber: string;
  userName : string;
  avatar?: string;
  userId :string;
}

interface AttendanceRecord {
  studentId: string;
  status: 'present' | 'absent' | 'late';
  userId: string;
}

interface AttendanceRecordProps {
  studentId?: string;
  classId?: string;
  selectedDate?: string | null;
  onSuccess: () => void;
  onCancel: () => void;
}
const RecordAttendance = ({classId, selectedDate, onSuccess, onCancel}: AttendanceRecordProps) =>{
  const [attendanceDate, setAttendanceDate] = useState(() => {
    if (selectedDate) {
      return selectedDate;
    }
   });
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
 
  const auth = useAuth();
     const [isLoading, setIsLoading] = useState(true);
     const navigate = useNavigate();
     
  const [students, setStudents] = useState<Student[]>([]);
  const fetchStudents = async () => {
          setIsLoading(true);
          try {
            const data = await getEnrolledStudentsForClass(auth.user.access_token,classId);
           setStudents(data.filter(student => student.status === "ACTIVE"));
          } catch (error) {
            console.error("Error fetching students:", error);
          } finally {
            setIsLoading(false);
          }
        };

      
    useEffect(() => {
        fetchStudents();
      }, []);

  const updateAttendance = (studentId: string, status: 'present' | 'absent' | 'late') => {
    setAttendanceRecords(prev => {
      const existing = prev.find(record => record.studentId === studentId);
      if (existing) {
        return prev.map(record =>
          record.studentId === studentId ? { ...record, status } : record
        );
      } else {
        // Use studentId as the userId and assign studentId with userId value
        return [...prev, { studentId:studentId , status, userId: studentId }];
      }
    });
  };
  
   const getAttendanceStats = () => {
    const present = attendanceRecords.filter(r => r.status === 'present').length;
    const absent = attendanceRecords.filter(r => r.status === 'absent').length;
    const late = attendanceRecords.filter(r => r.status === 'late').length;
    const total = students.length;
    const unmarked = total - attendanceRecords.length;
   
    return { present, absent, late, unmarked, total };
  };

  const stats = getAttendanceStats();

  const getAttendanceStatus = (studentId: string) => {
    const record = attendanceRecords.find(r => r.studentId === studentId || r.userId === studentId);
    return record?.status || null;
  };

  const handleSaveAttendance = async () => {
    try {
      const saveatten = {
        attendanceDate: attendanceDate, // already in YYYY-MM-DD format
        studentAttendances: attendanceRecords.map(r => ({
          studentId: r.userId, // Use studentId directly
          status: r.status.toUpperCase(), // Convert to "PRESENT", "LATE", "ABSENT"
          // Add notes if you have them, otherwise omit
        }))

      };
      await recordBulkAttendance(auth.user.access_token, classId, saveatten);
      onSuccess();
      toast.success("Attendance saved!");
    } catch (error) {
      if (error.response?.data?.details) {
        toast.error(error.response.data.details);
      } else {
        toast.error("Failed to save attendance");
      }
    }
  };

  return (
   <div className=" bg-gray-50 py-4">
      <div className="container mx-auto px-4 max-w-full md:max-w-[90%] lg:max-w-[1400px]">
          <Card className="mb-1"> 
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                     
                      <div className="flex items-center space-x-2">
                        <Users className="h-5 w-5 text-gray-600" />
                        <span className="text-gray-600">{stats.total} Students</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Stats Cards */}
                <div className="grid grid-cols-4 gap-4 mb-6">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">{stats.present}</div>
                      <div className="text-sm text-gray-600">Present</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-red-600">{stats.absent}</div>
                      <div className="text-sm text-gray-600">Absent</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-yellow-600">{stats.late}</div>
                      <div className="text-sm text-gray-600">Late</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-gray-600">{stats.unmarked}</div>
                      <div className="text-sm text-gray-600">Unmarked</div>
                    </CardContent>
                  </Card>
                </div>

                {/* Student List */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Users className="h-5 w-5" />
                      <span>Student List</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {students.map((student) => {
                          console.log(student.userId)
                        const status = getAttendanceStatus(student.userId);
                        return (
                          <div key={student.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                <span className="text-purple-600 font-medium text-sm">
                                  {student.userName.split(' ').map(n => n[0]).join('')}
                                </span>
                              </div>
                              <div>
                                <h3 className="font-medium text-gray-900">{student.userName}</h3>
                                <p className="text-sm text-gray-600">{student.phoneNumber || 'No phone number'}</p>
                              </div>
                            </div>
                           
                            <div className="flex items-center space-x-3">
                              {status && (
                                <Badge
                                  variant="outline"
                                  className={
                                    status === 'present' ? 'bg-green-50 text-green-700 border-green-200' :
                                    status === 'late' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                                    'bg-red-50 text-red-700 border-red-200'
                                  }
                                >
                                  {status.charAt(0).toUpperCase() + status.slice(1)}
                                </Badge>
                              )}
                             
                              <div className="flex space-x-1">
                                <Button
                                  size="sm"
                                  variant={status === 'present' ? 'default' : 'outline'}
                                  onClick={() => updateAttendance(student.userId, 'present')}
                                  className={status === 'present' ? 'bg-green-600 hover:bg-green-700' : 'hover:bg-green-50 hover:text-green-700 hover:border-green-300'}
                                >
                                  <Check className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant={status === 'late' ? 'default' : 'outline'}
                                  onClick={() => updateAttendance(student.userId, 'late')}
                                  className={status === 'late' ? 'bg-yellow-600 hover:bg-yellow-700' : 'hover:bg-yellow-50 hover:text-yellow-700 hover:border-yellow-300'}
                                >
                                  <Clock className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant={status === 'absent' ? 'default' : 'outline'}
                                  onClick={() => updateAttendance(student.userId, 'absent')}
                                  className={status === 'absent' ? 'bg-red-600 hover:bg-red-700' : 'hover:bg-red-50 hover:text-red-700 hover:border-red-300'}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                   
                    {/* Save Button */}
                    <div className="mt-6 flex justify-end">
                      <Button
                        onClick={handleSaveAttendance}
                        className="bg-purple-600 hover:bg-purple-700 px-8"
                        disabled={attendanceRecords.length === 0}
                      >
                        Save Attendance
                      </Button>
                    </div>
                  </CardContent>
                </Card>

      </div>
    </div>
  );
};

export default RecordAttendance;
