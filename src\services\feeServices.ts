export async function createFeeStructure(accessToken: string, classroomId: string, feeData: any) {
  const response = await fetch("/api/feeManagement/v1/fee-structures", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify({ classroomId, ...feeData })
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create fee structure");
  }
  return response.json();
}
export async function deleteFeeStructure(accessToken: string, classId: string, feeId: string) {
  const response = await fetch(`/api/feeManagement/v1/fee-structures/${feeId}/deactivate`, {
    method: "PATCH",
    headers: {
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to delete fee structure");
  }
  return response.json();
}
export async function getFeeStructuresForClassRoom(accessToken: string, classId: string) {
  const response = await fetch(`/api/feeManagement/v1/classrooms/${classId}/fee-structures`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch fee structures");
  }
  return response.json();
}

export async function updateFeeStructure(accessToken: string, classId: string, feeId: string, feeData: any) {
   const payload = {
    countryCode: feeData.countryCode,
    paymentType: feeData.paymentType,
    feeAmount: feeData.feeAmount,
    discount: feeData.discount,
    discountPercentage: feeData.discountPercentage,
    status: feeData.status
  };
  console.log("Updating fee structure with data:", payload);
  const response = await fetch(`/api/feeManagement/v1/fee-structures/${feeId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(payload)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update fee structure");
  }
  return response.json();
}

export async function getStudentsFeeSelections(accessToken: string, classId: string) {
  const response = await fetch(`/api/feeSelection/v1/teacher/classroom/${classId}/fee-selections`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch fee structures");
  }
  return response.json();
}


export async function getStudentOptedFee(accessToken: string, classId: string) {
  const response = await fetch(`/api/feeSelection/v1/student/fee-selection/classroom/${classId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch fee structures");
  }
  return response.json();
}

export async function optInToFeeStructure(accessToken: string, classroomId: string, feeData: any) {
  const response = await fetch("/api/feeSelection/v1/student/fee-selection", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify({classroomId :classroomId , feeStructureId :feeData })
  }); 
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create fee structure");
  }
  return response.json();
}

export async function removeFeeSelection(accessToken: string, classId :string,feeSelectionId: string) {
  const response = await fetch(`/api/feeSelection/v1/teacher/classroom/${classId}/fee-selection/${feeSelectionId}`, {
    method: "DELETE",
    headers: {
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
    if (response.status === 204) {
       throw new Error("removed fee selection");

  }
  if (!response.ok) {
    throw new Error("Failed to remove fee selection");
  }
  return response.json();
}
