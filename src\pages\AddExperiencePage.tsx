
import React, { useState } from "react";
import { ArrowLeft, Calendar, Building } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import ProfileSidebar from "@/components/profile/ProfileSidebar";
import { useToast } from "@/components/ui/use-toast";

export default function AddExperiencePage() {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [experience, setExperience] = useState({
    title: "",
    company: "",
    startDate: "",
    endDate: "",
    isCurrent: false,
    description: "",
    skills: ""
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setExperience(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setExperience(prev => ({
      ...prev,
      isCurrent: e.target.checked
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!experience.title || !experience.company || !experience.startDate) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }
    
    // Validate start date is before end date
    if (experience.endDate && !experience.isCurrent && new Date(experience.startDate) >= new Date(experience.endDate)) {
      toast({
        title: "Invalid date range",
        description: "Start date must be before end date",
        variant: "destructive"
      });
      return;
    }
    
    // Add logic to save experience to database/context
    toast({
      title: "Experience added",
      description: "Your experience has been successfully added"
    });
    
    // Navigate back to experience page
    navigate('/experience');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="p-4 border-b bg-white">
        <div className="flex items-center gap-2">
          <Link to="/experience" className="flex items-center">
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </Link>
          <h1 className="text-lg font-medium">Add Experience</h1>
        </div>
      </div>

      <div className="container mx-auto max-w-4xl py-6 px-4">
        <div className="grid grid-cols-12 gap-6">
          {/* Sidebar */}
          <div className="col-span-12 md:col-span-3">
            <ProfileSidebar activePage="experience" />
          </div>

          {/* Main Content */}
          <div className="col-span-12 md:col-span-9">
            <Card>
              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                <div className="space-y-2">
                  <label htmlFor="title" className="font-medium">
                    Job Title <span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="title"
                    name="title"
                    className='edu-form-field'
                    value={experience.title}
                    onChange={handleChange}
                    placeholder="e.g., Senior Physics Professor"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="company" className="font-medium">
                    Company/Institution <span className="text-red-500">*</span>
                  </label>
                  <div className="flex items-center">
                    <Building className="h-4 w-4 mr-2 text-gray-400" />
                    <Input
                      id="company"
                      name="company"
                      className='edu-form-field'
                      value={experience.company}
                      onChange={handleChange}
                      placeholder="e.g., Stanford University"
                      required
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="startDate" className="font-medium">
                      Start Date <span className="text-red-500">*</span>
                    </label>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                      <Input
                        id="startDate"
                        name="startDate"
                        type="date"
                        className='edu-form-field'
                        value={experience.startDate}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label htmlFor="endDate" className="font-medium">
                      End Date {!experience.isCurrent && <span className="text-red-500">*</span>}
                    </label>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                      <Input
                        id="endDate"
                        name="endDate"
                        type="date"
                        className='edu-form-field'
                        value={experience.endDate}
                        onChange={handleChange}
                        disabled={experience.isCurrent}
                        required={!experience.isCurrent}
                        min={experience.startDate} // Prevent selecting end date before start date
                      />
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Input
                    id="isCurrent"
                    name="isCurrent"
                    type="checkbox"
                    className="edu-form-field w-4 h-4 mr-2"
                    checked={experience.isCurrent}
                    onChange={handleCheckboxChange}
                  />
                  <label htmlFor="isCurrent">I currently work here</label>
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="description" className="font-medium">
                    Description
                  </label>
                  <Textarea
                    id="description"
                    name="description"
                    value={experience.description}
                    onChange={handleChange}
                    placeholder="Describe your responsibilities, achievements, etc."
                    rows={4}
                  />
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="skills" className="font-medium">
                    Skills (comma separated)
                  </label>
                  <Input
                    id="skills"
                    name="skills"
                    className='edu-form-field'
                    value={experience.skills}
                    onChange={handleChange}
                    placeholder="e.g., Quantum Physics, Research, Curriculum Development"
                  />
                </div>
                
                <div className="flex justify-end gap-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/experience')}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" className="bg-purple-600 hover:bg-purple-700">
                    Save Experience
                  </Button>
                </div>
              </form>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
