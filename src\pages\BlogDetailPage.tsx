
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>cil, Check, X } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import { useAuth } from "react-oidc-context"; // Updated import
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import RichTextEditor from "@/components/RichTextEditor";
import { toast } from "sonner";
import { UserRole } from "@/types";
import { generateAvatarUrl } from "@/lib/utils";
import { useSelector } from "react-redux";
import { useUserRole } from "@/hooks/useUserRole";


export default function BlogDetailPage() {
  const auth = useAuth(); // Use OIDC auth
  const navigate = useNavigate();
  const params = useParams();
  const blogId = params.blogId;
  const [isEditing, setIsEditing] = useState(false);
  
  // Get user from OIDC
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;
  
  const [blog, setBlog] = useState({
    id: blogId || "1",
    title: "Understanding Quantum Physics for Beginners",
    content: "<p>Quantum physics is often seen as a complex subject, but with the right approach, beginners can grasp the fundamental concepts.</p><p>In this blog, we'll explore the basic principles of quantum mechanics and how they differ from classical physics.</p><p>The double-slit experiment is one of the most famous demonstrations of quantum behavior. When particles like electrons are fired at a barrier with two slits, they create an interference pattern on the screen behind, suggesting they're behaving like waves. However, if we try to observe which slit each electron passes through, the interference pattern disappears, and the electrons behave like particles again.</p><p>This demonstrates one of the key principles of quantum mechanics: observation affects the system being observed. This principle challenges our classical intuition but is fundamental to understanding quantum physics.</p>",
    date: "2023-05-15",
    author: "Dr. Richard Feynman",
    tags: ["Physics", "Quantum Mechanics", "Science"],
  });
  
  const [tempBlog, setTempBlog] = useState({
    title: blog.title,
    content: blog.content,
  });

  useEffect(() => {
    setTempBlog({
      title: blog.title,
      content: blog.content,
    });
  }, [blogId]);

  const handleGoBack = () => {
    navigate('/blog');
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setTempBlog({
      title: blog.title,
      content: blog.content,
    });
  };

  const handleSave = () => {
    setBlog(prev => ({
      ...prev,
      title: tempBlog.title,
      content: tempBlog.content
    }));
    setIsEditing(false);
    toast.success("Blog updated successfully");
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempBlog(prev => ({ ...prev, title: e.target.value }));
  };

  const handleContentChange = (content: string) => {
    setTempBlog(prev => ({ ...prev, content }));
  };
  const { selectedRole } = useUserRole();

  const isTeacher = selectedRole === UserRole.TEACHER;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="sticky top-0 z-10 p-4 bg-white shadow-sm">
        <div className="flex items-center gap-2">
          <button onClick={handleGoBack} className="flex items-center">
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </button>
          <h1 className="text-lg font-medium">Blog Detail</h1>
        </div>
      </div>

      <div className="container mx-auto py-4 px-4 md:py-6 md:max-w-4xl">
        <Card className="overflow-hidden">
          <div className="p-4 md:p-6">
            {isEditing ? (
              <div className="space-y-4">
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                  <input
                    type="text"
                    value={tempBlog.title}
                    onChange={handleTitleChange}
                    className="text-xl font-semibold w-full p-2 border border-gray-300 rounded"
                  />
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 md:flex-none flex items-center gap-1 text-red-600 border-red-600 hover:bg-red-50"
                      onClick={handleCancel}
                    >
                      <X className="h-4 w-4" />
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      className="flex-1 md:flex-none flex items-center gap-1 bg-green-600 hover:bg-green-700"
                      onClick={handleSave}
                    >
                      <Check className="h-4 w-4" />
                      Save
                    </Button>
                  </div>
                </div>
                <RichTextEditor
                  value={tempBlog.content}
                  onChange={handleContentChange}
                  placeholder="Write your blog content here..."
                />
              </div>
            ) : (
              <>
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-4">
                  <h2 className="text-2xl font-semibold">{blog.title}</h2>
                  {isTeacher && (
                    <Button
                      variant="outline"
                      className="w-full md:w-auto flex items-center gap-2 text-purple-600 border-purple-600 hover:bg-purple-50"
                      onClick={handleEdit}
                    >
                      <Pencil className="h-4 w-4" />
                      Edit
                    </Button>
                  )}
                </div>
                <div className="flex items-center text-gray-500 text-sm mb-6">
                  <span>By {blog.author} • {blog.date}</span>
                </div>
                <div className="space-y-4">
                  <div 
                    className="text-gray-700 prose max-w-none"
                    dangerouslySetInnerHTML={{ __html: blog.content }}
                  />
                </div>
                <div className="mt-6 flex flex-wrap gap-2">
                  {blog.tags.map(tag => (
                    <span key={tag} className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">
                      {tag}
                    </span>
                  ))}
                </div>
              </>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}
