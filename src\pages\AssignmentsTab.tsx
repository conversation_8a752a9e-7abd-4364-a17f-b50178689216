import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useApp } from "@/context/AppContext";

import { Button } from "../components/ui/button";
import { Plus } from "lucide-react";
import { TeacherSubmissionsView } from "@/pages/TeacherSubmissionsView";
import { getTeacherAssignmentsForClass, deleteExistingAsisgnment, updateAssignmentStatus, createAssignment } from "@/services/assignmentService";
import { Assignment } from "@/types";
import AssignmentList from "@/components/assignment/AssignmentList";
import AssignmentForm from "@/components/assignment/AssignmentForm";
const AssignmentsTab = ({ auth, classId, currentClass }: any) => {
     const [fetchedAssignments, setFetchedAssignments] = useState<Assignment[]>([]);
      const [assignmentsLoading, setAssignmentsLoading] = useState(false);
      const [publishing, setPublishing] = useState(false);
     
     const [showSubmissionsView, setShowSubmissionsView] = useState(false);
  const [submissionsAssignment, setSubmissionsAssignment] = useState<Assignment | null>(null);
  const [showAssignmentForm, setShowAssignmentForm] = useState(false);
  const [editingAssignment, setEditingAssignment] = useState<Assignment | null>(null);
  const [selectedAssignment, setSelectedAssignment] = useState<Assignment | null>(null);
  const [formMode, setFormMode] = useState<'create'|'edit'|'view'>('create');

  const navigate = useNavigate();
  const { assignments, submitAssignment } = useApp();

 const fetchAssignments = useCallback(async () => {
  if (!auth?.user?.access_token || !classId) return;
  setAssignmentsLoading(true);
  try {
    const data = await getTeacherAssignmentsForClass(auth.user.access_token, classId);
    const list = Array.isArray(data) ? data : (data?.content || []);
    setFetchedAssignments(list);
  } catch (err) {
    console.error("Failed to load assignments:", err);
    toast.error("Failed to load assignments");
  } finally {
    setAssignmentsLoading(false);
  }
}, [auth?.user?.access_token, classId]);

useEffect(() => {
  fetchAssignments();
}, [fetchAssignments]);

const classAssignments = (fetchedAssignments.length ? fetchedAssignments : assignments || []).filter((a: Assignment) => a.classroomId === classId);

const handleSubmitAssignment = (assignmentId: string) => {
  if (auth?.user) {
    submitAssignment(assignmentId, auth.user.profile.sub);
  }
};

const handleDeleteAssignment = async (assignmentId: string) => {
  const confirm = window.confirm("Are you sure you want to delete this assignment?");
  if (!confirm) return;
  try {
    await deleteExistingAsisgnment(auth.user.access_token, assignmentId);
    await fetchAssignments();
    toast.success("Assignment deleted successfully");
  } catch (err) {
    console.error("Failed to delete assignment:", err);
    toast.error("Failed to delete assignment");
  }
};

const handlePublishAssignment = async (assignment: Assignment) => {
  if (!assignment) return;
  const confirm = window.confirm("Publish this assignment? It will become visible to students.");
  if (!confirm) return;
  setPublishing(true);
  try {
    await updateAssignmentStatus(auth.user.access_token, assignment.id, "PUBLISHED");
    await fetchAssignments();
    toast.success("Assignment published successfully");
  } catch (err) {
    console.error("Publish failed", err);
    toast.error("Failed to publish assignment");
  } finally {
    setPublishing(false);
  }
};

const handleViewDetails = (assignment: Assignment) => {
  setSelectedAssignment(assignment);
  setFormMode('view');
  setShowAssignmentForm(true);
};

const handleViewSubmissions = (assignment: Assignment) => {
  setSubmissionsAssignment(assignment);
  setShowSubmissionsView(true);
};

  return (
    <div className="px-4">
                  {showSubmissionsView ? (
                    <TeacherSubmissionsView
                      assignment={submissionsAssignment}
                      assignmentId={submissionsAssignment?.id || ''}
                      classId={classId || ''}
                      onBack={() => {
                        setShowSubmissionsView(false);
                        setSubmissionsAssignment(null);
                      }}
                    />
                  ) : showAssignmentForm ? (
                    <AssignmentForm
                      classId={classId || ""}
                      assignment={formMode === 'view' ? selectedAssignment : editingAssignment}
                      mode={formMode}
                      onSubmit={() => {
                        setShowAssignmentForm(false);
                        setEditingAssignment(null);
                        setSelectedAssignment(null);
                        fetchAssignments();
                      }}
                      onClose={() => {
                        setShowAssignmentForm(false);
                        setEditingAssignment(null);
                        setSelectedAssignment(null);
                      }}
                      onBack={() => {
                        setShowAssignmentForm(false);
                        setEditingAssignment(null);
                        setSelectedAssignment(null);
                      }}
                    />
                  ) : (
                    <>
                      <div className="mb-4 md:mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                        <Button onClick={() => {
                          setFormMode('create');
                          setSelectedAssignment(null);
                          setEditingAssignment(null);
                          setShowAssignmentForm(true);
                        }} className="bg-purple-600 hover:bg-purple-700" disabled={currentClass?.completed}>
                          <Plus className="h-4 w-4 mr-2" />
                          Create Assignment
                        </Button>
                      </div>
                      
                         {classAssignments.length > 0 ? <AssignmentList assignments={classAssignments} classId={classId || ''} currentEnrollment={currentClass?.currentEnrollment} onSubmit={handleSubmitAssignment}
                      onDelete={handleDeleteAssignment}
                      onEdit={(assignment) => {
                        setEditingAssignment(assignment);
                        setFormMode('edit');
                        setShowAssignmentForm(true);
                      }}
                      onPublish={handlePublishAssignment}
                      onViewDetails={handleViewDetails}
                      onViewSubmissions={handleViewSubmissions}
                      /> : <div className="p-8 md:p-12 flex flex-col items-center justify-center">
                              <img src="/lovable-uploads/6829d795-39c9-4216-a398-3b7ada11f608.png" alt="No data" className="w-16 h-16 md:w-24 md:h-24 object-contain mb-4" />
                              <p className="text-base md:text-lg font-medium mb-1">No assignments found</p>
                              <p className="text-xs md:text-sm text-gray-500 mb-4">Create new assignments for your class</p>
                              <Button onClick={() => setShowAssignmentForm(true)} className="bg-purple-600 hover:bg-purple-700" disabled={currentClass?.completed}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Assignment
                      </Button>
                            </div>}
                    </>
                  )}
                </div>
  );
};

export default AssignmentsTab;
