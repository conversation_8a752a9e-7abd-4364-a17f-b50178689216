
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Clock, Calendar } from "lucide-react";
import { format, parse } from "date-fns";

interface ClassCardProps {
  image: string;
  subject: string;
  grade: string;
  duration: string;
  title: string;
  timezone: string;
  date: string;
  time: string;
  endTime: string;
  studentCount: number;
  isNow?: boolean;
  endDate?: string;
  onClick?: () => void;
}

export const ClassCard: React.FC<ClassCardProps> = ({
  image,
  subject,
  timezone,
  grade,
  duration,
  title,
  date,
  time,
  endTime,
  studentCount,
  isNow = false,
  endDate,
  onClick
}) => {
  const isExpired = (() => {
    const sessionEndDateTime = new Date(`${date} ${endTime}`);
    return sessionEndDateTime < new Date();
  })();

  return (
    <div className={`${isExpired ? 'bg-gray-100' : 'bg-white'}  border border-border rounded-lg hover:shadow-md transition-shadow`} onClick={onClick}>
      {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-xs font-semibold text-primary bg-primary/10 px-2 py-1 rounded">
              {subject}
            </span>
            <span className="text-xs text-muted-foreground">{grade}</span>
          </div>
          {isNow && (
            <span className="text-xs font-semibold bg-green-500 text-white px-2 py-1 rounded flex items-center gap-1">
              <span className="w-1.5 h-1.5 bg-white rounded-full"></span>
              LIVE
            </span>
          )}
        </div>
      <div className="p-4">
        <h3 className={`font-semibold text-sm mb-3 line-clamp-2 leading-tight ${isExpired ? 'text-gray-500' : ''}`}>{title}</h3>
        {/* Date & Time */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>{date}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>{time} - {(timezone)}</span>
            <span className="text-xs">{duration}</span>
          </div>
        </div><div className="flex justify-between items-center">
         
          {isExpired ? (
            <span className="text-xs text-gray-500">Completed</span>
          ) : isNow ? (
            <Button size="sm" className="bg-purple-600 hover:bg-purple-700 text-xs py-1">Join Now</Button>
          ) : (
            (() => {
              const today = new Date();
              const todayStr = today.getFullYear() + '-' + 
                String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                String(today.getDate()).padStart(2, '0');
              
              const diffDays = date === todayStr ? 0 : 
                new Date(date).getTime() > new Date(todayStr).getTime() ? 
                Math.ceil((new Date(date).getTime() - new Date(todayStr).getTime()) / (1000 * 60 * 60 * 24)) : -1;
              
              let label = '';
              if (diffDays === 0) label = 'Today';
              else if (diffDays === 1) label = 'Tomorrow';
              else if (diffDays === 2) label = 'In 2 days';
              else if (diffDays === 3) label = 'In 3 days';
              
              return label && (
                <Button size="sm" variant="outline" className="text-purple-600 border-purple-600 hover:bg-purple-50 text-xs py-1 whitespace-nowrap">
                  {label}
                </Button>
              );
            })()
          )}
        </div>
      </div>
    </div>
  );
};
