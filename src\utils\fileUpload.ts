import { toast } from "sonner";
/**
 * Uploads files to the syllabus service.
 * @param accessToken - User access token
 * @param files - Array of File objects
 * @param options - Optional description/tags
 * @returns Promise of uploaded file info array
 */
export async function fileUpload(
  accessToken: string,
  files: File[],
  options?: { 
    fileId?: string;
    description?: string;
    tags?: string;
    relatedEntityType?: string;
    fileCategory?: string;
  }
): Promise<any[]> {
  const uploadedFiles = [];
  for (const file of files) {
    const formData = new FormData();
    formData.append("file", file);
    if (options?.fileId) formData.append("fileId", options.fileId);
    if (options?.description) formData.append("description", options.description);
    if (options?.tags) formData.append("tags", options.tags);
    formData.append("relatedEntityType", options?.relatedEntityType || "syllabus");
    formData.append("fileCategory", options?.fileCategory || "doc");

    const response = await fetch(`/api/file/v1/files`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`
      },
      body: formData
    });

    if (response.status === 401) {
      window.location.href = "/";
      return [];
    }
    if (response.status === 413) {
      const errorData = await response.json();
      toast.error("Maximum upload size exceeded. Please upload a smaller file.");
      throw new Error("File size too large. Please upload a smaller file.");
    }
    if (!response.ok) {
      throw new Error("Failed to upload file");
    }
    const result = await response.json();
    uploadedFiles.push(result);
  }
  return uploadedFiles;
}


export async function fileDownload(
  accessToken: string,
  fileId: string,
  fileName: string
): Promise<void> {
  try {
    const response = await fetch(`/api/file/v1/files/${fileId}/download`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`
      }
    });
    
    if (!response.ok) {
      throw new Error("Failed to download file");
    }
    
    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = downloadUrl;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    a.remove();
    window.URL.revokeObjectURL(downloadUrl);

    // Show success message
    toast.success("File downloaded successfully!");
    // Or use your toast library: toast.success("File downloaded successfully!");
  } catch (error) {
    // Show error message
    toast.error("Failed to download file.");
    // Or use your toast library: toast.error("Failed to download file.");
  }
}

export async function downloadAndCreateImageUrl(
  accessToken: string,
  fileId: string
): Promise<string | null> {
  try {
    const response = await fetch(`/api/file/v1/files/${fileId}/download`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`
      }
    });
    
    if (!response.ok) {
      throw new Error("Failed to download image");
    }
    
    const blob = await response.blob();
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error("Failed to download image:", error);
    return null;
  }
}
