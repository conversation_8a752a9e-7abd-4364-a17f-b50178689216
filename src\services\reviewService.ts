
import reviewsData from '../data/reviews.json';

interface Review {
  id: string;
  studentName: string;
  courseName: string;
  studentType: string;
  avatarInitials: string;
  avatarColor: string;
  rating: number;
  reviewDate: string;
  reviewText: string;
  helpfulCount: number;
}

interface ReviewsData {
  averageRating: number;
  totalReviews: number;
  reviews: Review[];
}
export const getReviewsData =async ( accessToken: string) => {
  const response = await fetch(`/api/teacherReview/v1/teacher/reviews`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetchReviews");
  }
  return response.json();
};
export const getTeacherReviews =async ( accessToken: string) => {
  const response = await fetch(`/api/teacherReview/v1/teacher/reviews`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetchReviews");
  }
  return response.json();
};

export const getStudentReviews =async ( accessToken: string) => {
  const response = await fetch(`/api/teacherReview/v1/student/reviews`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetchReviews");
  }
  return response.json();
};

export const getTeacherReviewSummary =async ( accessToken: string) => {
const response = await fetch(`/api/teacherReview/v1/teacher/summary`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetchReviews Summary");
  }
  return response.json();
};

export const createTeacherReview = async (reviewData: any, accessToken: string) => {
  const response = await fetch('/api/teacherReview/v1/reviews', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(reviewData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error('Failed to create review');
  }
  return response.json();
};

export const editTeacherReview = async (reviewId: string, reviewData: any, accessToken: string) => {
  const response = await fetch(`/api/teacherReview/v1/reviews/${reviewId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(reviewData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error('Failed to update review');
  }
  return response.json();
};

