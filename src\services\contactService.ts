export async function sendContactMessage(messageData: { name: string; email: string; message: string }) {
  const response = await fetch("/api/contact/v1/messages", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(messageData)
  });
  if (response.status === 401) {
  //  window.location.href = "/";
    //return;
  }
  if (!response.ok) {
    throw new Error("Failed to send message");
  }
  return response.json();
}