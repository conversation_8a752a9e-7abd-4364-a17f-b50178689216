import React, { useState } from "react";
import { ArrowLeft, Globe, Check, X } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
 import { useAuth } from "react-oidc-context"; // Updated import
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import ProfileSidebar from "@/components/profile/ProfileSidebar";
import { toast } from "sonner";

const TIMEZONES = [
  { value: "UTC", label: "UTC" },
  { value: "America/New_York", label:"America/New_York" },
  { value: "America/Chicago", label: "America/Chicago" },
  { value: "America/Denver", label:"America/Denver" },
  { value: "America/Los_Angeles", label:  "America/Los_Angeles" },
  { value: "America/Anchorage", label: "America/Anchorage" },
  { value: "Pacific/Honolulu", label: "Pacific/Honolulu"},
  { value: "Europe/London", label: "Europe/London" },
  { value: "Europe/Paris", label: "Europe/Paris" },
  { value: "Europe/Berlin", label: "Europe/Berlin" },
  { value: "Europe/Rome", label:  "Europe/Rome" },
  { value: "Europe/Madrid", label:"Europe/Madrid"},
  { value: "Europe/Moscow", label: "Europe/Moscow" },
  { value: "Asia/Dubai", label:  "Asia/Dubai"},
  { value: "Asia/Kolkata", label:  "Asia/Kolkata" },
  { value: "Asia/Shanghai", label:"Asia/Shanghai" },
  { value: "Asia/Tokyo", label: "Asia/Tokyo" },
  { value: "Asia/Seoul", label:"Asia/Seoul" },
  { value: "Australia/Sydney", label:  "Australia/Sydney" },
  { value: "Australia/Melbourne", label: "Australia/Melbourne" },
  { value: "Australia/Perth", label: "Australia/Perth" },
  { value: "Pacific/Auckland", label:"Pacific/Auckland" },
];
 import { updateProfile } from "@/services/profileService";
import { useUserRole } from "@/hooks/useUserRole";

 import { useSelector } from "react-redux";
 import { generateAvatarUrl } from "@/lib/utils";
import { UserRole } from "@/types";

export default function SettingsPage() {
     const { selectedRole, userTimezone, updateUserTimezone } = useUserRole();  

  const getValidTimezone = () => {
    
    const cookieTimezone = userTimezone;
    return TIMEZONES.find(tz => tz.value === cookieTimezone)?.value || 'UTC';
  };
  
  const [timezone, setTimezone] = useState(getValidTimezone());
  const [tempTimezone, setTempTimezone] = useState(getValidTimezone());
  const [isEditing, setIsEditing] = useState(false);
  const auth = useAuth();
  const userData = useSelector((state: any) => state.user.userData);
  console.log(auth.user.profile.email);
  const user = userData
  ? {
      id: userData.id,
      name: userData.name || "User",
      email: userData.email || "",
      role: userData.roles?.[0]?.role || "User", // or adjust as needed
      avatar: generateAvatarUrl(userData.name || "User", "3498db"),
    }
  : null;
  const handleSaveTimezone = async () => {
    try {
      if (!auth.user?.access_token) {
        toast.error("Authentication required");
        return;
      }
      
      await updateProfile({ 
        name: user?.name,
        phone: userData?.phone,
        email: auth.user.profile.email,
        timezone: tempTimezone 
      }, auth.user.access_token, user?.id || '');
      
      updateUserTimezone(tempTimezone);
      setTimezone(tempTimezone);
      setIsEditing(false);
      toast.success("Timezone updated successfully");
    } catch (error) {
      toast.error("Failed to update timezone");
    }
  };

  const handleCancel = () => {
    setTempTimezone(timezone);
    setIsEditing(false);
  };

  const getCurrentTimezoneLabel = () => {
    const tz = TIMEZONES.find(t => t.value === timezone);
    return tz ? tz.label : timezone;
  };

  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleString('en-US', {
      timeZone: timezone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };
  const navigate = useNavigate();
  
  const handleBackClick = () => {
    const route = (() => {
      switch (selectedRole) {
        case UserRole.TEACHER:
          return "/teacher-dashboard";
        case UserRole.STUDENT:
          return "/student-dashboard";
        case UserRole.PARENT:
          return "/parent-dashboard";
        default:
          return "/dashboard";
      }
    })();
    navigate(route);
  };
    

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="p-4 bg-white">
        <div className="flex items-center gap-2">
         <Button 
            variant="ghost" 
            size="icon" 
            onClick={handleBackClick}
            className="text-gray-600 hover:bg-gray-100"
          >
            <ArrowLeft className="h-6 w-6" />
          </Button>
          <h1 className="text-lg font-medium">Settings</h1>
        </div>
      </div>

      <div className="container mx-auto max-w-4xl py-6 px-4">
        <div className="grid grid-cols-12 gap-6">
          {/* Sidebar */}
          <div className="col-span-12 md:col-span-3">
            <ProfileSidebar activePage="settings" />
          </div>

          {/* Main Content */}
          <div className="col-span-12 md:col-span-9 space-y-6">
            {/* Timezone Settings */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Globe className="h-5 w-5 text-primary" />
                  <CardTitle>Timezone Settings</CardTitle>
                </div>
                <CardDescription>
                  Set your preferred timezone for all dates and times displayed in the application.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="timezone">Current Timezone</Label>
                  {isEditing ? (
                    <div className="space-y-3">
                      <Select value={tempTimezone} onValueChange={setTempTimezone}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a timezone" />
                        </SelectTrigger>
                        <SelectContent className="max-h-60">
                          {TIMEZONES.map((tz) => (
                            <SelectItem key={tz.value} value={tz.value}>
                              {tz.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <div className="flex items-center gap-2 justify-end">
                        <Button
                          type="button"
                           variant="outline"
           
                        //  className="flex items-center gap-1 text-red-600 border-red-600 hover:bg-red-50"
                          onClick={handleCancel}
                        >
                          <X className="h-4 w-4" />
                          Cancel
                        </Button>
                        <Button
                           className="bg-purple-600 hover:bg-purple-700 flex items-center gap-2 rounded-full "
                          onClick={handleSaveTimezone}
                        >
                          <Check className="h-4 w-4" />
                          Save Changes
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between p-3 border rounded-lg bg-muted/30">
                      <div>
                        <p className="font-medium">{getCurrentTimezoneLabel()}</p>
                        <p className="text-sm text-muted-foreground">
                          Current time: {getCurrentTime()}
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsEditing(true)}
                      >
                        Change
                      </Button>
                    </div>
                  )}
                </div>

                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 rounded-full bg-blue-500 mt-2 flex-shrink-0"></div>
                    <div className="text-sm text-blue-800">
                      <p className="font-medium mb-1">Important Note:</p>
                      <p>
                        This timezone setting will be used across all features including schedules, 
                        meetings, assignments, and announcements. Changes will take effect immediately.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Settings Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle>More Settings</CardTitle>
                <CardDescription>
                  Additional settings will be available here in future updates.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <p>Additional preference settings coming soon...</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}