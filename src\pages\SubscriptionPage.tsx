import { useState } from "react";
import {getFeatures} from '@/services/membershipService';

import Header from "@/components/layout/Header";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Check, Info, Mail, PhoneCall, Building } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Link } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import Footer from "@/components/Footer"; // or your preferred toast library
import { toast } from "sonner";
import { useAuth } from "react-oidc-context";
import {Sparkles} from "lucide-react";
import { useEffect } from "react";

export default function SubscriptionPage() {
  const [billingPeriod, setBillingPeriod] = useState<"monthly" | "yearly">("monthly");
    const auth = useAuth();
      const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleSignUp  = async () => {
   // window.location.href = "https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/signup?client_id=76u1v7el416ebllhpbhtqpmlh0&code_challenge=Cjh7j5XvSKwPZ5ahIhP5j2tuEvZiuoSm811Q62N0wFs&code_challenge_method=S256&redirect_uri=http%3A%2F%2Flocalhost%3A8081%2Flogin%2Foauth2%2Fcode%2Fcognito&response_type=code&scope=email+openid+phone&state=80e73e7091c04c30a0c4904373b2096f";
     setIsSubmitting(true);
    
    try {
      // Store form values in localStorage to access after redirect back from Cognito
    //  localStorage.setItem("registerFormData", JSON.stringify(form.getValues()));
      
      // Redirect to Cognito signup page
      await auth.signinRedirect({ prompt: "login" });
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };
  const plans = [
    {
      name: "Starter",
      description: "Perfect for new teachers with small classes",
      price: billingPeriod === "monthly" ? 29 : 290,
      features: [
        "Up to 30 students",
        "Basic messaging",
        "Class management",
        "Basic progress tracking",
        "Payment reminders"
      ],
      maxStudents: 30,
      popular: false
    },
    {
      name: "Professional",
      description: "Ideal for established teachers with growing classes",
      price: billingPeriod === "monthly" ? 79 : 790,
      features: [
        "Up to 100 students",
        "Advanced messaging & announcements",
        "Class management with scheduling",
        "Detailed progress tracking",
        "Payment processing",
        "Parent communication tools",
        "Resource sharing"
      ],
      maxStudents: 100,
      popular: true
    },
    {
      name: "Enterprise",
      description: "For educational institutions and large teaching networks",
      price: billingPeriod === "monthly" ? 199 : 1990,
      features: [
        "Unlimited students",
        "All Professional features",
        "Multi-teacher management",
        "Advanced analytics & reporting",
        "API access",
        "Priority support",
        "Custom branding",
        "Enterprise-grade security"
      ],
      maxStudents: Infinity,
      popular: false
    }
  ];
  const [features, setFeatures] = useState([]);
   
     useEffect(() => {
    const loadFeatures = async () => {
      try {
        const response = await getFeatures();
        if (response?.features && Array.isArray(response.features)) {
          const mappedFeatures = response.features.map(f => ({
            id: f.id,
            name: f.name,
            description: f.description,
            monthlyPrice: f.monthlyPrice,
            yearlyPrice: f.yearlyPrice,
            isFree: f.free,
            isDefault: f.default
          }));
          setFeatures(mappedFeatures);
        }
      } catch (error) {
        console.error('Failed to load features:', error);
      }
    };
          loadFeatures();
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-white to-edu-light">
      <Header />
      
       {/* Hero Section with gradient background */}
        <section className="py-16 md:py-24 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-blue-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto relative z-10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
              <div className="max-w-2xl text-center md:text-left">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white leading-tight">
                  Transform <span className="text-yellow-300">Education</span> Through Innovation
                </h1>
                <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
                  Join thousands of educators and students in revolutionizing the learning experience
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <Button asChild size="lg" className="w-full sm:w-auto bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full shadow-lg">
                    <Link to="/register">Get Started Free</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="w-full sm:w-auto border-2 bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full">
                    <Link to="/subscription">View Pricing</Link>
                  </Button>
                </div>
                <div className="mt-12 flex flex-col sm:flex-row items-center justify-center md:justify-start gap-6">
                  <div className="flex -space-x-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="w-12 h-12 rounded-full border-4 border-purple-600 bg-white shadow-lg flex items-center justify-center text-purple-600 font-bold text-lg">
                        {String.fromCharCode(64 + i)}
                      </div>
                    ))}
                  </div>
                  <p className="text-lg text-white/90">
                    Joined by <span className="font-bold text-yellow-300">2000+</span> educators
                  </p>
                </div>
              </div>
              <div className="w-full md:w-2/5 relative">
                <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-transform duration-500">
                  <img 
                    src="https://images.unsplash.com/photo-1571260899304-425eee4c7efc?q=80&w=2070&auto=format&fit=crop" 
                    alt="EduConnect Platform" 
                    className="w-full h-64 md:h-96 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800">Modern Learning Experience</h3>
                    <p className="text-gray-600">Interactive tools for better engagement</p>
                  </div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-xl shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <p className="text-lg font-bold flex items-center gap-2">
                    <Sparkles className="h-5 w-5" />
                    AI-Powered Learning
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>


        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-16 mt-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Explore Our <span className="text-edu-blue">Features</span>
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Discover the powerful features that make EduConnect the perfect platform for modern education.
            </p>
          </div>

          <div className="flex justify-center mb-10">
            <Tabs defaultValue="monthly" className="w-64">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger 
                  value="monthly" 
                  onClick={() => setBillingPeriod("monthly")}
                >
                  Monthly
                </TabsTrigger>
                <TabsTrigger 
                  value="yearly" 
                  onClick={() => setBillingPeriod("yearly")}
                >
                  Yearly <span className="ml-1 text-xs text-edu-green">(Save 15%)</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature) => (
              <Card key={feature.id} className={`flex flex-col ${feature.isDefault ? 'border-edu-blue shadow-lg relative' : ''}`}>
                {feature.isDefault && (
                  <div className="absolute top-0 right-0 bg-edu-blue text-white px-3 py-1 text-sm font-medium rounded-bl-lg rounded-tr-lg">
                    Default
                  </div>
                )}
                
                <CardHeader>
                  <CardTitle className="text-2xl">{feature.name}</CardTitle>
                  <CardDescription>{feature.description}</CardDescription>
                </CardHeader>
                
                <CardContent className="flex-grow">
                  <div className="mb-6">
                    {feature.isFree ? (
                      <span className="text-4xl font-bold text-green-600">Free</span>
                    ) : (
                      <>
                        <span className="text-4xl font-bold">
                          ${billingPeriod === "monthly" ? feature.monthlyPrice : feature.yearlyPrice}
                        </span>
                        <span className="text-gray-500">/{billingPeriod === "monthly" ? "month" : "year"}</span>
                      </>
                    )}
                  </div>
                </CardContent>
                
                <CardFooter>
                  <Button 
                    className={feature.isDefault ? "w-full bg-purple-600 hover:bg-purple-700 ml-auto" : "w-full bg-purple-600 hover:bg-purple-700 ml-auto"} 
                    size="lg"
                    disabled={feature.isFree}
                  >
                    {feature.isFree ? "Included" : `Get ${feature.name}`}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          <div className="mt-16 bg-white p-8 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Frequently Asked Questions</h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Can I switch plans at any time?</h3>
                <p className="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes will be applied immediately, with any difference in price prorated.</p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-2">What happens if I exceed my student limit?</h3>
                <p className="text-gray-600">You'll receive a notification when you're approaching your student limit. You can upgrade to a higher plan to accommodate more students or contact us for a custom solution.</p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-2">Is there a free trial available?</h3>
                <p className="text-gray-600">Yes, we offer a 14-day free trial for all plans so you can explore the platform and determine which plan best fits your needs.</p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-2">Do you offer discounts for educational institutions?</h3>
                <p className="text-gray-600">Yes, we offer special pricing for schools, colleges, and educational institutions. Please contact our sales team for more information.</p>
              </div>
            </div>
          </div>

          <div className="mt-16">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-4">Need a Custom Solution?</h2>
              <p className="text-lg text-gray-600">
                We offer tailored solutions for specific educational needs and larger institutions.
              </p>
            </div>

            <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="bg-edu-blue/10 p-3 rounded-full">
                    <Mail className="h-6 w-6 text-edu-blue" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Enterprise Sales</h3>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="bg-edu-blue/10 p-3 rounded-full">
                    <PhoneCall className="h-6 w-6 text-edu-blue" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Sales Hotline</h3>
                    <p className="text-gray-600">+****************</p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="bg-edu-blue/10 p-3 rounded-full">
                    <Building className="h-6 w-6 text-edu-blue" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">For Educational Institutions</h3>
                    <p className="text-gray-600">Special pricing and custom solutions available</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-lg p-6">
                <form className="space-y-4">
                  <div>
                    <Label htmlFor="institutionName">Institution Name</Label>
                    <Input id="institutionName" placeholder="Your institution name"  className="edu-form-field"/>
                  </div>
                  <div>
                    <Label htmlFor="name">Contact Person</Label>
                    <Input id="name" placeholder="Your full name"  className="edu-form-field"/>
                  </div>
                  <div>
                    <Label htmlFor="email">Work Email</Label>
                    <Input id="email" type="email" placeholder="<EMAIL>" className="edu-form-field" />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" type="tel" placeholder="+****************"  className="edu-form-field"/>
                  </div>
                  <div>
                    <Label htmlFor="studentsCount">Number of Students</Label>
                    <Input id="studentsCount" type="number" placeholder="Approximate number of students" className="edu-form-field" />
                  </div>
                  <div>
                    <Label htmlFor="message">Additional Requirements</Label>
                    <Textarea
                      id="message"
                      placeholder="Tell us about your specific needs and requirements"
                      className="h-32"
                    />
                  </div>
                  <Button className="w-full bg-purple-600 hover:bg-purple-700 ml-auto">
                    Contact Sales Team
                  </Button>
                </form>
              </div>
            </div>
          </div>
        </div>
      
       <footer className="bg-gray-900 text-white py-8 md:py-12 mt-4">
        <Footer/>
      </footer>
    </div>
  );
}
