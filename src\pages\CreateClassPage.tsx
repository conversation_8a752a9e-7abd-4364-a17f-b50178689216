
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { ChevronRight, ChevronLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useApp } from "@/context/AppContext";
import { useAuth } from "react-oidc-context"; // Updated import
import { ClassType, UserRole } from "@/types";
import StepOneForm from "@/components/class/StepOneForm";
import StepTwoForm from "@/components/class/StepTwoForm";
import { toast } from "sonner";
import { generateAvatarUrl } from "@/lib/utils";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createClassService } from "@/services/classService";
import { createClassScheduleService  ,createClassFeeService} from "@/services/scheduleService";

import { useSelector } from "react-redux";
import ClassSchedule from "@/components/class/ClassSchedule";
import FeeForm from "@/components/class/FeeForm";

export default function CreateClassPage() {
  const navigate = useNavigate();
  const auth = useAuth(); // Use OIDC auth
  const { createClass } = useApp();
  const [currentStep, setCurrentStep] = useState(1);
  const userData = useSelector((state: any) => state.user.userData);
  const [classroomId, setClassroomId] = useState<string | null>(null);
  const [isFormValid, setIsFormValid] = useState(false);
  const [validateForm, setValidateForm] = useState<(() => boolean) | null>(null);
  const [validationTriggered, setValidationTriggered] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    className: "",
    subjectName:"",
    description:"",
    courseTitle: "",
    level: 10,
    batchName :"",
    teacherId : "",
    capacity : 1,
    students :[],
    parents :[],
    assignments:[],
    announcements : [],
     classType: ClassType.ONLINE,
    image: "",
    startDate: new Date(),
    endDate: new Date(),
    daysOfWeek: [] as string[],
    startTime: "",
    endTime: "",
    recurring: false,
    demoVideoURL: "",
    posterFileId:"",
    onlineMeeting: {
      platform: "",
      link: ""
    }
  });

  // Get user from OIDC
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;

  // Ensure validation is set up
  useEffect(() => {
    if (!validateForm) {
      // Set a default validation function if none is provided
      setValidateForm(() => () => {
        const stringFields = ['className', 'subjectName', 'classType', 'batchName', 'demoVideoURL', 'description'];
        const numberFields = ['capacity', 'level'];
        
        const stringValid = stringFields.every(field => {
          const value = formData[field as keyof typeof formData] as string;
          return value && value.trim().length > 0;
        });
        
        const numberValid = numberFields.every(field => {
          const value = formData[field as keyof typeof formData] as number;
          return value && value > 0;
        });
        
        return stringValid && numberValid;
      });
    }
  }, [formData, validateForm]);
  const [scheduleData, setScheduleData] = useState({
    classroomId: "", // if available
    sessionType:"",
    startDate: new Date(),
    endDate: new Date(),
    daysOfWeek: [] as string[],
    sessionStartTime: "",
    sessionEndTime: "",
    recurrenceType: "",
    meetingLink: "",
    selectedDays: [] as string[]
  });
 // Form state
  const [feeFormData, setFeeFormData] = useState({
    feeAmount:0,
    paymentType:"",
    countryCode:"",
    description: "", // Optional field for description
    discount: 0 // Optional field for discount
  });

function formatDate(date: Date | string) {
  if (!date) return "";
  if (typeof date === "string") return date.split("T")[0]; // handles ISO strings
  return date.toISOString().split("T")[0];
}
  const updateFormData = (data: Partial<typeof formData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };
  const updateFeeFormData = (data: any) => {
    setFeeFormData(prev => ({
      ...prev,
      ...data
    }));
  };
  const handleNext = () => {
    // Use StepOneForm validation
    if (currentStep === 1) {
      // Trigger validation and check if form is valid
      setValidationTriggered(true);
      if (!validateForm || !validateForm()) {
        return; // Validation errors will be shown inline
      }
      
      // Only proceed if validation passes
        const newClass = {
          className: formData.className,
          subjectName: formData.subjectName,
          classType: formData.classType.toUpperCase(),
          description: formData.description || "",
          level: formData.level,
          batchName: formData.batchName,
          posterFileId :formData.posterFileId,
          //teacherId: userData?.id?.replace(/\"/g, ''),
          capacity: formData.capacity
          //image: formData.image
        };
       
        const handleCreateClass = async () => {
          try {
            const result = await createClassService(newClass, auth.user.access_token);
            //  const data = await result.json();  
            setClassroomId(result.id);
          } catch (error) {
        console.error("Error:", error);
          }
        };      
        handleCreateClass();
        toast.success("Class created successfully!");
        navigate("/teacher-dashboard");

    }
};
  const handlePrevious = () => {

    setCurrentStep(prev => Math.max(prev - 1,1));

  };

  const handleSubmit = () => {
    if (!user) {
      toast.error("You must be logged in to create a class");
      return;
    }
       // Basic validation for fee form
      if (feeFormData.feeAmount <= 0 || !feeFormData.paymentType || !feeFormData.countryCode) {
        toast.error("Please fill out all required fields in the fee form");
        //return;
      }else{
        const newFeeData = {
          classroomId: classroomId, // if available
          feeAmount: feeFormData.feeAmount,
          paymentType: feeFormData.paymentType.toUpperCase(), 
          countryCode: feeFormData.countryCode,
          description: feeFormData.description || ""//, // Optional field for description
          //discount: feeFormData.discount || 0 // Optional field for discount
        };
        const handleCreateFee = async () => {
          try {
            console.log(newFeeData);
            const result = await createClassFeeService(newFeeData, auth.user.access_token);
          } catch (error) {
            console.error("Error:", error);
          }
        };
        handleCreateFee();
      }
      console.log("Fee Data:", feeFormData);
   
    
    toast.success("Class created successfully!");
    navigate("/teacher-dashboard");
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-grow">
        {/* Header */}
        <header className="bg-white p-6 flex justify-between items-center border-b border-gray-200">
          <div className="flex items-center">
            <button 
              onClick={() => navigate("/teacher-dashboard")}
              className="mr-4 text-gray-500 hover:text-gray-700"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <h1 className="text-2xl font-semibold">Create New Class</h1>
          </div>
        </header>
         <Card className="border border-gray-200 shadow-sm">
            <CardContent className="pt-6">
              {currentStep === 1 && (
                <StepOneForm 
                  formData={formData} 
                  updateFormData={updateFormData}
                  validationTriggered={validationTriggered}
                  onValidationChange={(isValid, validate) => {
                    setIsFormValid(isValid);
                    setValidateForm(() => validate);
                  }}
                />
              )}

              {/* Navigation buttons */}
              <div className="flex justify-between mt-8">
                

                {currentStep === 1 && (
                <Button
                      onClick={handleNext}
                      className="bg-purple-600 hover:bg-purple-700 ml-auto flex items-center gap-2 rounded-full px-8"
                    >
                      Create Class <ChevronRight className="h-4 w-4" />
                    </Button>
              )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    
  );
}
