import React from 'react';
import { Search, ChevronRight } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { ChatContact } from '@/services/chatService';

interface ChatContactListProps {
  contacts: ChatContact[];
  selectedContactId: string | null;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onContactSelect: (contactId: string) => void;
  showSelection?: boolean;
}

export const ChatContactList: React.FC<ChatContactListProps> = ({
  contacts,
  selectedContactId,
  searchTerm,
  onSearchChange,
  onContactSelect,
  showSelection = true
}) => {
  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    }).format(date);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search contacts"
            className="edu-form-field pl-9"
          
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
      </div>

      <div className="overflow-y-auto flex-grow">
        {filteredContacts.map((contact) => (
          <div
            key={contact.id}
            className={`flex items-center gap-3 p-4 cursor-pointer transition-colors hover:bg-gray-50 ${
              showSelection && selectedContactId === contact.id
                ? 'bg-purple-50 border-l-4 border-purple-500'
                : ''
            }`}
            onClick={() => onContactSelect(contact.id)}
          >
            <div className="relative">
              <Avatar className="h-12 w-12">
                <AvatarImage src={contact.avatar} alt={contact.name} />
                <AvatarFallback>{contact.name[0]}</AvatarFallback>
              </Avatar>
              {contact.isOnline && (
                <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
              )}
            </div>
            
            <div className="flex-grow overflow-hidden">
              <div className="flex items-center justify-between">
                <h3 className="font-medium truncate">{contact.name}</h3>
                <span className="text-xs text-gray-500">
                  {formatTime(contact.lastMessageTime)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600 truncate">{contact.lastMessage}</p>
                {contact.unreadCount > 0 && (
                  <span className="bg-purple-600 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                    {contact.unreadCount}
                  </span>
                )}
              </div>
            </div>
            
            {!showSelection && (
              <ChevronRight className="h-4 w-4 text-gray-400" />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};