
import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ManualAssignmentForm } from '@/components/assignment/ManualAssignmentForm';
import { AIAssignmentForm } from '@/components/assignment/AIAssignmentForm';
import { Button } from '@/components/ui/button';
import { <PERSON>rkles, PencilLine, ArrowLeft } from 'lucide-react';
import AssignmentForm from "@/components/assignment/AssignmentForm";
import { useApp } from "@/context/AppContext";
import { toast } from "sonner";

const CreateAssignmentPage = () => {
  const { classId } = useParams<{ classId: string }>();
  const navigate = useNavigate();
    const { createAssignment } = useApp();

  const [activeTab, setActiveTab] = useState('ai');
    const handleCreateAssignment = (assignmentData: any) => {
    createAssignment({
      ...assignmentData,
      submittedBy: []
    });
    toast.success("Assignment created successfully");
    navigate(`/class/${classId}`);
  };

  const handleBack = () => {
    navigate(`/class/${classId}`);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Button 
        variant="ghost" 
        className="mb-6"
          onClick={() => navigate(`/class/${classId}`)}
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Assignments
      </Button>

      <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-white">
          <h1 className="text-white mb-2">Create New Assignment</h1>
          <p className="text-blue-100">Choose your preferred method to create an engaging assignment for your students</p>
        </div>

        {/* Tabs for creation methods */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="border-b border-gray-200 bg-gray-50/50 px-8 pt-6">
            <TabsList className="grid w-full max-w-md mx-auto grid-cols-2 h-auto p-1 bg-white border border-gray-200">
              <TabsTrigger 
                value="ai" 
                className="flex items-center gap-2 py-3 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-purple-600 data-[state=active]:text-white"
              >
                <Sparkles className="w-4 h-4" />
                AI-Assisted
              </TabsTrigger>
              <TabsTrigger 
                value="manual" 
                className="flex items-center gap-2 py-3 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-purple-600 data-[state=active]:text-white"
              >
                <PencilLine className="w-4 h-4" />
                Manual Creation
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="p-8">
            <TabsContent value="ai" className="mt-0">
              <AIAssignmentForm onSuccess={handleBack} />
            </TabsContent>

            <TabsContent value="manual" className="mt-0">
              <ManualAssignmentForm onSuccess={handleBack} />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default CreateAssignmentPage;
