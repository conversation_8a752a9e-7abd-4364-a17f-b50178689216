
import React, { useState ,useEffect} from "react";
import { useApp } from "@/context/AppContext";
import { useAuth } from "react-oidc-context"; // Updated import
import { format } from "date-fns";
import { 
  Check, 
  X, 
  Clock, 
  Calendar,
  ArrowLeft,
  CalendarRange
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Link, useNavigate } from "react-router-dom";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { getClassesForStudent, ClassData } from "@/services/classService";
import { useUserRole } from "@/hooks/useUserRole";
import  {getAttendanceByStudentId} from "@/services/attendanceService";
import { useDispatch, useSelector } from "react-redux";
// Dummy data for attendance records
const dummyAttendanceRecords = [
  {
    id: "1",
    date: "2025-04-15",
    classId: "class1",
    status: "present",
    notes: "Participated actively in class discussion"
  },
  {
    id: "2",
    date: "2025-04-14",
    classId: "class1",
    status: "late",
    notes: "Arrived 10 minutes late"
  },
  {
    id: "3",
    date: "2025-04-13",
    classId: "class2",
    status: "absent",
    notes: "Medical appointment"
  },
  {
    id: "4",
    date: "2025-04-12",
    classId: "class1",
    status: "present",
    notes: "Excellent participation"
  },
  {
    id: "5",
    date: "2025-04-11",
    classId: "class2",
    status: "excused",
    notes: "Family event"
  },
  {
    id: "6",
    date: "2025-04-10",
    classId: "class1",
    status: "present",
    notes: "Completed all assignments"
  },
  {
    id: "7",
    date: "2025-04-09",
    classId: "class2",
    status: "present",
    notes: "Group presentation day"
  }
];

// Dummy class data
const dummyClasses = [
  { id: "class1", name: "Advanced Mathematics" },
  { id: "class2", name: "Physics 101" }
];


export default function StudentAttendancePage() {
 const auth = useAuth();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { selectedRole } = useUserRole();

const userId = useSelector((state: any) => state.user.userId);
  
  const [selectedClass, setSelectedClass] = useState<string>("all");
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
      const [availableClasses, setAvailableClasses] = useState<ClassData[]>([]);
  const [attendanceRecords,setAttendanceRecords] = useState<[]>([]);
  const fetchClasses = async () => {
      try {
        const classes = await getClassesForStudent(auth.user.access_token);
        setAvailableClasses(classes.content || []);
      } catch (error) {
        console.error("Error fetching classes:", error);
        setAvailableClasses([]);
      }
    };
 useEffect(() => {
     fetchClasses();
    }, []);
 
  const fetchAttendanceRecords = async () => {
      try {
        const startDateStr = startDate ? format(startDate, "yyyy-MM-dd") : "";
        const endDateStr = endDate ? format(endDate, "yyyy-MM-dd") : "";
        const records = await getAttendanceByStudentId(auth.user.access_token, startDateStr, endDateStr);
        setAttendanceRecords(records || []);
      } catch (error) {
        console.error("Error fetching classes:", error);
        setAvailableClasses([]);
      }
    };
 useEffect(() => {
     fetchAttendanceRecords();
    }, []);




  const filteredAttendanceRecords = dummyAttendanceRecords
    .filter(record => selectedClass === "all" || record.classId === selectedClass)
    .filter(record => {
      if (!startDate && !endDate) return true;
      const recordDate = new Date(record.date);
      if (startDate && !endDate) return recordDate >= startDate;
      if (!startDate && endDate) return recordDate <= endDate;
      if (startDate && endDate) return recordDate >= startDate && recordDate <= endDate;
      return true;
    });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <Check className="h-5 w-5 text-green-500" />;
      case 'absent':
        return <X className="h-5 w-5 text-red-500" />;
      case 'late':
        return <Clock className="h-5 w-5 text-amber-500" />;
      case 'excused':
        return <Calendar className="h-5 w-5 text-blue-500" />;
      default:
        return null;
    }
  };

  const getAttendanceStats = () => {
    if (filteredAttendanceRecords.length === 0) return { present: 0, absent: 0, late: 0, excused: 0, total: 0, presentPercentage: 0 };
    
    const present = filteredAttendanceRecords.filter(r => r.status === 'present').length;
    const absent = filteredAttendanceRecords.filter(r => r.status === 'absent').length;
    const late = filteredAttendanceRecords.filter(r => r.status === 'late').length;
    const excused = filteredAttendanceRecords.filter(r => r.status === 'excused').length;
    const total = filteredAttendanceRecords.length;
    
    return {
      present,
      absent,
      late,
      excused,
      total,
      presentPercentage: total > 0 ? Math.round((present / total) * 100) : 0
    };
  };

  const stats = getAttendanceStats();

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
      <header className="bg-white shadow-sm p-6 flex justify-between items-center border-b">
        <div className="flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => navigate("/student-dashboard")}
            className="mr-2"
          >
            <ArrowLeft className="h-6 w-6 text-gray-500" />
          </Button>
          <h1 className="text-2xl font-semibold text-gray-800">My Attendance</h1>
        </div>
      </header>

      <div className="container mx-auto p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-none shadow-md">
            <CardContent className="p-6">
              <h3 className="text-gray-700 mb-2 font-medium">Present</h3>
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-3xl font-bold text-green-600">{stats.present}</div>
                  <div className="text-sm text-gray-600">Days</div>
                </div>
                <Check className="h-8 w-8 text-green-500 opacity-20" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-red-50 to-red-100 border-none shadow-md">
            <CardContent className="p-6">
              <h3 className="text-gray-700 mb-2 font-medium">Absent</h3>
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-3xl font-bold text-red-600">{stats.absent}</div>
                  <div className="text-sm text-gray-600">Days</div>
                </div>
                <X className="h-8 w-8 text-red-500 opacity-20" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border-none shadow-md">
            <CardContent className="p-6">
              <h3 className="text-gray-700 mb-2 font-medium">Late</h3>
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-3xl font-bold text-amber-600">{stats.late}</div>
                  <div className="text-sm text-gray-600">Days</div>
                </div>
                <Clock className="h-8 w-8 text-amber-500 opacity-20" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-none shadow-md">
            <CardContent className="p-6">
              <h3 className="text-gray-700 mb-2 font-medium">Attendance Rate</h3>
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-3xl font-bold text-blue-600">{stats.presentPercentage}%</div>
                  <div className="text-sm text-gray-600">Overall</div>
                </div>
                <Calendar className="h-8 w-8 text-blue-500 opacity-20" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="shadow-lg border-none">
          <CardHeader>
            <CardTitle>Attendance Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="w-full md:w-64">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Class</label>
                  <Select 
                    value={selectedClass} 
                    onValueChange={setSelectedClass}
                  >
                    <SelectTrigger className="edu-form-field">
                      <SelectValue placeholder="Select a class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Classes</SelectItem>
                      {dummyClasses.map(cls => (
                        <SelectItem key={cls.id} value={cls.id}>
                          {cls.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="w-full md:w-64">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                  <div className="flex gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "justify-start text-left font-normal",
                            !startDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarRange className="mr-2 h-4 w-4" />
                          {startDate ? format(startDate, "MMM dd, yyyy") : "Start date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={startDate}
                          onSelect={setStartDate}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>

                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "justify-start text-left font-normal",
                            !endDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarRange className="mr-2 h-4 w-4" />
                          {endDate ? format(endDate, "MMM dd, yyyy") : "End date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={endDate}
                          onSelect={setEndDate}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>

              {filteredAttendanceRecords.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="font-semibold">Date</TableHead>
                      <TableHead className="font-semibold">Class</TableHead>
                      <TableHead className="font-semibold">Status</TableHead>
                      <TableHead className="font-semibold">Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAttendanceRecords.map(record => (
                      <TableRow key={record.id} className="hover:bg-gray-50">
                        <TableCell>{record.date}</TableCell>
                        <TableCell>
                          {dummyClasses.find(c => c.id === record.classId)?.name || 'Unknown Class'}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {getStatusIcon(record.status)}
                            <span className="ml-2 capitalize">{record.status}</span>
                          </div>
                        </TableCell>
                        <TableCell>{record.notes || 'No notes'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                  <h3 className="text-lg font-medium text-gray-600 mb-2">No Attendance Records</h3>
                  <p className="text-gray-500">
                    There are no attendance records available for {selectedClass !== "all" ? "this class" : "your classes"}.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
