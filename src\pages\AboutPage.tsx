 
 import React, { useState, useEffect } from "react";
 import { ArrowLef<PERSON>, <PERSON>cil, Check, X, Plus, Trash2,Calendar,Building } from "lucide-react";
 import { Link } from "react-router-dom";
 import { useAuth } from "react-oidc-context"; // Updated import
 import { useUserRole } from "@/hooks/useUserRole";
 import { Button } from "@/components/ui/button";
 import { Card } from "@/components/ui/card";
 import { Textarea } from "@/components/ui/textarea";
 import { Input } from "@/components/ui/input";
 import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
 import ProfileSidebar from "@/components/profile/ProfileSidebar";
 import { toast } from "sonner";
 import { 
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue
 } from "@/components/ui/select";
 import { Certificate } from "crypto";
 import {getSkills, getAllCertificates, addCertificate, updateCertificate, deleteCertificate 
   ,addSkill ,updateSkill,deleteSkill, addEducation,getEducation,updateEducation,deleteEducation,
   createProfile,getProfile,
   getAllExperience, addExperience, updateExperience ,deleteExperience

 } from "@/services/profileService";

 import { RichTextEditor } from "@/components/ui/rich-text-editor";

 export default function AboutPage() {
 const auth = useAuth();
 const { selectedRole } = useUserRole();
   const [editSection, setEditSection] = useState<string | null>(null);
   const [showAddForm, setShowAddForm] = useState(false);
   const [skills, setSkills] = useState([]);
   // Mock about data
   const [aboutData, setAboutData] = useState({
     aboutMe: "",
     gender : ""
     });
   
   // Temp state for editing
   const [tempData, setTempData] = useState({ ...aboutData });
 const [certificates, setCertificates] = useState([]);
   const [newCert, setNewCert] = useState({
     certificationName: "",
     certificationLevel: "",
     issuingOrganization: "",
     issueYear: "",
     expiryYear:''
   });
   const [certError, setCertError] = useState("");
   const [editingCertId, setEditingCertId] = useState(null);
   const [certErrors, setCertErrors] = useState({
     certificationName: "",
     certificationLevel: "",
     issuingOrganization: "",
     issueYear: "",
     expiryYear: ""
   });
   const [showSkillForm, setShowSkillForm] = useState(false);
   const [newSkill, setNewSkill] = useState({
     skillName: '',
     proficiencyLevel: '',
     category: '',
     yearsOfExperience: '',
     description: ''
   });
   const [skillError, setSkillError] = useState("");
   const [editingSkillId, setEditingSkillId] = useState(null);
   const [showEducationForm, setShowEducationForm] = useState(false);
   const [newEducation, setNewEducation] = useState({
     title: '',
     institution: '',
     subject: '',
     startYear: '',
     endYear: '',
     description: ''
   });
   const [educationError, setEducationError] = useState("");
   const [editingEducationId, setEditingEducationId] = useState(null);
   const [education, setEducation] = useState([]);
   const [educationErrors, setEducationErrors] = useState({
     title: "",
     institution: "",
     subject: "",
     startYear: "",
     endYear: ""
   });
   const [bioErrors, setBioErrors] = useState({
     aboutMe: "",
     gender: ""
   });
   const [skillErrors, setSkillErrors] = useState({
     skillName: "",
     proficiencyLevel: ""
   });
   const fetchProfile = async () => {
     try {
       const fetchedProfile = await getProfile(auth.user?.access_token);
       console.log('Fetched profile:', fetchedProfile); // Debug log
       if (fetchedProfile) {
         setAboutData({
           aboutMe: fetchedProfile.aboutMe || '',
           gender: fetchedProfile.gender || ''
         });
       }
     } catch (error) {
       console.error('Error fetching profile:', error);
     }
   };
   useEffect(() => {
     fetchProfile();
   }, []);
   const fetchEducation = async () => {
        try {
          const fetchedEducation = await getEducation(auth.user?.access_token);
          setEducation(fetchedEducation);
        } catch (error) {
          console.error('Error fetching Education:', error);
        }
    };
    useEffect(() => {
      fetchEducation();
    }, []);
  
   const fetchSkills = async () => {
        try {
          const fetchedSkills = await getSkills(auth.user?.access_token);
          setSkills(fetchedSkills);
        } catch (error) {
          console.error('Error fetching Skills', error);
        }
    };
    useEffect(() => {
      fetchSkills();
    }, []);
   const fetchCertificates = async () => {
        try {
          const fetchedCertificates = await getAllCertificates(auth.user?.access_token);
          setCertificates(fetchedCertificates);
        } catch (error) {
          console.error('Error fetching Certificate:', error);
        }
    };
    useEffect(() => {
      fetchCertificates();
    }, []);
   const handleEdit = (section: string) => {
     setTempData({ ...aboutData });
     setEditSection(section);
   };

   const handleCancel = () => {
     setEditSection(null);
     setBioErrors({ aboutMe: "", gender: "" });
   };

   const validateBio = () => {
     const errors = {
       aboutMe: "",
       gender: ""
     };
     
     if (!tempData.aboutMe || tempData.aboutMe.trim() === "" || tempData.aboutMe === "<p></p>") {
       errors.aboutMe = "About me is required";
     }
     if (!tempData.gender || tempData.gender.trim() === "") {
       errors.gender = "Gender is required";
     }
     
     setBioErrors(errors);
     return Object.values(errors).every(error => error === "");
   };

   const handleSave = async (section: string) => {
     if (section === 'bio') {
       if (!validateBio()) {
         return;
       }
       
       try {
         await createProfile({
           aboutMe: tempData.aboutMe,
           gender: tempData.gender,
           profilePicture:"dummy url"
         }, auth.user?.access_token);
         setAboutData({ ...tempData });
         setBioErrors({ aboutMe: "", gender: "" });
         setEditSection(null);
         toast.success('Profile saved successfully');
       } catch (error) {
         console.error('Error saving profile:', error);
         toast.error('Failed to save profile');
       }
     } else {
       setAboutData({ ...tempData });
       setEditSection(null);
       toast.success(`${section} updated successfully`);
     }
   };

   const handleAboutMeChange = (value: string) => {
     setTempData(prev => ({ ...prev, aboutMe: value }));
   };

   const handleGenderChange = (value: string) => {
     setTempData(prev => ({ ...prev, gender: value }));
   };


   const handleSkillsChange = (value: string) => {
     const skillsArray = value.split(',').map(skill => skill.trim()).filter(skill => skill);
    // setTempData(prev => ({ ...prev, skills: skillsArray }));
   };

   const handleEducationChange = (id: string, field: string, value: string) => {
     
   };

   const handleCertificateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
     const { name, value } = e.target;
     setNewCert(prev => ({ ...prev, [name]: value }));
   };

   const handleSaveCertificate = async () => {
     const errors = {
       certificationName: !newCert.certificationName ? "Certification name is required" : "",
       certificationLevel: !newCert.certificationLevel ? "Certification level is required" : "",
       issuingOrganization: !newCert.issuingOrganization ? "Issuing organization is required" : "",
       issueYear: !newCert.issueYear ? "Issue year is required" : "",
       expiryYear: !newCert.expiryYear ? "Expiry year is required" : ""
     };
     
     // Additional validation for year comparison
     if (newCert.issueYear && newCert.expiryYear && parseInt(newCert.issueYear) >= parseInt(newCert.expiryYear)) {
       errors.issueYear = "Issue year must be less than expiry year";
     }
     
     setCertErrors(errors);
     
     if (errors.certificationName || errors.certificationLevel || errors.issuingOrganization || errors.issueYear || errors.expiryYear) {
       return;
     }
     
     setCertError('');
     setCertErrors({ certificationName: "", certificationLevel: "", issuingOrganization: "", issueYear: "", expiryYear: "" });
     try {
       if (editingCertId && editingCertId !== null) {
         await updateCertificate(newCert, auth.user?.access_token, editingCertId);
         toast.success('Certificate updated successfully');
       } else {
         await addCertificate(newCert, auth.user?.access_token);
         toast.success('Certificate added successfully');
       }
       const fetchedCertificates = await getAllCertificates(auth.user?.access_token);
       setCertificates(fetchedCertificates);
       setNewCert({ certificationName: '', certificationLevel: '', issuingOrganization: '', issueYear: '' ,expiryYear :''});
       setCertErrors({ certificationName: "", certificationLevel: "", issuingOrganization: "", issueYear: "", expiryYear: "" });
       setEditingCertId(null);
       setShowAddForm(false);
     } catch (error) {
       console.error('Error saving certificate:', error);
       toast.error('Failed to save certificate');
     }
   };

   const handleCancelCertificate = () => {
     setNewCert({ certificationName: '', certificationLevel: '', issuingOrganization: '', issueYear: '' ,expiryYear:''});
     setCertErrors({ certificationName: "", certificationLevel: "", issuingOrganization: "", issueYear: "", expiryYear: "" });
     setEditingCertId(null);
     setShowAddForm(false);
     setCertError('');
   };

   const handleEditCertificate = (certId: string) => {
     const certToEdit = certificates.find(cert => cert.certificationId === certId || cert.id === certId);
     console.log('Found certificate to edit:', certToEdit);
     if (certToEdit) {
       setNewCert({
         certificationName: certToEdit.certificationName || '',
         certificationLevel: certToEdit.certificationLevel || '',
         issuingOrganization: certToEdit.issuingOrganization || '',
         issueYear: certToEdit.issueYear || '',
         expiryYear: certToEdit.expiryYear || ''
       });
       setEditingCertId(certToEdit.certificationId || certToEdit.id);
       setShowAddForm(true);
       setCertError('');
     }
   };


   const handleRemoveCertificate = async (certId: string) => {
     try {
       await deleteCertificate(auth.user?.access_token, certId);
       const fetchedCertificates = await getAllCertificates(auth.user?.access_token);
       setCertificates(fetchedCertificates);
       toast.success('Certificate removed successfully');
     } catch (error) {
       console.error('Error removing certificate:', error);
       toast.error('Failed to remove certificate');
     }
   };
 const handleSaveEducation = async () => {
     const errors = {
       title: !newEducation.title ? "Title is required" : "",
       institution: !newEducation.institution ? "Institution is required" : "",
       subject: !newEducation.subject ? "Subject is required" : "",
       startYear: !newEducation.startYear ? "Start year is required" : "",
       endYear: !newEducation.endYear ? "End year is required" : ""
     };
     
     // Additional validation for year comparison
     if (newEducation.startYear && newEducation.endYear && parseInt(newEducation.startYear) >= parseInt(newEducation.endYear)) {
       errors.startYear = "Start year must be less than end year";
     }
     
     setEducationErrors(errors);
     
     if (errors.title || errors.institution || errors.subject || errors.startYear || errors.endYear) {
       return;
     }
     
     setEducationError('');
     setEducationErrors({ title: "", institution: "", subject: "", startYear: "", endYear: "" });
     try {
       if (editingEducationId) {
         await updateEducation(newEducation, auth.user?.access_token, editingEducationId);
         toast.success('Education updated successfully');
       } else {
         await addEducation(newEducation, auth.user?.access_token);
         toast.success('Education added successfully');
       }
       const fetchedEducation = await getEducation(auth.user?.access_token);
       setEducation(fetchedEducation);
       setNewEducation({ title: '', institution: '', subject: '', startYear: '', endYear: '', description: '' });
       setEducationErrors({ title: "", institution: "", subject: "", startYear: "", endYear: "" });
       setEditingEducationId(null);
       setShowEducationForm(false);
     } catch (error) {
       console.error('Error saving education:', error);
       toast.error('Failed to save education');
     }
   };

   const handleEditEducation = (eduId: string) => {
     const eduToEdit = education.find(edu => edu.id === eduId);
     if (eduToEdit) {
       setNewEducation({
         title: eduToEdit.title || '',
         institution: eduToEdit.institution || '',
         subject: eduToEdit.subject || '',
         startYear: eduToEdit.startYear || '',
         endYear: eduToEdit.endYear || '',
         description: eduToEdit.description || ''
       });
       setEditingEducationId(eduId);
       setShowEducationForm(true);
       setEducationError('');
     }
   };
   const handleRemoveEducation = async (eduId: string) => {
     try {
       await deleteEducation(auth.user?.access_token, eduId);
       const fetchedEducation = await getEducation(auth.user?.access_token);
       setEducation(fetchedEducation);
       toast.success('Education removed successfully');
     } catch (error) {
       console.error('Error removing education:', error);
       toast.error('Failed to remove education');
     }
   };

   const handleRemoveSkill = async (skillId: string) => {
     try {
       await deleteSkill(auth.user?.access_token, skillId);
       const fetchedSkills = await getSkills(auth.user?.access_token);
       setSkills(fetchedSkills);
       toast.success('Skill removed successfully');
     } catch (error) {
       console.error('Error removing skill:', error);
       toast.error('Failed to remove skill');
     }
   };

   const validateSkill = () => {
     const errors = {
       skillName: "",
       proficiencyLevel: ""
     };
     
     if (!newSkill.skillName || newSkill.skillName.trim() === "") {
       errors.skillName = "Skill name is required";
     }
     if (!newSkill.proficiencyLevel || newSkill.proficiencyLevel.trim() === "") {
       errors.proficiencyLevel = "Proficiency level is required";
     }
     
     setSkillErrors(errors);
     return Object.values(errors).every(error => error === "");
   };

   const handleSaveSkill = async () => {
     if (!validateSkill()) {
       return;
     }
     
     setSkillError('');
     try {
       if (editingSkillId) {
         await updateSkill(newSkill, auth.user?.access_token, editingSkillId);
         toast.success('Skill updated successfully');
       } else {
         await addSkill(newSkill, auth.user?.access_token);
         toast.success('Skill added successfully');
       }
       const fetchedSkills = await getSkills(auth.user?.access_token);
       setSkills(fetchedSkills);
       setNewSkill({ skillName: '', proficiencyLevel: '', category: '', yearsOfExperience: '', description: '' });
       setSkillErrors({ skillName: "", proficiencyLevel: "" });
       setEditingSkillId(null);
       setShowSkillForm(false);
     } catch (error) {
       console.error('Error saving skill:', error);
       toast.error('Failed to save skill');
     }
   };

   const handleEditSkill = (skillId: string) => {
     const skillToEdit = skills.find(skill => skill.id === skillId);
     if (skillToEdit) {
       setNewSkill({
         skillName: skillToEdit.skillName || '',
         proficiencyLevel: skillToEdit.proficiencyLevel || '',
         category: skillToEdit.category || '',
         yearsOfExperience: skillToEdit.yearsOfExperience || '',
         description: skillToEdit.description || ''
       });
       setEditingSkillId(skillId);
       setShowSkillForm(true);
       setSkillError('');
     }
   };
     // Handle form field changes
     const handleExpChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
       const { name, value } = e.target;
       setTempExp(prev => ({
         ...prev,
         [name]: value
       }));
     };
   const [showAddExpForm, setShowAddExpForm] = useState(false);
   const [newExp, setNewExp] = useState({
     jobTitle: "",
     companyName: "",
     companyLocation: "",
     employmentType: "FULL_TIME",
     startYear: "",
     startMonth: "",
     endYear: "",
     endMonth: "",
     currentlyWorking: false,
     description: ""
   });
   
   const [expErrors, setExpErrors] = useState({
     jobTitle: "",
     companyName: "",
     startYear: "",
     startMonth: "",
     endYear: ""
   });
   const [experiences, setExperiences] = useState([  ]);
   // Handle edit experience
   const handleEditExperience = (id: string) => {
     const exp = experiences.find(e => e.id === id);
     if (exp) {
       setTempExp({
         jobTitle: exp.jobTitle,
         companyName: exp.companyName,
         employmentType: exp.employmentType,
         startYear: exp.startYear,
         startMonth: exp.startMonth,
         endMonth: exp.endMonth,
         endYear: exp.endYear,
         currentlyWorking: exp.currentlyWorking,
         companyLocation: exp.companyLocation,
         description: exp.description
       });
       setEditingExpId(id);
     }
   };
 const handleCancelEditExperience = () => {
     setEditingExpId(null);
     setTempExp({
       jobTitle: "",
       companyName: "",
       companyLocation: "",
       employmentType: "FULL_TIME",
       startYear: "",
       startMonth: "",
       endYear: "",
       endMonth: "",
       currentlyWorking: false,
       description: ""
     });
   };

   const fetchExperience = async () => {
          try {
            const fetchedExp = await getAllExperience(auth.user?.access_token);
            setExperiences(fetchedExp);
          } catch (error) {
            console.error('Error fetching Experiences:', error);
          }
      };
      useEffect(() => {
        fetchExperience();
      }, []);     

   // State for editing
   const [editingExpId, setEditingExpId] = useState<string | null>(null);
   const [tempExp, setTempExp] = useState({
     jobTitle: "",
     companyName: "",
     companyLocation: "",
     startYear: "",
     startMonth: "",
     endMonth: "",
     endYear: "",
     currentlyWorking: false,
     employmentType : "FULL_TIME", 
     description: ""  });
   

   const handleExpFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
     const { name, value } = e.target;
     setNewExp(prev => ({ ...prev, [name]: value }));
   };

   const handleSaveNewExperience = async () => {
     const errors = {
       jobTitle: !newExp.jobTitle ? "Job title is required" : "",
       companyName: !newExp.companyName ? "Company name is required" : "",
       startYear: !newExp.startYear ? "Start year is required" : "",
       startMonth: "",
       endYear: ""
     };
     
     // Validate end year is required when not currently working
     if (!newExp.currentlyWorking && !newExp.endYear) {
       errors.endYear = "End year is required";
     }
     
     // Validate start year is less than end year
     if (!newExp.currentlyWorking && newExp.endYear && 
         parseInt(newExp.startYear) > parseInt(newExp.endYear)) {
       errors.startYear = "Start year must be less than end year";
     }
     
     // Validate start month cannot be after end month in the same year
     if (!newExp.currentlyWorking && newExp.startYear && newExp.endYear && 
         newExp.startMonth && newExp.endMonth &&
         parseInt(newExp.startYear) === parseInt(newExp.endYear) &&
         parseInt(newExp.startMonth) > parseInt(newExp.endMonth)) {
       errors.startMonth = "Start month cannot be after end month in the same year";
     }
     
     setExpErrors(errors);
     
     if (errors.jobTitle || errors.companyName || errors.startYear || errors.startMonth || errors.endYear) {
       return;
     }
     
     try {
       await addExperience(newExp, auth.user?.access_token);
       const fetchedExp = await getAllExperience(auth.user?.access_token);
       setExperiences(fetchedExp);
       setNewExp({
         jobTitle: "",
         companyName: "",
         companyLocation: "",
         employmentType: "FULL_TIME",
         startYear: "",
         startMonth: "",
         endYear: "",
         endMonth: "",
         currentlyWorking: false,
         description: ""
       });
       setExpErrors({ jobTitle: "", companyName: "", startYear: "", startMonth: "", endYear: "" });
       setShowAddExpForm(false);
       toast.success('Experience added successfully');
     } catch (error) {
       console.error('Error adding experience:', error);
       toast.error('Failed to add experience');
     }
   };

   const handleCancelNewExperience = () => {
     setNewExp({
       jobTitle: "",
       companyName: "",
       companyLocation: "",
       employmentType: "FULL_TIME",
       startYear: "",
       startMonth: "",
       endYear: "",
       endMonth: "",
       currentlyWorking: false,
       description: ""
     });
     setExpErrors({ jobTitle: "", companyName: "", startYear: "", startMonth: "", endYear: "" });
     setShowAddExpForm(false);
   };

   const handleUpdateExperience = async (id: string) => {
     // Validate required fields
     if (!tempExp.jobTitle || !tempExp.companyName || !tempExp.startYear) {
       toast.error('Please fill in all required fields');
       return;
     }
     
     // Validate start year is less than end year
     if (!tempExp.currentlyWorking && tempExp.endYear && 
         parseInt(tempExp.startYear) > parseInt(tempExp.endYear)) {
       toast.error('Start year must be less than end year');
       return;
     }
     
     try {
       await updateExperience(tempExp, auth.user?.access_token, id);
       const fetchedExp = await getAllExperience(auth.user?.access_token);
       setExperiences(fetchedExp);
       setEditingExpId(null);
       toast.success('Experience updated successfully');
     } catch (error) {
       console.error('Error updating experience:', error?.response?.data?.details);
       const errorMessage = error?.response?.data?.details || 'Failed to update experience';
       toast.error(errorMessage);
     }
   };

   const handleRemoveExperience = async (id: string) => {
     try {
       await deleteExperience(auth.user?.access_token, id);
       const fetchedExp = await getAllExperience(auth.user?.access_token);
       setExperiences(fetchedExp);
       toast.success('Experience removed successfully');
     } catch (error) {
       console.error('Error removing experience:', error);
       toast.error('Failed to remove experience');
     }
   };

   return (
     <div className="min-h-screen bg-gray-50">
       {/* Header */}
       <div className="p-4 bg-white">
         <div className="flex items-center gap-2">
           <Link to="/dashboard" className="flex items-center">
             <ArrowLeft className="h-5 w-5 text-gray-700" />
           </Link>
           <h1 className="text-lg font-medium">About</h1>
         </div>
       </div>

       <div className="container mx-auto max-w-4xl py-6 px-4">
         <div className="grid grid-cols-12 gap-6">
           {/* Sidebar */}
           <div className="col-span-12 md:col-span-3">
             <ProfileSidebar activePage="about" />
           </div>

           {/* Main Content */}
           <div className="col-span-12 md:col-span-9 space-y-6">
             <Card className="overflow-hidden">
               <div className="p-6">
                 <div className="flex justify-between items-center mb-6">
                   <h2 className="text-xl font-semibold">About Me</h2>
                   {editSection === 'bio' ? (
                     <div className="flex items-center gap-2">
                       <Button
                         variant="outline"
                         size="sm"
                         className="flex items-center gap-1 text-red-600 border-red-600 hover:bg-red-50"
                         onClick={handleCancel}
                       >
                         <X className="h-4 w-4" />
                         Cancel
                       </Button>
                       <Button
                         size="sm"
                         className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                         onClick={() => handleSave('bio')}
                       >
                         <Check className="h-4 w-4" />
                         Save
                       </Button>
                     </div>
                   ) : (
                     <Button
                       variant="outline"
                       className="flex items-center gap-2 text-purple-600 border-purple-600 hover:bg-purple-50"
                       onClick={() => handleEdit('bio')}
                     >
                       <Pencil className="h-4 w-4" />
                       
                     </Button>
                   )}
                 </div>

                 <div className="space-y-6">
                   {/* Bio Section */}
                   <div>
                     <p className="text-sm text-gray-500 mb-1">About Me*</p>
                     {editSection === 'bio' ? (
                       <div>
                         <RichTextEditor 
                           value={tempData.aboutMe}
                           onChange={handleAboutMeChange}
                           placeholder="Write about yourself..."
                           className={bioErrors.aboutMe ? 'border-red-500' : ''}
                         />
                         {bioErrors.aboutMe && <p className="text-red-500 text-xs mt-1">{bioErrors.aboutMe}</p>}
                       </div>
                     ) : (
                       <div 
                         className="p-3 min-h-[150px] border rounded-md bg-gray-50 prose max-w-none"
                         dangerouslySetInnerHTML={{ 
                           __html: aboutData.aboutMe || '<p class="text-gray-500 italic">No bio added yet.</p>' 
                         }}
                       />
                     )}
                   </div>

                     <div>
                       <p className="text-sm text-gray-500 mb-1">Gender*</p>
                       <Select
                          name="gender"
                         value={editSection === 'bio' ? tempData.gender : aboutData.gender}
                         onValueChange={handleGenderChange}
                         disabled={editSection !== 'bio'}
                           >
                         <SelectTrigger className={`edu-form-field ${bioErrors.gender ? 'border-red-500' : ''}`}>
                           <SelectValue placeholder="Select Gender" />
                         </SelectTrigger>
                         <SelectContent>
                           <SelectItem value={"MALE"}>Male</SelectItem>
                           <SelectItem value={"FEMALE"}>Female</SelectItem>
                         </SelectContent>
                       </Select>
                       {bioErrors.gender && <p className="text-red-500 text-xs mt-1">{bioErrors.gender}</p>}
                     </div>
                    

                  
                 </div>
               </div>
             </Card>
             <Card className="overflow-hidden">
                {/* Education Section */}
                   <div className="p-6">
                     <div className="flex justify-between items-center mb-3">
                       <h3 className="text-lg font-medium">Education</h3>
                       {editSection === 'education' ? (
                         <div className="flex items-center gap-2">
                           <Button
                             variant="outline"
                             size="sm"
                             className="flex items-center gap-1 text-red-600 border-red-600 hover:bg-red-50"
                             onClick={handleCancel}
                           >
                             <X className="h-4 w-4" />
                             Cancel
                           </Button>
                           <Button
                             size="sm"
                             className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                             onClick={() => handleSave('education')}
                           >
                             <Check className="h-4 w-4" />
                             Save
                           </Button>
                         </div>
                       ) : (
                         <div className="flex items-center gap-2">
                           <Button
                             variant="outline"
                             size="sm"
                             className="flex items-center gap-2 text-purple-600 border-purple-600 hover:bg-purple-50"
                       onClick={() => setShowEducationForm(true)}
                           >
                             <Plus className="h-4 w-4" />
                             Add
                           </Button>
                         
                         </div>
                       )}
                     </div>
                     <div className="space-y-4">
                       <Dialog open={showEducationForm} onOpenChange={setShowEducationForm}>
                         <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                           <DialogHeader>
                             <DialogTitle>{editingEducationId ? 'Edit Education' : 'Add Education'}</DialogTitle>
                           </DialogHeader>
                           <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                             <div>
                               <p className="text-sm text-gray-500 mb-1">Title</p>
                               <Input 
                                 name="title"
                                 value={newEducation.title}
                                 onChange={(e) => {
                                   setNewEducation(prev => ({ ...prev, title: e.target.value }));
                                   if (educationErrors.title) setEducationErrors(prev => ({ ...prev, title: "" }));
                                 }}
                                 className={educationErrors.title ? "border-red-500" : "edu-form-field"}
                               />
                               {educationErrors.title && <p className="text-sm text-red-500 mt-1">{educationErrors.title}</p>}
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">Institution</p>
                               <Input 
                                 name="institution"
                                 value={newEducation.institution}
                                 onChange={(e) => {
                                   setNewEducation(prev => ({ ...prev, institution: e.target.value }));
                                   if (educationErrors.institution) setEducationErrors(prev => ({ ...prev, institution: "" }));
                                 }}
                                 className={educationErrors.institution ? "border-red-500" :  "edu-form-field"}
                               />
                               {educationErrors.institution && <p className="text-sm text-red-500 mt-1">{educationErrors.institution}</p>}
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">Subject</p>
                               <Input 
                                 name="subject"
                                 value={newEducation.subject}
                                 onChange={(e) => {
                                   setNewEducation(prev => ({ ...prev, subject: e.target.value }));
                                   if (educationErrors.subject) setEducationErrors(prev => ({ ...prev, subject: "" }));
                                 }}
                                 className={educationErrors.subject ? "border-red-500" :  "edu-form-field"}
                               />
                               {educationErrors.subject && <p className="text-sm text-red-500 mt-1">{educationErrors.subject}</p>}
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">Start Year</p>
                               <Input 
                                 name="startYear"
                                 type="number"
                                 value={newEducation.startYear}
                                 onChange={(e) => {
                                   setNewEducation(prev => ({ ...prev, startYear: e.target.value }));
                                   if (educationErrors.startYear) setEducationErrors(prev => ({ ...prev, startYear: "" }));
                                 }}
                                 className={educationErrors.startYear ? "border-red-500" :  "edu-form-field"}
                               />
                               {educationErrors.startYear && <p className="text-sm text-red-500 mt-1">{educationErrors.startYear}</p>}
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">End Year</p>
                               <Input 
                                 name="endYear"
                                 type="number"
                                 value={newEducation.endYear}
                                 onChange={(e) => {
                                   setNewEducation(prev => ({ ...prev, endYear: e.target.value }));
                                   if (educationErrors.endYear) setEducationErrors(prev => ({ ...prev, endYear: "" }));
                                 }}
                                 className={educationErrors.endYear ? "border-red-500" :  "edu-form-field"}
                               />
                               {educationErrors.endYear && <p className="text-sm text-red-500 mt-1">{educationErrors.endYear}</p>}
                             </div>
                             <div className="md:col-span-2">
                               <p className="text-sm text-gray-500 mb-1">Description</p>
                               <Textarea 
                                 name="description"
                                 className="edu-form-field"
                                 value={newEducation.description}
                                 onChange={(e) => setNewEducation(prev => ({ ...prev, description: e.target.value }))}
                               />
                             </div>
                             {educationError && (
                               <div className="md:col-span-2">
                                 <p className="text-sm text-red-500">{educationError}</p>
                               </div>
                             )}
                             <div className="md:col-span-2 flex gap-2 justify-end mt-4">
                               <Button
                                 variant="outline"
                                 size="sm"
                                 className="text-red-600 border-red-600 hover:bg-red-50"
                                 onClick={() => {
                                   setNewEducation({ title: '', institution: '', subject: '', startYear: '', endYear: '', description: '' });
                                   setEducationErrors({ title: "", institution: "", subject: "", startYear: "", endYear: "" });
                                   setShowEducationForm(false);
                                   setEducationError('');
                                 }}
                               >
                                 <X className="h-4 w-4 mr-1" />
                                 Cancel
                               </Button>
                               <Button
                                 size="sm"
                                 className="bg-green-600 hover:bg-green-700"
                                 onClick={handleSaveEducation}
                               >
                                 <Check className="h-4 w-4 mr-1" />
                                 Save
                               </Button>
                             </div>
                           </div>
                         </DialogContent>
                       </Dialog>
                       {education.map(edu => (
                         <div key={edu.id} className="relative border-l-2 border-purple-500 pl-4">
                           <div className="absolute top-0 right-0 flex gap-1">
                             <Button
                               size="sm"
                               variant="outline"
                               className="h-8 w-8 p-0 text-purple-600 border-purple-600 hover:bg-purple-50"
                               onClick={() => handleEditEducation(edu.id)}
                             >
                               <Pencil className="h-3 w-3" />
                             </Button>
                             <Button
                               size="sm"
                               variant="outline"
                               className="h-8 w-8 p-0 text-red-600 border-red-600 hover:bg-red-50"
                               onClick={() => handleRemoveEducation(edu.id)}
                             >
                               <Trash2 className="h-3 w-3" />
                             </Button>
                           </div>
                           <h4 className="font-medium">{edu.title}</h4>
                           <p className="text-sm text-gray-500">{edu.institution} - {edu.subject}</p>
                           <p className="text-sm text-gray-500">{edu.startYear} - {edu.endYear}</p>
                             <p className="text-sm mt-1">
                             {edu.description}
                           </p>
                         </div>
                       ))}
                     </div>
                   </div>
             </Card>
             <Card className="overflow-hidden">
                        {/* Skills Section */}
                   <div className="p-6">
                     <div className="flex justify-between items-center mb-3">
                       <h3 className="text-lg font-medium">Skills</h3>
                       {editSection === 'skills' ? (
                         <div className="flex items-center gap-2">
                           <Button
                             variant="outline"
                             size="sm"
                             className="flex items-center gap-1 text-red-600 border-red-600 hover:bg-red-50"
                             onClick={handleCancel}
                           >
                             <X className="h-4 w-4" />
                             Cancel
                           </Button>
                           <Button
                             size="sm"
                             className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                             onClick={() => handleSave('skills')}
                           >
                             <Check className="h-4 w-4" />
                             Save
                           </Button>
                         </div>
                       ) : (
                         <div className="flex items-center gap-2">
                           <Button
                             variant="outline"
                             size="sm"
                           className="flex items-center gap-2 text-purple-600 border-purple-600 hover:bg-purple-50"
                         onClick={() => setShowSkillForm(true)}
                           >
                             <Plus className="h-4 w-4" />
                             Add
                           </Button>
                         </div>
                       )}
                     </div>
                     <div className="space-y-4">
                       <Dialog open={showSkillForm} onOpenChange={setShowSkillForm}>
                         <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                           <DialogHeader>
                             <DialogTitle>{editingSkillId ? 'Edit Skill' : 'Add Skill'}</DialogTitle>
                           </DialogHeader>
                           <div className="grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-12 p-4">
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Skill Name*</p>
                             <Input 
                               name="skillName"
                               value={newSkill.skillName}
                               onChange={(e) => {
                                 setNewSkill(prev => ({ ...prev, skillName: e.target.value }));
                                 if (skillErrors.skillName) setSkillErrors(prev => ({ ...prev, skillName: "" }));
                               }}
                               className={skillErrors.skillName ? "border-red-500" :  "edu-form-field"}
                             />
                             {skillErrors.skillName && <p className="text-red-500 text-xs mt-1">{skillErrors.skillName}</p>}
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Proficiency Level*</p>
                             <Select 
                               value={newSkill.proficiencyLevel} 
                               onValueChange={(value) => {
                                 setNewSkill(prev => ({ ...prev, proficiencyLevel: value }));
                                 if (skillErrors.proficiencyLevel) setSkillErrors(prev => ({ ...prev, proficiencyLevel: "" }));
                               }}
                             >
                               <SelectTrigger className={skillErrors.proficiencyLevel ? "border-red-500" : "edu-form-field"}>
                                 <SelectValue placeholder="Select level" />
                               </SelectTrigger>
                               <SelectContent>
                                 <SelectItem value="Beginner">Beginner</SelectItem>
                                 <SelectItem value="Intermediate">Intermediate</SelectItem>
                                 <SelectItem value="Advance">Advance</SelectItem>
                                 <SelectItem value="Expert">Expert</SelectItem>
                               </SelectContent>
                             </Select>
                             {skillErrors.proficiencyLevel && <p className="text-red-500 text-xs mt-1">{skillErrors.proficiencyLevel}</p>}
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Category</p>
                             <Input 
                               name="category"
                               value={newSkill.category}
                               className= "edu-form-field"
                               onChange={(e) => setNewSkill(prev => ({ ...prev, category: e.target.value }))}
                             />
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Years of Experience</p>
                             <Input 
                               name="yearsOfExperience"
                               type="number"
                               className= "edu-form-field"
                               value={newSkill.yearsOfExperience}
                               onChange={(e) => setNewSkill(prev => ({ ...prev, yearsOfExperience: e.target.value }))}
                             />
                           </div>
                           <div className="md:col-span-2">
                             <p className="text-sm text-gray-500 mb-1">Description</p>
                             <Textarea 
                             className="edu-form-field"
                               name="description"
                               value={newSkill.description}
                               onChange={(e) => setNewSkill(prev => ({ ...prev, description: e.target.value }))}
                             />
                           </div>
                           {skillError && (
                             <div className="md:col-span-2">
                               <p className="text-sm text-red-500">{skillError}</p>
                             </div>
                           )}
                           <div className="md:col-span-2 flex gap-2 justify-end mt-4">
                             <Button
                               variant="outline"
                               size="sm"
                               className="text-red-600 border-red-600 hover:bg-red-50"
                               onClick={() => {
                                 setNewSkill({ skillName: '', proficiencyLevel: '', category: '', yearsOfExperience: '', description: '' });
                                 setSkillErrors({ skillName: "", proficiencyLevel: "" });
                                 setShowSkillForm(false);
                                 setSkillError('');
                               }}
                             >
                               <X className="h-4 w-4 mr-1" />
                               Cancel
                             </Button>
                             <Button
                               size="sm"
                               className="bg-green-600 hover:bg-green-700"
                               onClick={handleSaveSkill}
                             >
                               <Check className="h-4 w-4 mr-1" />
                               Save
                             </Button>
                           </div>
                           </div>
                         </DialogContent>
                       </Dialog>
                       {skills.map(skill => (
                         <div key={skill.id} className="relative border-l-2 border-purple-500 pl-4">
                           <div className="absolute top-0 right-0 flex gap-1">
                             <Button
                               size="sm"
                               variant="outline"
                               className="h-8 w-8 p-0 text-purple-600 border-purple-600 hover:bg-purple-50"
                               onClick={() => handleEditSkill(skill.id)}
                             >
                               <Pencil className="h-3 w-3" />
                             </Button>
                             <Button
                               size="sm"
                               variant="outline"
                               className="h-8 w-8 p-0 text-red-600 border-red-600 hover:bg-red-50"
                               onClick={() => handleRemoveSkill(skill.id)}
                             >
                               <Trash2 className="h-3 w-3" />
                             </Button>
                           </div>
                           <h4 className="font-medium">{skill.skillName}</h4>
                           <p className="text-sm text-gray-500">{skill.category} - {skill.proficiencyLevel}</p>
                           {skill.yearsOfExperience && skill.yearsOfExperience !== '0' && (
                             <p className="text-sm text-gray-500">{skill.yearsOfExperience} years experience</p>
                           )}
                           <p className="text-sm mt-1">
                             {skill.description}
                           </p>
                         </div>
                       ))}
                     </div>
                   </div>
             </Card>
            <Card className="overflow-hidden">
               <div className="p-6">
                 <div className="flex justify-between items-center mb-6">
                   <h2 className="text-xl font-semibold">Work Experience</h2>
                   <Button
                     variant="outline"
                             size="sm"
                            className="flex items-center gap-2 text-purple-600 border-purple-600 hover:bg-purple-50"
                        onClick={() => setShowAddExpForm(true)}
                   >
                     <Plus className="h-4 w-4" />
                     Add 
                   </Button>
                 </div>

                 <div className="space-y-4">
                   <Dialog open={showAddExpForm} onOpenChange={setShowAddExpForm}>
                     <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                       <DialogHeader>
                         <DialogTitle>Add Work Experience</DialogTitle>
                       </DialogHeader>
                       <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                         <div>
                           <p className="text-sm text-gray-500 mb-1">Job Title</p>
                           <Input 
                             name="jobTitle"
                             value={newExp.jobTitle}
                             onChange={(e) => {
                               handleExpFormChange(e);
                               if (expErrors.jobTitle) setExpErrors(prev => ({ ...prev, jobTitle: "" }));
                             }}
                             className={expErrors.jobTitle ? "border-red-500" :  "edu-form-field"}
                           />
                           {expErrors.jobTitle && <p className="text-sm text-red-500 mt-1">{expErrors.jobTitle}</p>}
                         </div>
                         <div>
                           <p className="text-sm text-gray-500 mb-1">Company Name</p>
                           <Input 
                             name="companyName"
                             value={newExp.companyName}
                             onChange={(e) => {
                               handleExpFormChange(e);
                               if (expErrors.companyName) setExpErrors(prev => ({ ...prev, companyName: "" }));
                             }}
                             className={expErrors.companyName ? "border-red-500" :  "edu-form-field"}
                           />
                           {expErrors.companyName && <p className="text-sm text-red-500 mt-1">{expErrors.companyName}</p>}
                         </div>
                         <div>
                           <p className="text-sm text-gray-500 mb-1">Company Location</p>
                           <Input 
                             name="companyLocation"
                             value={newExp.companyLocation}
                             onChange={handleExpFormChange}
                             className="edu-form-field"
                           />
                         </div>
                         <div>
                           <p className="text-sm text-gray-500 mb-1">Employment Type</p>
                           <Select value={newExp.employmentType} onValueChange={(value) => setNewExp(prev => ({ ...prev, employmentType: value }))}>
                             <SelectTrigger className="edu-form-field">
                               <SelectValue placeholder="Select type" />
                             </SelectTrigger>
                             <SelectContent>
                               <SelectItem value="FULL_TIME">Full Time</SelectItem>
                               <SelectItem value="PART_TIME">Part Time</SelectItem>
                               <SelectItem value="CONTRACT">Contract</SelectItem>
                               <SelectItem value="INTERNSHIP">Internship</SelectItem>
                             </SelectContent>
                           </Select>
                         </div>
                         <div>
                           <p className="text-sm text-gray-500 mb-1">Start Year</p>
                           <Input 
                             name="startYear"
                             type="number"
                             value={newExp.startYear}
                             onChange={(e) => {
                               handleExpFormChange(e);
                               if (expErrors.startYear) setExpErrors(prev => ({ ...prev, startYear: "" }));
                             }}
                             className={expErrors.startYear ? "border-red-500" :  "edu-form-field"}
                           />
                           {expErrors.startYear && <p className="text-sm text-red-500 mt-1">{expErrors.startYear}</p>}
                         </div>
                         <div>
                           <p className="text-sm text-gray-500 mb-1">Start Month</p>
                           <Input 
                             name="startMonth"
                             type="number"
                             min="1"
                             max="12"
                             value={newExp.startMonth}
                             onChange={(e) => {
                               handleExpFormChange(e);
                               if (expErrors.startMonth) setExpErrors(prev => ({ ...prev, startMonth: "" }));
                             }}
                             className={expErrors.startMonth ? "border-red-500" :  "edu-form-field"}
                           />
                           {expErrors.startMonth && <p className="text-sm text-red-500 mt-1">{expErrors.startMonth}</p>}
                         </div>
                         <div>
                           <p className="text-sm text-gray-500 mb-1">End Year</p>
                           <Input 
                             name="endYear"
                             type="number"
                             value={newExp.endYear}
                             onChange={(e) => {
                               handleExpFormChange(e);
                               if (expErrors.endYear) setExpErrors(prev => ({ ...prev, endYear: "" }));
                             }}
                             className={expErrors.endYear ? "border-red-500" : "edu-form-field"}
                             disabled={newExp.currentlyWorking}
                           />
                           {expErrors.endYear && <p className="text-sm text-red-500 mt-1">{expErrors.endYear}</p>}
                         </div>
                         <div>
                           <p className="text-sm text-gray-500 mb-1">End Month</p>
                           <Input 
                             name="endMonth"
                             type="number"
                             min="1"
                             max="12"
                              className= "edu-form-field"
                             value={newExp.endMonth}
                             onChange={handleExpFormChange}
                             disabled={newExp.currentlyWorking}
                           />
                         </div>
                         <div className="md:col-span-2">
                           <div className="flex items-center mb-2">
                             <input
                               type="checkbox"
                               checked={newExp.currentlyWorking}
                               onChange={(e) => setNewExp(prev => ({ ...prev, currentlyWorking: e.target.checked }))}
                               className="edu-form-field w-4 h-4 mr-2"
                             />
                             <label>Currently working here</label>
                           </div>
                         </div>
                         <div className="md:col-span-2">
                           <p className="text-sm text-gray-500 mb-1">Description</p>
                           <Textarea 
                           className="edu-form-field"
                             name="description"
                             value={newExp.description}
                             onChange={handleExpFormChange}
                             rows={3}
                           />
                         </div>
                         <div className="md:col-span-2 flex gap-2 justify-end mt-4">
                           <Button
                             variant="outline"
                             size="sm"
                             className="text-red-600 border-red-600 hover:bg-red-50"
                             onClick={handleCancelNewExperience}
                           >
                             <X className="h-4 w-4 mr-1" />
                             Cancel
                           </Button>
                           <Button
                             size="sm"
                             className="bg-green-600 hover:bg-green-700"
                             onClick={handleSaveNewExperience}
                           >
                             <Check className="h-4 w-4 mr-1" />
                             Save
                           </Button>
                         </div>
                       </div>
                     </DialogContent>
                   </Dialog>
                   {experiences.map(exp => (
                     <div key={exp.id} className="relative border-l-2 border-purple-500 pl-4">
                       <Dialog open={editingExpId === exp.id} onOpenChange={(open) => {
                         if (!open) handleCancelEditExperience();
                       }}>
                         <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                           <DialogHeader>
                             <DialogTitle>Edit Work Experience</DialogTitle>
                           </DialogHeader>
                           <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                             <div>
                               <p className="text-sm text-gray-500 mb-1">Job Title</p>
                               <Input 
                                 name="jobTitle"
                                 className="edu-form-field"
                                 value={tempExp.jobTitle}
                                 onChange={handleExpChange}
                               />
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">Company Name</p>
                               <Input 
                                 name="companyName"
                                    className="edu-form-field"
                                 value={tempExp.companyName}
                                 onChange={handleExpChange}
                               />
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">Company Location</p>
                               <Input 
                                 name="companyLocation"
                                  className="edu-form-field"
                                 value={tempExp.companyLocation}
                                 onChange={handleExpChange}
                               />
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">Employment Type</p>
                               <Select value={tempExp.employmentType} onValueChange={(value) => setTempExp(prev => ({ ...prev, employmentType: value }))}>
                                 <SelectTrigger className="edu-form-field">
                                   <SelectValue placeholder="Select type" />
                                 </SelectTrigger>
                                 <SelectContent>
                                   <SelectItem value="FULL_TIME">Full Time</SelectItem>
                                   <SelectItem value="PART_TIME">Part Time</SelectItem>
                                   <SelectItem value="CONTRACT">Contract</SelectItem>
                                   <SelectItem value="INTERNSHIP">Internship</SelectItem>
                                 </SelectContent>
                               </Select>
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">Start Year</p>
                               <Input 
                                 name="startYear"
                                 type="number"
                                 className="edu-form-field"
                                 value={tempExp.startYear}
                                 onChange={handleExpChange}
                               />
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">Start Month</p>
                               <Input 
                                 name="startMonth"
                                 type="number"
                                 className="edu-form-field"
                                 min="1"
                                 max="12"
                                 value={tempExp.startMonth}
                                 onChange={handleExpChange}
                               />
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">End Year</p>
                               <Input 
                                 name="endYear"
                                 type="number"
                                  className="edu-form-field"
                                 value={tempExp.endYear}
                                 onChange={handleExpChange}
                                 disabled={tempExp.currentlyWorking}
                               />
                             </div>
                             <div>
                               <p className="text-sm text-gray-500 mb-1">End Month</p>
                               <Input 
                                 name="endMonth"
                                 type="number"
                                 min="1"
                                 className="edu-form-field"
                                 max="12"
                                 value={tempExp.endMonth}
                                 onChange={handleExpChange}
                                 disabled={tempExp.currentlyWorking}
                               />
                             </div>
                             <div className="md:col-span-2">
                               <div className="flex items-center mb-2">
                                 <input
                                   type="checkbox"
                                   checked={tempExp.currentlyWorking}
                                   onChange={(e) => setTempExp(prev => ({ ...prev, currentlyWorking: e.target.checked }))}                                  
                                   className="edu-form-field w-4 h-4 mr-2"
                                 />
                                 <label>Currently working here</label>
                               </div>
                             </div>
                             <div className="md:col-span-2">
                               <p className="text-sm text-gray-500 mb-1">Description</p>
                               <Textarea 
                                 name="description"
                                 value={tempExp.description}
                                 onChange={handleExpChange}
                                 rows={3}
                               />
                             </div>
                             <div className="md:col-span-2 flex gap-2 justify-end mt-4">
                               <Button
                                 variant="outline"
                                 size="sm"
                                 className="text-red-600 border-red-600 hover:bg-red-50"
                                 onClick={handleCancelEditExperience}
                               >
                                 <X className="h-4 w-4 mr-1" />
                                 Cancel
                               </Button>
                               <Button
                                 size="sm"
                                 className="bg-green-600 hover:bg-green-700"
                                 onClick={() => handleUpdateExperience(exp.id)}
                               >
                                 <Check className="h-4 w-4 mr-1" />
                                 Save
                               </Button>
                             </div>
                           </div>
                         </DialogContent>
                       </Dialog>
                       <div className="absolute top-0 right-0 flex gap-1">
                         <Button
                           size="sm"
                           variant="outline"
                           className="h-8 w-8 p-0 text-purple-600 border-purple-600 hover:bg-purple-50"
                           onClick={() => handleEditExperience(exp.id)}
                         >
                           <Pencil className="h-3 w-3" />
                         </Button>
                         <Button
                           size="sm"
                           variant="outline"
                           className="h-8 w-8 p-0 text-red-600 border-red-600 hover:bg-red-50"
                           onClick={() => handleRemoveExperience(exp.id)}
                         >
                           <Trash2 className="h-3 w-3" />
                         </Button>
                       </div>
                       <h4 className="font-medium">{exp.jobTitle}</h4>
                       <p className="text-sm text-gray-500">{exp.companyName} - {exp.employmentType}</p>
                       <p className="text-sm text-gray-500">
                         {exp.startMonth && exp.startMonth !== '0' ? `${exp.startMonth}/${exp.startYear}` : exp.startYear} - {exp.currentlyWorking ? 'Present' : (exp.endMonth && exp.endMonth !== '0' ? `${exp.endMonth}/${exp.endYear}` : exp.endYear)}
                       </p>
                       <p className="text-sm mt-1">
                         {exp.description}
                       </p>
                     </div>
                       )
                     
                   )}
                                   </div>
               </div>
             </Card>
             
             <Card className="overflow-hidden">
               <div className="p-6">
                 <div className="flex justify-between items-center">
                   <h2 className="text-xl font-semibold">Certificates & Awards</h2>
                   {editSection === 'certificates' ? (
                     <div className="flex items-center gap-2">
                       <Button
                         variant="outline"
                         size="sm"
                         className="flex items-center gap-1 text-red-600 border-red-600 hover:bg-red-50"
                         onClick={handleCancel}
                       >
                         <X className="h-4 w-4" />
                         Cancel
                       </Button>
                       <Button
                         size="sm"
                         className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                         onClick={() => handleSave('certificates')}
                       >
                         <Check className="h-4 w-4" />
                         Save
                       </Button>
                     </div>
                   ) : (
                     <div className="flex items-center gap-2">
                       <Button
                         variant="outline"
                      className="flex items-center gap-2 text-purple-600 border-purple-600 hover:bg-purple-50"
                          onClick={() => setShowAddForm(true)}
                       >
                         <Plus className="h-4 w-4" />
                         Add
                       </Button>
                     </div>
                   )}
                 </div>
               </div>
                 <div className="space-y-4 pl-6 pr-6 pb-6">
                     <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
                       <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                         <DialogHeader>
                           <DialogTitle>{editingCertId ? 'Edit Certificate' : 'Add Certificate'}</DialogTitle>
                         </DialogHeader>
                         <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Certification Name</p>
                             <Input 
                               name="certificationName"
                               value={newCert.certificationName}
                               onChange={(e) => {
                                 handleCertificateChange(e);
                                 if (certErrors.certificationName) setCertErrors(prev => ({ ...prev, certificationName: "" }));
                               }}
                               className={certErrors.certificationName ? "border-red-500" :  "edu-form-field"}
                             />
                             {certErrors.certificationName && <p className="text-sm text-red-500 mt-1">{certErrors.certificationName}</p>}
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Certification Level</p>
                             <Select 
                               value={newCert.certificationLevel} 
                               onValueChange={(value) => {
                                 setNewCert(prev => ({ ...prev, certificationLevel: value }));
                                 if (certErrors.certificationLevel) setCertErrors(prev => ({ ...prev, certificationLevel: "" }));
                               }}
                             >
                               <SelectTrigger className={certErrors.certificationLevel ? "border-red-500" :  "edu-form-field"}>
                                 <SelectValue placeholder="Select level" />
                               </SelectTrigger>
                               <SelectContent>
                                 <SelectItem value="Beginner">Beginner</SelectItem>
                                 <SelectItem value="Intermediate">Intermediate</SelectItem>
                                 <SelectItem value="Advance">Advance</SelectItem>
                                 <SelectItem value="Expert">Expert</SelectItem>
                               </SelectContent>
                             </Select>
                             {certErrors.certificationLevel && <p className="text-sm text-red-500 mt-1">{certErrors.certificationLevel}</p>}
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Issuing Organization</p>
                             <Input 
                               name="issuingOrganization"
                               value={newCert.issuingOrganization}
                               onChange={(e) => {
                                 handleCertificateChange(e);
                                 if (certErrors.issuingOrganization) setCertErrors(prev => ({ ...prev, issuingOrganization: "" }));
                               }}
                               className={certErrors.issuingOrganization ? "border-red-500" :  "edu-form-field"}
                             />
                             {certErrors.issuingOrganization && <p className="text-sm text-red-500 mt-1">{certErrors.issuingOrganization}</p>}
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Issue Year</p>
                             <Input 
                               name="issueYear"
                               type="number"
                               value={newCert.issueYear}
                               onChange={(e) => {
                                 handleCertificateChange(e);
                                 if (certErrors.issueYear) setCertErrors(prev => ({ ...prev, issueYear: "" }));
                               }}
                               className={certErrors.issueYear ? "border-red-500" :  "edu-form-field"}
                             />
                             {certErrors.issueYear && <p className="text-sm text-red-500 mt-1">{certErrors.issueYear}</p>}
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Expiry Year</p>
                             <Input 
                               name="expiryYear"
                               type="number"
                               value={newCert.expiryYear}
                               onChange={(e) => {
                                 handleCertificateChange(e);
                                 if (certErrors.expiryYear) setCertErrors(prev => ({ ...prev, expiryYear: "" }));
                               }}
                               className={certErrors.expiryYear ? "border-red-500" :  "edu-form-field"}
                             />
                             {certErrors.expiryYear && <p className="text-sm text-red-500 mt-1">{certErrors.expiryYear}</p>}
                           </div>
                           {certError && (
                             <div className="md:col-span-2">
                               <p className="text-sm text-red-500">{certError}</p>
                             </div>
                           )}
                           <div className="md:col-span-2 flex gap-2 justify-end mt-4">
                             <Button
                               variant="outline"
                               size="sm"
                               className="text-red-600 border-red-600 hover:bg-red-50"
                               onClick={handleCancelCertificate}
                             >
                               <X className="h-4 w-4 mr-1" />
                               Cancel
                             </Button>
                             <Button
                               size="sm"
                               className="bg-green-600 hover:bg-green-700"
                               onClick={handleSaveCertificate}
                             >
                               <Check className="h-4 w-4 mr-1" />
                               Save
                             </Button>
                           </div>
                         </div>
                       </DialogContent>
                     </Dialog>
                     {certificates.map(cert => (
                       <div key={cert.certificationId || cert.id} className="relative border-l-2 border-purple-500 pl-4 ">
                         <div className="absolute top-0 right-0 flex gap-1">
                           <Button
                             size="sm"
                             variant="outline"
                             onClick={() => handleEditCertificate(cert.certificationId || cert.id)}
                             className="h-8 w-8 p-0 text-purple-600 border-purple-600 hover:bg-purple-50"
                           >
                             <Pencil className="h-3 w-3" />
                           </Button>
                           <Button
                             size="sm"
                             variant="outline"
                             className="h-8 w-8 p-0 text-red-600 border-red-600 hover:bg-red-50"
                             onClick={() => handleRemoveCertificate(cert.certificationId || cert.id)}
                           >
                             <Trash2 className="h-3 w-3" />
                           </Button>
                         </div>
                         <h4 className="font-medium">{cert.certificationName}</h4>
                         <p className="text-sm text-gray-500">{cert.issuingOrganization} - {cert.certificationLevel}</p>
                         <p className="text-sm text-gray-500">{cert.issueYear} - {cert.expiryYear}</p>
                       </div>
                     ))}
                   
                   
                 </div>
             </Card>
                
               </div>
             
           </div>
         </div>
       </div>
     
   );
 }
