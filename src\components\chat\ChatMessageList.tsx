import React, { useEffect, useRef } from 'react';
import { ChatMessage } from '@/types';

interface ChatMessageListProps {
  messages: ChatMessage[];
  currentUserId: string;
  className?: string;
}

export const ChatMessageList: React.FC<ChatMessageListProps> = ({
  messages,
  currentUserId,
  className = ""
}) => {
  const messageEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messageEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const formatMessageTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    }).format(date);
  };

  if (messages.length === 0) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <p className="text-gray-500">No messages yet</p>
          <p className="text-sm text-gray-400">Send a message to start the conversation</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {messages.map((msg) => {
        const isMyMessage = msg.senderId === currentUserId;
        
        return (
          <div
            key={msg.id}
            className={`flex ${isMyMessage ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[70%] rounded-lg p-3 ${
                isMyMessage
                  ? 'bg-purple-600 text-white rounded-br-none'
                  : 'bg-white text-gray-800 rounded-bl-none shadow-sm'
              }`}
            >
              <p className="break-words">{msg.content}</p>
              <span
                className={`text-xs mt-1 block text-right ${
                  isMyMessage ? 'text-purple-200' : 'text-gray-500'
                }`}
              >
                {formatMessageTime(msg.timestamp)}
              </span>
            </div>
          </div>
        );
      })}
      <div ref={messageEndRef} />
    </div>
  );
};