import { useState  ,useEffect} from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "react-oidc-context";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { UserRole } from "@/types";
import { generateAvatarUrl } from "@/lib/utils";
import { toast } from "sonner"; 
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { setUserData, setSelectedRole } from "@/redux/userSlice";
import { clearUserData } from '@/redux/userSlice';
import { useUserRole } from "@/hooks/useUserRole";
import logo from "@/assets/educonnect-logo.png";
export function Header() {
  const auth = useAuth();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isAuthenticated = auth.isAuthenticated;
  const dispatch = useDispatch();
  const userData = useSelector((state: any) => state.user.userData);
  const user = userData
      ? {
          id: userData.id,
          name: userData.name || "User",
          email: userData.email || "",
          role: userData.roles?.[0].role, // or adjust as needed
          avatar: generateAvatarUrl(userData.name || "User", "3498db"),
        }
      : null;
 const { selectedRole } = useUserRole();  

  useEffect(() => {
      if (auth.isAuthenticated && auth.user) {
      fetch("/api/userManagement/v1/me", {
        headers: {
          'Authorization': `Bearer ${auth.user.access_token}`
        }
      })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
       }).then((result) =>{
           dispatch(setUserData(result));
        })
      
      //.then((result) => setUserData(result))
      .catch((error) => console.error("Error fetching data:", error));
      
    }
    }, [auth.isAuthenticated, auth.user]);
  
  
  const handleProfileClick = () => {
    navigate('/profile');
  };
  const handleMembershipClick=()=>{
    navigate('/membershipPage');
  };  
  
  // Function to handle the sign in using OIDC
  const handleSignIn = async () => {
    setIsSubmitting(true);
    try {
      await auth.signinRedirect();
    } catch (error: any) {
      toast.error(`Authentication failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };
  
  const handleSignUp  = async () => {
   // window.location.href = "https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/signup?client_id=76u1v7el416ebllhpbhtqpmlh0&code_challenge=Cjh7j5XvSKwPZ5ahIhP5j2tuEvZiuoSm811Q62N0wFs&code_challenge_method=S256&redirect_uri=http%3A%2F%2Flocalhost%3A8081%2Flogin%2Foauth2%2Fcode%2Fcognito&response_type=code&scope=email+openid+phone&state=80e73e7091c04c30a0c4904373b2096f";
     setIsSubmitting(true);
    
    try {
      // Store form values in localStorage to access after redirect back from Cognito
    //  localStorage.setItem("registerFormData", JSON.stringify(form.getValues()));
      
      // Redirect to Cognito signup page
      await auth.signinRedirect({ prompt: "login" });
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };
  const handleLogout = () => {
    auth.removeUser();
    dispatch(clearUserData()); // Clears user data from Redux
    window.location.href = window.location.origin + "/logout";
  };

  const getDashboardLink = () => {
    switch (selectedRole) {
      case UserRole.TEACHER:
        return "/teacher-dashboard";
      case UserRole.STUDENT:
        return "/student-dashboard";
      case UserRole.PARENT:
        return "/parent-dashboard";
      default:
        return "/dashboard";
    }
  };

  const getDashboardText = () => {
    switch (selectedRole) {
      case UserRole.TEACHER:
        return "Teacher Dashboard";
      case UserRole.STUDENT:
        return "Student Dashboard";
      case UserRole.PARENT:
        return "Parent Dashboard";
      default:
        return "Dashboard";
    }
  };

  return (
   <header className="sticky top-0 z-50 w-full bg-background/95 backdrop-blur h-14">
      <div className="container flex h-14 items-center justify-between">
        <div className="flex items-center gap-2">
          <Link to="/" className="flex items-center gap-2">
            <img src={logo} alt="EduConnect Logo" className="h-12 w-12 object-contain" />
            <span className="text-xl font-semibold text-foreground hidden sm:inline">EduConnect</span>
          </Link>
        </div>

        <nav className="hidden md:flex items-center gap-6">
          {isAuthenticated && <>
              
              
            </>}
        </nav>

        <div className="flex items-center gap-4">
          {isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user?.avatar} alt={user?.name} />
                    <AvatarFallback>{user?.name?.charAt(0)}</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{user?.name}</p>
                    <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
                    <p className="text-xs leading-none text-muted-foreground capitalize">{selectedRole}</p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleProfileClick}>
                  Profile
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleMembershipClick}>
                  Membership
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to={getDashboardLink()}>{getDashboardText()}</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/blogs">Blogs</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/profile">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>Log out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex items-center gap-2">
              <Button
                                    className="bg-purple-600 hover:bg-purple-700 ml-auto flex items-center gap-2 rounded-full px-8"

              disabled={isSubmitting || auth.isLoading}
              onClick={handleSignIn}
            >
              {isSubmitting || auth.isLoading ? "Signing in..." : "Sign In"}
            </Button>
              <Button onClick={handleSignUp}                                    
              className="bg-purple-600 hover:bg-purple-700 ml-auto flex items-center gap-2 rounded-full px-8">
                Sign up
              </Button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}

export default Header;
function dispatch(arg0: any): any {
  throw new Error("Function not implemented.");
}

