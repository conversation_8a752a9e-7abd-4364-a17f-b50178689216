import { Card, CardContent } from "@/components/ui/card";
import { SyllabusTopicList, type SyllabusTopic } from "@/components/syllabus/SyllabusTopicList";
import React, { useState, useEffect, useRef } from "react";
import { toast } from "sonner";
import { getAllSyllabus } from "@/services/syllabusService";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { SyllabusTopicForm } from "@/components/syllabus/SyllabusTopicForm";
import { useAuth } from  "react-oidc-context";
 import { generateAvatarUrl } from "@/lib/utils";
import { UserRole } from "@/types";
import { useParams, useNavigate, Link } from "react-router-dom";

export default function StudentSyllabusTab({classId}){
    
      
    const auth= useAuth();
    // Get user from OIDC
    const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
    } : null;
    const [syllabusTopics, setSyllabusTopics] = useState<SyllabusTopic[]>();
      const [isAddTopicOpen, setIsAddTopicOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const hasFetched = useRef(false);

  const fetchSyllabus = async () => {
    if (hasFetched.current) return;
    hasFetched.current = true;
    try {
      setIsLoading(true);
      const data = await getAllSyllabus(auth.user?.access_token ,classId);
      setSyllabusTopics(data);
      console.log(syllabusTopics);
      
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching syllabus:", error);
      toast.error("Failed to load syllabus");
      setIsLoading(false);
    }
  };

  const handleUpdateTopicStatus = (topicId: string, newStatus: SyllabusTopic['status']) => {
    setSyllabusTopics(prev => prev.map(topic => topic.id === topicId ? {
      ...topic,
      status: newStatus
    } : topic));
    toast.success(`Topic status updated to ${newStatus}`);
  };

  const handleAddTopic = (data: {
      title: string;
      description: string;
    }) => {
      const newTopic: SyllabusTopic = {
        id: Date.now().toString(),
        title: data.title,
        content: data.description,
        status: 'OPEN',
        links: []
      };
      setSyllabusTopics(prev => [...prev, newTopic]);
      setIsAddTopicOpen(false);
      toast.success("Topic added successfully");
    };
  
  useEffect(() => {
      fetchSyllabus();
    }, []);
  
    
 return <div>
            <SyllabusTopicList
            topics={syllabusTopics}
            onUpdateStatus={handleUpdateTopicStatus}
            onAddTopic={() => setIsAddTopicOpen(true)}
            onRefresh={fetchSyllabus}
            />
       
        <Dialog open={isAddTopicOpen} onOpenChange={setIsAddTopicOpen}>
        <DialogContent>
            <DialogHeader>
            <DialogTitle>Add New Topic</DialogTitle>
            </DialogHeader>
            <SyllabusTopicForm onSubmit={handleAddTopic} onCancel={() => setIsAddTopicOpen(false)} />
        </DialogContent>
        </Dialog>
    </div>
}
