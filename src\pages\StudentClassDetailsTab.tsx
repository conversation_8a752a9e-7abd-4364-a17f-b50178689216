import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, BookOpen ,Users,FileText} from "lucide-react";
import { RichTextEditor } from "@/components/ui/rich-text-editor";

interface StudentClassDetailsTabProps {
  classId: string;
  editedClass: any;
}

export default function StudentClassDetailsTabStudentClassDetailsTab({ classId, editedClass}: StudentClassDetailsTabProps) {
  return (
        <div className="bg-gray-50 -m-4 md:-m-6 p-4 md:p-6 min-h-[400px]">
          <div className="space-y-6">
            {/* Class Information Cards */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6  ">
                  {/* Basic Information */}
                  <Card className="shadow-sm">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                        <BookOpen className="h-5 w-5 mr-2 text-primary" />
                        Basic Information
                      </h3>
                      <div className="space-y-4">
                        <div className="space-y-2">
                           <label className="text-sm font-medium text-foreground">Class Name</label>
            <Input className="edu-form-field" value={editedClass?.className || ''} readOnly placeholder="Enter class name" />
          
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Course Title</label>
            <Input className="edu-form-field" value={editedClass?.subjectName || ''} readOnly placeholder="Enter course title" />
         
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Class Type</label>
                                      <Input className="edu-form-field"value={editedClass?.classType || ''} readOnly placeholder="Select class type" />

                    </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Class Details */}
                  <Card className="shadow-sm">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                        <Users className="h-5 w-5 mr-2 text-primary" />
                        Class Details
                      </h3>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Batch Name</label>
                                    <Input  className="edu-form-field" value={editedClass?.batchName || ''} readOnly placeholder="Enter Batch name" />

                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Level</label>
                                      <Input className="edu-form-field" value={editedClass?.level || ''} readOnly placeholder="Enter course title" />

                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Capacity</label>
                                      <Input className="edu-form-field" value={editedClass?.capacity || ''} readOnly placeholder="Enter Description" />

                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                {/* Description */}
                <Card className="shadow-sm">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                      <FileText className="h-5 w-5 mr-2 text-primary" />
                      Description
                    </h3>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-foreground">Class Description</label>
                      <RichTextEditor
                        value={editedClass?.description || ""}
                        onChange={() => {}}
                        placeholder="Enter a detailed description of your class..."
                        readOnly
                      />
                    </div>
                  </CardContent>
                </Card>
          </div>
        </div>
       
  );
}