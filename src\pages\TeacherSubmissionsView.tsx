import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { 
  ArrowLeft, 
  Calendar, 
  Award, 
  Users, 
  CheckCircle2, 
  Clock,
  AlertCircle,
  Search,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { format } from 'date-fns';
import React, { useCallback ,useEffect } from "react";
import { useAuth } from "react-oidc-context"; // Updated import
import { toast } from "sonner";
import { AssignmentSubmission } from "@/types";


import { getAllSubmisionsForAssignment,gradeAssignmentByTeacher} from "@/services/assignmentService";

interface StudentSubmission {
  id: string;
  studentName: string;
  studentEmail: string;
  submittedAt?: Date;
  status: 'pending' | 'submitted' | 'graded';
  score?: number;
  answers: Record<string, string>;
}

interface TeacherSubmissionsViewProps {
  assignment: any;
  assignmentId: string;
  classId: string;
  onBack: () => void;
}

export function TeacherSubmissionsView({ assignment, assignmentId, classId, onBack }: TeacherSubmissionsViewProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedSubmission, setExpandedSubmission] = useState<string | null>(null);
  const [gradingScores, setGradingScores] = useState<Record<string, Record<string, number>>>({});
const [fetchedSubmissions, setFetchedSubmissions] = useState<any[]>([]);
 const [submissionsLoading, setSubmissionsLoading] = useState(false);
  
  const filteredSubmissions = fetchedSubmissions.filter(sub => 
    (sub.studentName || '').toLowerCase().includes(searchQuery.toLowerCase())
  );
 const auth = useAuth();

  const submittedCount = fetchedSubmissions.filter(s => s.status === 'SUBMITTED' || s.status === 'GRADED').length;
  const gradedCount = fetchedSubmissions.filter(s => s.status === 'GRADED').length;
  const pendingCount = fetchedSubmissions.filter(s => s.status === 'PENDING').length;
const fetchSubmissions = useCallback(async () => {
  console.log("fetchSubmissions triggered", { token: !!auth?.user?.access_token, assignmentId });
  if (!auth?.user?.access_token || !classId) return;
  setSubmissionsLoading(true);
  try {
    let data;
     data = await getAllSubmisionsForAssignment(auth.user.access_token,assignmentId);
   console.log(data)
   // ensure we store an array result directly on fetchedAssignments
    const list = Array.isArray(data) ? data : (data?.content || []);
    setFetchedSubmissions(list);


    console.log("fetched assignments:", list);
  } catch (err) {
    console.error("Failed to load assignments:", err);
    toast.error("Failed to load assignments");
  } finally {
    setSubmissionsLoading(false);
  }
}, [auth?.user?.access_token, classId]);

useEffect(() => {
  fetchSubmissions();
}, [fetchSubmissions]);


  const toggleExpand = (submissionId: string) => {
    setExpandedSubmission(expandedSubmission === submissionId ? null : submissionId);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'SUBMITTED': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'GRADED': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING': return <AlertCircle className="w-4 h-4" />;
      case 'SUBMITTED': return <Clock className="w-4 h-4" />;
      case 'GRADED': return <CheckCircle2 className="w-4 h-4" />;
      default: return null;
    }
  };

  const handleSubmitGrade = async (submissionId: string) => {
    try {
      const submission = filteredSubmissions.find(s => s.id === submissionId);
      if (!submission?.answers) return;
      
      const answerMarks = [];
      
      submission.answers.forEach((answer) => {
        const inputElement = document.getElementById(`score-${submissionId}-${answer.questionId}`) as HTMLInputElement;
        if (inputElement && inputElement.value) {
          answerMarks.push({
            questionId: answer.questionId,
            obtainedMarks: parseInt(inputElement.value) || 0,
            teacherComment: ""
          });
        }
      });
      
      const payload = {
        submissionId,
        answerMarks,
        teacherFeedback: "Grade submitted"
      };
      
      await gradeAssignmentByTeacher(auth.user.access_token, payload);
      
      toast.success('Grade submitted successfully!');
      await fetchSubmissions();
    } catch (error) {
      console.error('Failed to submit grade:', error);
      toast.error('Failed to submit grade');
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <Button 
        variant="ghost" 
        className="mb-6"
        onClick={onBack}
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Assignments
      </Button>

      <Card className="p-8 mb-6 border-2">
        <h1 className="text-gray-900 mb-2">{assignment.title}</h1>
        <p className="text-gray-600 mb-4">{assignment.description?.replace(/<[^>]*>/g, '') || ''}</p>
        
        <div className="flex flex-wrap items-center gap-4 text-gray-600">
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span>Due {format(assignment.dueDate, 'MMM d, yyyy')}</span>
          </div>
          <div className="flex items-center gap-2">
            <Award className="w-4 h-4" />
            <span>{assignment.points} points</span>
          </div>
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            <span>{assignment.totalStudents} students</span>
          </div>
        </div>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card className="p-6 border-2 border-blue-200 bg-blue-50/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 mb-1">Submitted</p>
              <p className="text-gray-900">{submittedCount}/{assignment.totalStudents}</p>
            </div>
            <div className="w-12 h-12 rounded-full bg-blue-200 flex items-center justify-center">
              <CheckCircle2 className="w-6 h-6 text-blue-700" />
            </div>
          </div>
        </Card>

        <Card className="p-6 border-2 border-green-200 bg-green-50/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 mb-1">Graded</p>
              <p className="text-gray-900">{gradedCount}/{submittedCount}</p>
            </div>
            <div className="w-12 h-12 rounded-full bg-green-200 flex items-center justify-center">
              <Award className="w-6 h-6 text-green-700" />
            </div>
          </div>
        </Card>

        <Card className="p-6 border-2 border-yellow-200 bg-yellow-50/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 mb-1">Pending</p>
              <p className="text-gray-900">{pendingCount} Students</p>
            </div>
            <div className="w-12 h-12 rounded-full bg-yellow-200 flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-yellow-700" />
            </div>
          </div>
        </Card>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <Input
            placeholder="Search students by name or email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="edu-form-field pl-10"
          />
        </div>
      </div>

      {/* Submissions List */}
      <div className="space-y-4">
        {filteredSubmissions.map((submission) => (
          <Card key={submission.id} className="border-2 hover:border-blue-300 transition-colors">
            <div 
              className="p-6 cursor-pointer"
              onClick={() => toggleExpand(submission.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 flex-1">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center text-white">
                    {(submission.studentName || 'Student').split(' ').map(n => n[0]).join('')}
                  </div>
                  
                  <div className="flex-1">
                    <h3 className="text-gray-900">{submission.studentName || 'Student Name'}</h3>
                    <p className="text-gray-600">ID: {submission.studentId}</p>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                    {submission.submittedDate && (
                      <div className="text-gray-600 text-sm">
                        Submitted {format(new Date(submission.submittedDate), 'MMM d, h:mm a')}
                      </div>
                    )}
                    
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(submission.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(submission.status)}
                          {submission.status}
                        </div>
                      </Badge>

                      {submission.status === 'GRADED' && (
                        <div className="text-green-600 text-sm">
                          {submission.obtainedMarks}/{submission.totalMarks}
                        </div>
                      )}

                      {expandedSubmission === submission.id ? (
                        <ChevronUp className="w-5 h-5 text-gray-400" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Expanded Content */}
            {expandedSubmission === submission.id && submission.status !== 'PENDING' && (
              <div className="border-t border-gray-200 p-6 bg-gray-50/50 space-y-6">
                {submission.answers?.map((answer, index) => (
                  <Card key={answer.id} className="p-5 bg-white">
                    <div className="flex items-start gap-3 mb-3">
                      <Badge variant="outline">Q{index + 1}</Badge>
                      <div className="flex-1">
                        <p className="text-gray-900 mb-1">{answer.questionText} </p>
                        <p className="text-gray-600">{answer.maxMarks || 10} points</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <Label className="text-gray-700 mb-1 block">Student Answer:</Label>
                        <div className="p-3 bg-gray-50 rounded border" dangerouslySetInnerHTML={{ __html: answer.studentAnswer || 'No answer provided' }} />
                      </div>
                      
                      {submission.status === 'SUBMITTED' && (
                        <div>
                          <Label htmlFor={`score-${submission.id}-${answer.questionId}`}>
                            Points Awarded (out of {answer.maxMarks || 10})
                          </Label>
                          <Input
                            id={`score-${submission.id}-${answer.questionId}`}
                            type="number"
                            min="0"
                            max={answer.maxMarks || 10}
                            placeholder="Enter score"
                            className="edu-form-field w-32"
                            defaultValue={answer.obtainedMarks}
                          />
                        </div>
                      )}
                      
                      {submission.status === 'GRADED' && (
                        <div className="mt-2">
                          <Badge className={answer.correct ? "bg-green-100 text-green-800 border-green-200" : "bg-red-100 text-red-800 border-red-200"}>
                            {answer.correct ? 'Correct' : 'Incorrect'} - {answer.obtainedMarks} points
                          </Badge>
                        </div>
                      )}
                    </div>
                  </Card>
                ))}

                {submission.status === 'SUBMITTED' && (
                  <div className="flex gap-3 pt-4">
                    <Button 
                      variant="outline"
                      className="flex-1"
                    >
                      Save Progress
                    </Button>
                    <Button 
                      className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                      onClick={() => handleSubmitGrade(submission.id)}
                    >
                      Submit Grade
                    </Button>
                  </div>
                )}
              </div>
            )}
          </Card>
        ))}
      </div>
    </div>
  );
}
