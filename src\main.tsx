
import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { AuthProvider } from 'react-oidc-context'
import { Provider } from "react-redux";
import { store } from "@/redux/store"; // Correct path to your store
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient();

//import {JitsiMeeting} from "@jitsi/react-sdk";
// Configure AWS Cognito with OIDC
const cognitoAuthConfig = {
  authority: "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_CfPZWbR4P",
  client_id: "76u1v7el416ebllhpbhtqpmlh0",
  redirect_uri: window.location.origin + "/login/oauth2/code/cognito",
  response_type: "code",
  scope: "email openid phone",
  automaticSilentRenew: true,
  loadUserInfo: true,
  onSigninCallback: () => {
    // Remove the query parameters that OIDC adds for better UX
    window.history.replaceState({}, document.title, window.location.pathname);
  },
};

// Explicitly create a root element
const root = createRoot(document.getElementById("root")!);

// Render the application with the AuthProvider
root.render(
  <QueryClientProvider client={queryClient}>
    <AuthProvider {...cognitoAuthConfig}>
      <Provider store={store}>
       <App />
      </Provider>
    </AuthProvider>
  </QueryClientProvider>
);
