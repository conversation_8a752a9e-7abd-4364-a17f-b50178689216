import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RichTextEditor } from "@/components/ui/rich-text-editor";

import { 
  Check,
  Clock,
  CircleDot,
  CircleX,
  Plus,
  Edit,
  Save,
  X,
   ChevronDown,
  ChevronUp,
  Link as LinkIcon,
  Trash2,
  Calendar,
  Paperclip,
  FileText,
  BookAIcon
} from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
    import { MoreHorizontal } from "lucide-react";
import { toast } from "sonner";
import { UserRole } from "@/types";
import { useAuth } from "react-oidc-context"; // Updated import
import { generateAvatarUrl } from "@/lib/utils";
import { removeSyllabusById ,EditSyllabusStatus } from "@/services/syllabusService";
import { editSyllabusById } from "@/services/syllabusService"; // Add this import
import { useUserRole } from "@/hooks/useUserRole";
import { Badge } from "@/components/ui/badge";
import { fileDownload } from "@/utils/fileUpload";
import { fileUpload } from "@/utils/fileUpload";
import {  BookOpen, Edit2,  Eye, Download } from "lucide-react";
import { format } from "date-fns";
export interface SyllabusTopic {
  id: string;
  title: string;
  content: string;
  status: 'OPEN' |  'COMPLETED' | 'IN_PROGRESS';
  links: { url: string; title: string }[];
 attachedFiles?: { id: string;
       name: string;
        fileSize: number;
         url: string; 
        downloadUrl:string;
        originalFilename:string;
      }[];
   updatedDate?: Date; // Optional field for last updated date
}

interface SyllabusTopicListProps {
  topics: SyllabusTopic[];
  onUpdateStatus: (topicId: string, status: SyllabusTopic['status']) => void;
  onAddTopic: () => void;
  onRefresh: () => void; 
    onDeleteTopic?: (topicId: string) => void;
}

interface EditableTopicState {
  title: string;
  description: string;
 attachedFiles?: { id: string;
       name: string;
        fileSize: number;
         url: string; 
        downloadUrl:string;
        originalFilename:string;
      }[];
  //fileUrl: [];
}

const getStatusIcon = (status: SyllabusTopic['status']) => {
  switch (status) {
    case 'COMPLETED':
      return <Check className="h-4 w-4 text-green-500" />;
    case 'OPEN':
      return <Clock className="h-4 w-4 text-yellow-500" />;
    case 'IN_PROGRESS':
      return <CircleDot className="h-4 w-4 text-blue-500" />;
    default:
      return <CircleX className="h-4 w-4 text-gray-500" />;
  }
};

const getStatusColor = (status: SyllabusTopic['status']) => {
  switch (status) {
    case 'COMPLETED':
      return 'bg-green-50 text-green-700 border-green-200';
    case 'OPEN':
      return 'bg-yellow-50 text-yellow-700 border-yellow-200';
    case 'IN_PROGRESS':
      return 'bg-blue-50 text-blue-700 border-blue-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
};
const getStatusBadgeVariant = (status: SyllabusTopic['status']) => {
  switch (status) {
    case 'COMPLETED':
      return 'default';
    case 'OPEN':
      return 'secondary';
    case 'IN_PROGRESS':
      return 'outline';
    default:
      return 'secondary';
  }
};

export const SyllabusTopicList: React.FC<SyllabusTopicListProps> = ({
  topics,
  onUpdateStatus,
  onAddTopic,
  onRefresh,
}) => {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editableContent, setEditableContent] = useState<EditableTopicState>({
    title: '',
    description: '',
    
    attachedFiles: []
  });
  const [expandedTopics, setExpandedTopics] = useState<Set<string>>(new Set());
  
  const auth = useAuth(); // Use OIDC auth
  const { selectedRole } = useUserRole();
  // Get user from OIDC
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;
  
  const isTeacher =selectedRole === UserRole.TEACHER ;
  const handleStatusChange = async(topicId: string) => {
    const currentTopic = topics.find(t => t.id === topicId);
    if (!currentTopic) return;

    const statusOrder: SyllabusTopic['status'][] = ['OPEN', 'IN_PROGRESS', 'COMPLETED'];
    const currentIndex = statusOrder.indexOf(currentTopic.status);
    const nextStatus = statusOrder[(currentIndex + 1) % statusOrder.length];
 
    try {
      await EditSyllabusStatus(auth.user?.access_token || '', topicId, nextStatus);
      onUpdateStatus(topicId, nextStatus);
      onRefresh();
      toast.success(`Topic status updated to ${nextStatus}`);
    } catch (error) {
      toast.error('Failed to update topic status');
    }
  }; const handleDeleteTopic = async (topicId: string) => {
    if (!auth.user?.access_token) return;
    try {
      await removeSyllabusById(auth.user.access_token, topicId);
      toast.success("Syllabus topic removed!");
    } catch (error) {
      toast.error("Failed to remove syllabus topic");
    }
      onRefresh(); // <-- refresh the list
    
  };
  const handleEditStart = (topic: SyllabusTopic) => {
    setEditingId(topic.id);
    setEditableContent({
     title: topic.title,
      description: topic.content,
      attachedFiles: topic.attachedFiles || []
    });
  };

  const handleEditCancel = () => {
    setEditingId(null);
    setEditableContent({ title: '', description: '', attachedFiles: [] });
  };

  
const handleAddLink = () => {
  setEditableContent(prev => ({
    ...prev,
    links: [...prev.attachedFiles, { title: "", url: "" }] // Add empty link fields
  }));
};

const handleUpdateLink = (index: number, field: 'url' | 'title', value: string) => {
  setEditableContent(prev => ({
    ...prev,
    links: prev.attachedFiles.map((link, i) =>
      i === index ? { ...link, [field]: value } : link
    )
  }));
};

const handleRemoveLink = (index: number) => {
  setEditableContent(prev => ({
    ...prev,
    attachedFiles: prev.attachedFiles.filter((_, i) => i !== index)
  }));
};
  const handleEditSave= async (topicId: string) => {
        const validLinks = editableContent.attachedFiles.filter(link => link.downloadUrl.trim() && link.originalFilename.trim());

    if (!auth.user?.access_token) return;
    //const validLinks = editableContent.links.filter(link => link.url.trim() && link.title.trim());

    try {
      await editSyllabusById(auth.user.access_token, topicId, {
        title: editableContent.title,
        content: editableContent.description,
        attachedFileIds: editableContent.attachedFiles?.map(file => file.id) || [],
         status: 'OPEN' // Default status, can be changed later
      });
      toast.success("Topic updated successfully");
      setEditingId(null);
          onRefresh(); // <-- Refresh the topic list after editing

      } catch (error) {
      toast.error("Failed to update topic");
    }
  };
const toggleExpanded = (topicId: string) => {
    setExpandedTopics(prev => {
      const newSet = new Set(prev);
      if (newSet.has(topicId)) {
        newSet.delete(topicId);
      } else {
        newSet.add(topicId);
      }
      return newSet;
    });
  };const truncateHtml = (html: string, maxLength: number = 200) => {
    const textContent = html.replace(/<[^>]*>/g, '');
    return textContent.length > maxLength;
  };
  const [uploading, setUploading] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;
    
    setUploading(true);
    try {
      const uploadedFiles = await fileUpload(auth.user?.access_token || '', files);
      setEditableContent(prev => ({
        ...prev,
        attachedFiles: [...prev.attachedFiles, ...uploadedFiles]
      }));
      toast.success("File uploaded successfully");
    } catch (error) {
      toast.error("Failed to upload files");
    } finally {
      setUploading(false);
    }
  };

  // Helper function to truncate filename
  const truncateFilename = (filename: string, maxLength: number = 30) => {
    if (!filename || filename.length <= maxLength) return filename;
    const extension = filename.split('.').pop();
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4);
    return `${truncatedName}...${extension}`;
  };

  return (
    <div className="space-y-4">
      {!topics || topics.length === 0 ? (
       <div className="text-center py-12">
        <BookAIcon className="h-12 w-12 mx-auto text-gray-400 mb-3" />
        <h3 className="text-lg font-medium text-gray-600 mb-2">No Syllabus Topic Found</h3>
        <p className="text-gray-500">
          There are no Syllabus Topics matching your criteria.
        </p>
      </div>
      ) : (
        topics.map((topic) => (
        <Card key={topic.id} className="relative mb-4">
          <CardContent className="p-4">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1">
                {editingId === topic.id ? (
                  <div className="space-y-3">
                    <Input
                      value={editableContent.title}
                      onChange={(e) => setEditableContent(prev => ({ ...prev, title: e.target.value }))}
                      className="edu-form-field mb-2"
                      placeholder="Enter topic title"
                    />
                    <RichTextEditor
                      key={topic.id}
                      value={editableContent.description}
                      onChange={(value) => setEditableContent(prev => ({ ...prev, description: value }))}
                      placeholder="Enter topic description"
                    />
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium">Attached Files</h4>
                        <div className="flex items-center gap-2">
                          <input
                            id="file-upload-edit"
                            type="file"
                            multiple
                            className="hidden"
                            onChange={handleFileUpload}
                            accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.mp4,.mp3"
                            disabled={uploading}
                          />
                          <Button
                            type="button"
                            size="sm"
                            onClick={() => document.getElementById('file-upload-edit')?.click()}
                            className="bg-purple-600 hover:bg-purple-700"
                            disabled={uploading}
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            {uploading ? "Uploading..." : "Add Files"}
                          </Button>
                        </div>
                      </div>
                   {/*   {editableContent.attachedFiles?.map((file, index) => (
                        <div key={file.id || index} className="flex items-center gap-2 p-3 border rounded-lg bg-muted/50">
                          <div className="flex-1">
                            <p className="text-sm font-medium">{file.originalFilename}</p>
                            <p className="text-xs text-muted-foreground">
                              {file.fileSize || `${Math.round(file.fileSize / 1024)} KB`}
                            </p>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveLink(index)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}*/}

                       {/* Resource Links */}
                {editableContent.attachedFiles && editableContent.attachedFiles.length > 0 && (
                  <div className="space-y-2 mt-4">
                    <div className="grid grid-cols-2 gap-2">
                     {editableContent.attachedFiles.map((file, index) => (
                     <>
                     <button
                          key={file.id}
                        // onClick={() => fileDownload(auth.user?.access_token || '', file.id, file.originalFilename)}
                          className="flex items-center gap-2 p-2 rounded-md hover:bg-muted/50 transition-colors group text-left"
                        >
                          <LinkIcon className="h-4 w-4 text-primary" />
                          <span className="text-sm text-foreground group-hover:text-primary transition-colors" title={file.originalFilename}>
                            {truncateFilename(file.originalFilename)}
                          </span>
                        </button>
                        
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveLink(index)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                                                </>
                    ))}
                    </div>
                  </div>
                )}
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      <Button
                        size="sm"
                        type="submit"
                         onClick={() => handleEditSave(topic.id)}
                        className="bg-purple-600 hover:bg-purple-700 flex items-center gap-2"
                      >
                       Save
                      </Button>
                      <Button
                        size="sm"
                        type="button"
                        variant="outline"
                        onClick={handleEditCancel}
                        >
                        
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex-1 min-w-0">
                   <div className="flex items-start justify-between gap-4 mb-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-gray-900 break-words">{topic.title}</h3>
                        <Badge variant="outline" className="text-gray-600 border-gray-300 flex-shrink-0">
                          {topic.attachedFiles?.length || 0} files
                        </Badge>
                      </div>
                      <p
                        className="text-gray-600 break-words"
                        dangerouslySetInnerHTML={{ __html: (topic.content || '').substring(0, 300) + ((topic.content || '').length > 300 ? '...' : '') }}
                      />
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                     <Badge className={topic.status === 'COMPLETED' ? 'bg-green-100 text-green-800 border-green-200' : 'bg-amber-100 text-amber-800 border-amber-200'}>
                        {topic.status}
                      </Badge>
                     {/* Actions for teachers */}
                    {isTeacher && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem
                            onClick={() => handleStatusChange(topic.id)}
                            className="flex items-center gap-2"
                          >
                            <CircleDot className="h-4 w-4" />
                            Change Status
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleEditStart(topic)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit Topic
                          </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteTopic(topic.id)}
                              className="flex items-center gap-2 text-destructive focus:text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                              Delete Topic
                            </DropdownMenuItem>
                          
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                    </div>
                 </div>

                  <div className="flex flex-wrap items-center gap-3 md:gap-4 text-gray-600 mb-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span>{(topic as any).duration || '-'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      <span>{topic.attachedFiles?.length || 0} resources</span>
                    </div>
                   <div className="flex items-center gap-2">
                      <BookOpen className="w-4 h-4" />
                      <span>{(topic as any).moduleCount || 0} modules</span>
                    </div>
                    <span className="text-gray-500">
                      Updated {format(topic.updatedDate ? new Date(topic.updatedDate) : new Date(), 'MMM d, yyyy')}
                   </span>
                  </div>

                 {/* Description */}
                <div className="text-muted-foreground text-sm leading-relaxed">
                
                  {truncateHtml(topic.content) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleExpanded(topic.id)}
                      className="text-primary hover:text-primary/80 p-0 h-auto mt-2 text-sm"
                    >
                      {expandedTopics.has(topic.id) ? (
                        <>
                          <ChevronUp className="h-4 w-4 mr-1" />
                          Show less
                        </>
                      ) : (
                        <>
                          <ChevronDown className="h-4 w-4 mr-1" />
                          Show more
                        </>
                      )}
                    </Button>
                  )}
                </div>

                {/* Attached Files Section */}
                {topic.attachedFiles && topic.attachedFiles.length > 0 && (
                  <div className="space-y-3 border-0">
                
                    <div className="p-3">
                      {topic.attachedFiles.map((file, fileIndex) => (
                        <button 
                          key={fileIndex}
                          onClick={() => fileDownload(auth.user?.access_token || '', file.id, file.originalFilename)}
                          className="flex items-center justify-between p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer w-full"
                        >
                          <div className="flex items-center gap-3">
                            <FileText className="h-4 w-4 text-primary shrink-0" />
                            <span className="text-sm text-foreground font-medium truncate" title={file.originalFilename}>
                              {file.originalFilename.length > 30 ? file.originalFilename.substring(0, 30) + '...' : file.originalFilename}
                            </span>
                          </div>
                         
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Resource Links */}
                {topic.links && topic.links.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-sm font-semibold text-foreground">
                      RESOURCES
                    </h4>
                    <div className="space-y-2">
                      {topic.links.map((link, linkIndex) => (
                        <a
                          key={linkIndex}
                          href={link.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg border border-border/50 hover:bg-muted/50 transition-colors group"
                        >
                          <LinkIcon className="h-4 w-4 text-primary shrink-0" />
                          <span className="text-sm text-foreground group-hover:text-primary transition-colors">
                            {link.title}
                          </span>
                        </a>
                      ))}
                    </div>
                  </div>
                )}

                  

                
                </div>
            )}
            </div>
            </div>
          </CardContent>
        </Card>
        ))
      )}
    </div>
  );
};

