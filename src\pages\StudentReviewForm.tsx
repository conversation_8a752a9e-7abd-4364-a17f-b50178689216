import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Star, Edit } from "lucide-react";
import { toast } from "sonner";
import { createTeacherReview, getStudentReviews, editTeacherReview } from "@/services/reviewService";
import { useAuth } from "react-oidc-context";

interface StudentReviewFormProps {
  teacherId: string;
  teacherName: string;
  courseName?: string;
  classroomId?: string;
  onSubmit: (reviewData: {
    teacherId: string;
    rating: number;
    reviewText: string;
    courseName: string;
  }) => void;
  onCancel: () => void;
}

export default function StudentReviewForm({
  teacherId,
  teacherName,
  courseName = "",
  classroomId ,
  onSubmit,
  onCancel
}: StudentReviewFormProps) {
  const auth = useAuth();
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [reviewText, setReviewText] = useState("");
  const [reviewTitle, setReviewTitle] = useState("");
  const [reviewCategory, setReviewCategory] = useState("TEACHING_QUALITY");
  const [course, setCourse] = useState(courseName);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [reviewData, setReviewData] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const hasFetched = useRef(false);

  const fetchReviews = async (forceRefresh = false) => {
    if (hasFetched.current && !forceRefresh) return;
    if (!forceRefresh) hasFetched.current = true;
    try {
      const response = await getStudentReviews(auth.user?.access_token);
      setReviewData(response);
      
      // If there's existing review data, populate the form fields
      if (response && response.length > 0) {
        const review = response[0];
        setReviewTitle(review.reviewTitle || "");
        setRating(review.rating || 0);
        setReviewCategory(review.reviewCategory || "TEACHING_QUALITY");
        setReviewText(review.reviewText || "");
        setCourse(courseName || "");
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
    }
  };

  useEffect(() => {          
    if (auth.user?.access_token) {
      fetchReviews();
    }
  }, [auth.user?.access_token, courseName]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
   
    if (reviewData && reviewData.length > 0 && !isEditing) {
      toast.error("You have already submitted a review for this teacher");
      return;
    }
   
    if (rating === 0) {
      toast.error("Please select a rating");
      return;
    }
   
    if (reviewText.trim().length < 10) {
      toast.error("Please write at least 10 characters in your review");
      return;
    }
   
    if (!course.trim()) {
      toast.error("Please specify the course name");
      return;
    }

    setIsSubmitting(true);
   
    try {
      const reviewPayload = {
        teacherId,
        classroomId,
        rating,
        reviewTitle: reviewTitle.trim(),
        reviewText: reviewText.trim(),
        reviewCategory
    //    isAnonymous: false
      };
      
      if (isEditing && reviewData && reviewData.length > 0) {
        // Call edit API with review ID
        await editTeacherReview(reviewData[0].id, reviewPayload, auth.user?.access_token);
      } else {
        // Call create API for new reviews
        await createTeacherReview(reviewPayload, auth.user?.access_token);
      }
      
      onSubmit({
        teacherId,
        rating,
        reviewText: reviewText.trim(),
        courseName: course.trim()
      });
     
      toast.success(isEditing ? "Review updated successfully!" : "Review submitted successfully!");
      
      // Refresh the review data after successful submission/update
      await fetchReviews(true);
      setIsEditing(false);
      
      if (!isEditing) {
        setRating(0);
        setReviewText("");
        setReviewTitle("");
        setReviewCategory("TEACHING_QUALITY");
        setCourse("");
      }
    } catch (error) {
      toast.error(isEditing ? "Failed to update review. Please try again." : "Failed to submit review. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (reviewData && reviewData.length > 0 && !isEditing) {
    const review = reviewData[0];
    return (
      <Card className="p-6">
        <div className="mb-4">
          <h3 className="text-lg font-semibold">Your Review for {teacherName}</h3>
          <p className="text-sm text-gray-600">You have already submitted a review for this teacher</p>
        </div>
        <div className="space-y-4">
          <div>
            <Label>Review Title</Label>
            
              <div className="flex items-center gap-6">
              <p className="font-medium">{review.reviewTitle}</p>
              <button 
                type="button" 
                className="text-blue-600 hover:text-blue-800"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="w-4 h-4" />
              </button>
            </div>
          </div>
          <div>
            <Label>Rating</Label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className="w-5 h-5"
                  fill={star <= review.rating ? "#FBBF24" : "none"}
                  color={star <= review.rating ? "#FBBF24" : "#E5E7EB"}
                />
              ))}
              <span className="ml-2 text-sm text-gray-600">{review.rating} stars</span>
            </div>
          </div>
          <div>
            <Label>Category</Label>
            <p className="font-medium">{review.reviewCategory}</p>
          </div>
          <div>
            <Label>Review</Label>
            <p className="text-gray-700">{review.reviewText}</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold">{isEditing ? "Edit" : "Write"} a Review for {teacherName}</h3>
        <p className="text-sm text-gray-600">{isEditing ? "Update your feedback" : "Share your experience to help other students"}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="course">Course Name</Label>
          <Input
            id="course"
            value={course}
             className="edu-form-field"
            onChange={(e) => setCourse(e.target.value)}
            placeholder="e.g., Mathematics 101"
            required
          />
        </div>

        <div>
          <Label htmlFor="reviewTitle">Review Title</Label>
          <Input
            id="reviewTitle"
             className="edu-form-field"
            value={reviewTitle}
            onChange={(e) => setReviewTitle(e.target.value)}
            placeholder="e.g., Excellent Teaching Style"
            required
          />
        </div>

        <div>
          <Label htmlFor="reviewCategory">Review Category</Label>
          <Select value={reviewCategory} onValueChange={setReviewCategory}>
            <SelectTrigger className="edu-form-field">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="TEACHING_QUALITY">Teaching Quality</SelectItem>
              <SelectItem value="COMMUNICATION">Communication</SelectItem>
              <SelectItem value="HELPFULNESS">Helpfulness</SelectItem>
              <SelectItem value="OVERALL">Overall</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Rating</Label>
          <div className="flex items-center space-x-1 mt-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                type="button"
                className="focus:outline-none"
                onMouseEnter={() => setHoverRating(star)}
                onMouseLeave={() => setHoverRating(0)}
                onClick={() => setRating(star)}
              >
                <Star
                  className="w-6 h-6 cursor-pointer transition-colors"
                  fill={star <= (hoverRating || rating) ? "#FBBF24" : "none"}
                  color={star <= (hoverRating || rating) ? "#FBBF24" : "#E5E7EB"}
                />
              </button>
            ))}
            <span className="ml-2 text-sm text-gray-600">
              {rating > 0 && `${rating} star${rating > 1 ? 's' : ''}`}
            </span>
          </div>
        </div>

        <div>
          <Label htmlFor="review">Your Review</Label>
          <Textarea
            id="review"
            value={reviewText}
            onChange={(e) => setReviewText(e.target.value)}
            placeholder="Share your experience with this teacher's teaching style, helpfulness, course content, etc."
            rows={4}
            required
            className="edu-form-field"
            minLength={10}
          />
          <p className="text-xs text-gray-500 mt-1">
            {reviewText.length}/500 characters
          </p>
        </div>

        <div className="flex space-x-3 pt-4">
          <Button
            type="submit"
            disabled={isSubmitting || rating === 0 || reviewText.trim().length < 10}
                                  className="bg-purple-600 hover:bg-purple-700 ml-auto flex-1 items-center gap-2 rounded-full px-8"

          >
            {isSubmitting ? "Submitting..." : isEditing ? "Update Review" : "Submit Review"}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => isEditing ? setIsEditing(false) : onCancel()}
            disabled={isSubmitting}
          >
            {isEditing ? "Cancel Edit" : "Cancel"}
          </Button>
        </div>
      </form>
    </Card>
  );
}