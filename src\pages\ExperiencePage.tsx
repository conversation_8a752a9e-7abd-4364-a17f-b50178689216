import React, { useState, useEffect } from "react";
import { ArrowLeft, Pencil, Calendar, Building, Plus, Check, X, Trash2} from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "react-oidc-context"; // Updated import
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import ProfileSidebar from "@/components/profile/ProfileSidebar";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

import {getSkills, getAllCertificates, addCertificate, updateCertificate, deleteCertificate ,
  getAllExperience, addExperience, updateExperience ,deleteExperience
 

} from "@/services/profileService";

import { start } from "repl";
export default function ExperiencePage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast: uiToast } = useToast();
const auth = useAuth();
  // State for experience data
  const [experiences, setExperiences] = useState([  ]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showAddExpForm, setShowAddExpForm] = useState(false);
  const [newExp, setNewExp] = useState({
    jobTitle: "",
    companyName: "",
    companyLocation: "",
    employmentType: "FULL_TIME",
    startYear: "",
    startMonth: "",
    endYear: "",
    endMonth: "",
    currentlyWorking: false,
    description: ""
  });
  
  const [expErrors, setExpErrors] = useState({
    jobTitle: "",
    companyName: "",
    startYear: "",
    startMonth: ""
  });


const [certificates, setCertificates] = useState([]);
  const [newCert, setNewCert] = useState({
    certificationName: "",
    certificationLevel: "",
    issuingOrganization: "",
    issueYear: "",
    expiryYear:''
  });
  const [editingCertId, setEditingCertId] = useState(null);
  const [certErrors, setCertErrors] = useState({
    certificationName: "",
    certificationLevel: "",
    issuingOrganization: "",
    issueYear: "",
    expiryYear: ""
  });
  
    const fetchCertificates = async () => {
         try {
           const fetchedCertificates = await getAllCertificates(auth.user?.access_token);
           setCertificates(fetchedCertificates);
         } catch (error) {
           console.error('Error fetching Certificate:', error);
         }
     };
     useEffect(() => {
       fetchCertificates();
     }, []);
       const [editSection, setEditSection] = useState<string | null>(null);
  
  const fetchExperience = async () => {
         try {
           const fetchedExp = await getAllExperience(auth.user?.access_token);
           setExperiences(fetchedExp);
         } catch (error) {
           console.error('Error fetching Experiences:', error);
         }
     };
     useEffect(() => {
       fetchExperience();
     }, []);     

  // State for editing
  const [editingExpId, setEditingExpId] = useState<string | null>(null);
  const [tempExp, setTempExp] = useState({
    jobTitle: "",
    companyName: "",
    companyLocation: "",
    startYear: "",
    startMonth: "",
    endMonth: "",
    endYear: "",
    currentlyWorking: false,
    employmentType : "FULL_TIME", 
    description: ""  });
  const [tempCert, setTempCert] = useState({
    name: "",
    issuer: "",
    date: "",
    issuedDesc: ""
  });

  // Handle edit experience
  const handleEditExperience = (id: string) => {
    const exp = experiences.find(e => e.id === id);
    if (exp) {
      setTempExp({
        jobTitle: exp.jobTitle,
        companyName: exp.companyName,
        employmentType: exp.employmentType,
        startYear: exp.startYear,
        startMonth: exp.startMonth,
        endMonth: exp.endMonth,
        endYear: exp.endYear,
        currentlyWorking: exp.currentlyWorking,
        companyLocation: exp.companyLocation,
        description: exp.description
      });
      setEditingExpId(id);
    }
  };
// Handle edit experience
  const handleSaveExperience = (id: string) => {
    const exp = experiences.find(e => e.id === id);
    if (exp) {
      setTempExp({
       jobTitle: exp.jobTitle,
        companyName: exp.companyName,
        employmentType: exp.employmentType,
        startYear: exp.startYear,
        endYear: exp.endYear,
        currentlyWorking: exp.currentlyWorking,
        companyLocation: exp.companyLocation,
        startMonth: exp.startMonth,
        endMonth: exp.endMonth,
        description: exp.description
      });
      setEditingExpId(id);
    }
  };
  
  // Handle form field changes
  const handleExpChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setTempExp(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleExpCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempExp(prev => ({
      ...prev,
      isCurrent: e.target.checked,
      endYear: e.target.checked ? "" : prev.endYear
    }));
  };

  const handleCancel = () => {
    setEditSection(null);
  };

  const handleCancelEditExperience = () => {
    setEditingExpId(null);
    setTempExp({
      jobTitle: "",
      companyName: "",
      companyLocation: "",
      employmentType: "FULL_TIME",
      startYear: "",
      startMonth: "",
      endYear: "",
      endMonth: "",
      currentlyWorking: false,
      description: ""
    });
  };

  const handleSkillsChange = (value: string) => {
    const skillsArray = value.split(',').map(skill => skill.trim()).filter(skill => skill);
    setTempExp(prev => ({
      ...prev,
      skills: skillsArray
    }));
  };

  
  const handleCertificateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setNewCert(prev => ({ ...prev, [name]: value }));
    };
  
    const handleSaveCertificate = async () => {
      const errors = {
        certificationName: !newCert.certificationName ? "Certification name is required" : "",
        certificationLevel: !newCert.certificationLevel ? "Certification level is required" : "",
        issuingOrganization: !newCert.issuingOrganization ? "Issuing organization is required" : "",
        issueYear: !newCert.issueYear ? "Issue year is required" : "",
        expiryYear: !newCert.expiryYear ? "Expiry year is required" : ""
      };
      
      // Validate issue year is less than expiry year
      if (newCert.issueYear && newCert.expiryYear && 
          parseInt(newCert.issueYear) >= parseInt(newCert.expiryYear)) {
        errors.issueYear = "Issue year must be less than expiry year";
      }
      
      setCertErrors(errors);
      
      if (errors.certificationName || errors.certificationLevel || errors.issuingOrganization || errors.issueYear || errors.expiryYear) {
        return;
      }
      
      try {
        if (editingCertId && editingCertId !== null) {
          await updateCertificate(newCert, auth.user?.access_token, editingCertId);
          toast.success('Certificate updated successfully');
        } else {
          await addCertificate(newCert, auth.user?.access_token);
          toast.success('Certificate added successfully');
        }
        const fetchedCertificates = await getAllCertificates(auth.user?.access_token);
        setCertificates(fetchedCertificates);
        setNewCert({ certificationName: '', certificationLevel: '', issuingOrganization: '', issueYear: '' ,expiryYear :''});
        setCertErrors({ certificationName: "", certificationLevel: "", issuingOrganization: "", issueYear: "", expiryYear: "" });
        setEditingCertId(null);
        setShowAddForm(false);
      } catch (error) {
        console.error('Error saving certificate:', error);
        toast.error('Failed to save certificate');
      }
    };
  
    const handleCancelCertificate = () => {
      setNewCert({ certificationName: '', certificationLevel: '', issuingOrganization: '', issueYear: '' ,expiryYear:''});
      setCertErrors({ certificationName: "", certificationLevel: "", issuingOrganization: "", issueYear: "", expiryYear: "" });
      setEditingCertId(null);
      setShowAddForm(false);
    };
  
    const handleEditCertificate = (certId: string) => {
      const certToEdit = certificates.find(cert => cert.certificationId === certId || cert.id === certId);
      console.log('Found certificate to edit:', certToEdit);
      if (certToEdit) {
        setNewCert({
          certificationName: certToEdit.certificationName || '',
          certificationLevel: certToEdit.certificationLevel || '',
          issuingOrganization: certToEdit.issuingOrganization || '',
          issueYear: certToEdit.issueYear || '',
          expiryYear: certToEdit.expiryYear || ''
        });
        setCertErrors({ certificationName: "", certificationLevel: "", issuingOrganization: "", issueYear: "", expiryYear: "" });
        setEditingCertId(certToEdit.certificationId || certToEdit.id);
        setShowAddForm(true);
      }
    };
  
  
    const handleRemoveCertificate = async (certId: string) => {
      try {
        await deleteCertificate(auth.user?.access_token, certId);
        const fetchedCertificates = await getAllCertificates(auth.user?.access_token);
        setCertificates(fetchedCertificates);
        toast.success('Certificate removed successfully');
      } catch (error) {
        console.error('Error removing certificate:', error);
        toast.error('Failed to remove certificate');
      }
    };

  const handleExpFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewExp(prev => ({ ...prev, [name]: value }));
  };

  const handleSaveNewExperience = async () => {
    const errors = {
      jobTitle: !newExp.jobTitle ? "Job title is required" : "",
      companyName: !newExp.companyName ? "Company name is required" : "",
      startYear: !newExp.startYear ? "Start year is required" : "",
      startMonth: ""
    };
    
    // Validate start year is less than end year
    if (!newExp.currentlyWorking && newExp.endYear && 
        parseInt(newExp.startYear) > parseInt(newExp.endYear)) {
      errors.startYear = "Start year must be less than end year";
    }
    
    // Validate start month cannot be after end month in the same year
    if (!newExp.currentlyWorking && newExp.startYear && newExp.endYear && 
        newExp.startMonth && newExp.endMonth &&
        parseInt(newExp.startYear) === parseInt(newExp.endYear) &&
        parseInt(newExp.startMonth) > parseInt(newExp.endMonth)) {
      errors.startMonth = "Start month cannot be after end month in the same year";
    }
    
    setExpErrors(errors);
    
    if (errors.jobTitle || errors.companyName || errors.startYear || errors.startMonth) {
      return;
    }
    
    try {
      await addExperience(newExp, auth.user?.access_token);
      const fetchedExp = await getAllExperience(auth.user?.access_token);
      setExperiences(fetchedExp);
      setNewExp({
        jobTitle: "",
        companyName: "",
        companyLocation: "",
        employmentType: "FULL_TIME",
        startYear: "",
        startMonth: "",
        endYear: "",
        endMonth: "",
        currentlyWorking: false,
        description: ""
      });
      setExpErrors({ jobTitle: "", companyName: "", startYear: "", startMonth: "" });
      setShowAddExpForm(false);
      toast.success('Experience added successfully');
    } catch (error) {
      console.error('Error adding experience:', error);
      toast.error('Failed to add experience');
    }
  };

  const handleCancelNewExperience = () => {
    setNewExp({
      jobTitle: "",
      companyName: "",
      companyLocation: "",
      employmentType: "FULL_TIME",
      startYear: "",
      startMonth: "",
      endYear: "",
      endMonth: "",
      currentlyWorking: false,
      description: ""
    });
    setExpErrors({ jobTitle: "", companyName: "", startYear: "", startMonth: "" });
    setShowAddExpForm(false);
  };

  const handleUpdateExperience = async (id: string) => {
    // Validate required fields
    if (!tempExp.jobTitle || !tempExp.companyName || !tempExp.startYear) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    // Validate start year is less than end year
    if (!tempExp.currentlyWorking && tempExp.endYear && 
        parseInt(tempExp.startYear) > parseInt(tempExp.endYear)) {
      toast.error('Start year must be less than end year');
      return;
    }
    
    try {
      await updateExperience(tempExp, auth.user?.access_token, id);
      const fetchedExp = await getAllExperience(auth.user?.access_token);
      setExperiences(fetchedExp);
      setEditingExpId(null);
      toast.success('Experience updated successfully');
    } catch (error) {
      console.error('Error updating experience:', error?.response?.data?.details);
      const errorMessage = error?.response?.data?.details || 'Failed to update experience';
      toast.error(errorMessage);
    }
  };

  const handleRemoveExperience = async (id: string) => {
    try {
      await deleteExperience(auth.user?.access_token, id);
      const fetchedExp = await getAllExperience(auth.user?.access_token);
      setExperiences(fetchedExp);
      toast.success('Experience removed successfully');
    } catch (error) {
      console.error('Error removing experience:', error);
      toast.error('Failed to remove experience');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="p-4 bg-white">
        <div className="flex items-center gap-2">
          <Link to="/teacher-dashboard" className="flex items-center">
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </Link>
          <h1 className="text-lg font-medium">Experience</h1>
        </div>
      </div>

      <div className="container mx-auto max-w-4xl py-6 px-4">
        <div className="grid grid-cols-12 gap-6">
          {/* Sidebar */}
          <div className="col-span-12 md:col-span-3">
            <ProfileSidebar activePage="experience" />
          </div>

          {/* Main Content */}
          <div className="col-span-12 md:col-span-9 space-y-6">
            <Card className="overflow-hidden">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold">Work Experience</h2>
                  <Button
                    variant="outline"
                    className="flex items-center gap-2 text-purple-600 border-purple-600 hover:bg-purple-50"
                    onClick={() => setShowAddExpForm(true)}
                  >
                    <Plus className="h-4 w-4" />
                    Add Experience
                  </Button>
                </div>

                <div className="space-y-8">
                  <Dialog open={showAddExpForm} onOpenChange={setShowAddExpForm}>
                    <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Add Work Experience</DialogTitle>
                      </DialogHeader>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Job Title</p>
                          <Input 
                            name="jobTitle"
                            value={newExp.jobTitle}
                            onChange={(e) => {
                              handleExpFormChange(e);
                              if (expErrors.jobTitle) setExpErrors(prev => ({ ...prev, jobTitle: "" }));
                            }}
                            className={expErrors.jobTitle ? "border-red-500" : "edu-form-field"}
                          />
                          {expErrors.jobTitle && <p className="text-sm text-red-500 mt-1">{expErrors.jobTitle}</p>}
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Company Name</p>
                          <Input 
                            name="companyName"
                            value={newExp.companyName}
                            onChange={(e) => {
                              handleExpFormChange(e);
                              if (expErrors.companyName) setExpErrors(prev => ({ ...prev, companyName: "" }));
                            }}
                            className={expErrors.companyName ? "border-red-500" : "edu-form-field"}
                          />
                          {expErrors.companyName && <p className="text-sm text-red-500 mt-1">{expErrors.companyName}</p>}
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Company Location</p>
                          <Input 
                            name="companyLocation"
                            value={newExp.companyLocation}
                            className="edu-form-field"
                            onChange={handleExpFormChange}
                          />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Employment Type</p>
                          <Select value={newExp.employmentType} onValueChange={(value) => setNewExp(prev => ({ ...prev, employmentType: value }))}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="FULL_TIME">Full Time</SelectItem>
                              <SelectItem value="PART_TIME">Part Time</SelectItem>
                              <SelectItem value="CONTRACT">Contract</SelectItem>
                              <SelectItem value="INTERNSHIP">Internship</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Start Year</p>
                          <Input 
                            name="startYear"
                            type="number"
                            value={newExp.startYear}
                            onChange={(e) => {
                              handleExpFormChange(e);
                              if (expErrors.startYear) setExpErrors(prev => ({ ...prev, startYear: "" }));
                            }}
                            className={expErrors.startYear ? "border-red-500" : "edu-form-field"}
                          />
                          {expErrors.startYear && <p className="text-sm text-red-500 mt-1">{expErrors.startYear}</p>}
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Start Month</p>
                          <Input 
                            name="startMonth"
                            type="number"
                            min="1"
                            max="12"
                            value={newExp.startMonth}
                            onChange={(e) => {
                              handleExpFormChange(e);
                              if (expErrors.startMonth) setExpErrors(prev => ({ ...prev, startMonth: "" }));
                            }}
                            className={expErrors.startMonth ? "border-red-500" : "edu-form-field"}
                          />
                          {expErrors.startMonth && <p className="text-sm text-red-500 mt-1">{expErrors.startMonth}</p>}
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">End Year</p>
                          <Input 
                            name="endYear"
                            type="number"
                            className="edu-form-field"
                            value={newExp.endYear}
                            onChange={handleExpFormChange}
                            disabled={newExp.currentlyWorking}
                          />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">End Month</p>
                          <Input 
                            name="endMonth"
                            type="number"
                            min="1"
                            max="12"
                            className="edu-form-field"
                            value={newExp.endMonth}
                            onChange={handleExpFormChange}
                            disabled={newExp.currentlyWorking}
                          />
                        </div>
                        <div className="md:col-span-2">
                          <div className="flex items-center mb-2">
                            <input
                              type="checkbox"
                              checked={newExp.currentlyWorking}
                              onChange={(e) => setNewExp(prev => ({ ...prev, currentlyWorking: e.target.checked }))}
                              className="edu-form-field w-4 h-4 mr-2"
                            />
                            <label>Currently working here</label>
                          </div>
                        </div>
                        <div className="md:col-span-2">
                          <p className="text-sm text-gray-500 mb-1">Description</p>
                          <Textarea 
                            name="description"
                            value={newExp.description}
                            onChange={handleExpFormChange}
                            rows={3}
                          />
                        </div>
                        <div className="md:col-span-2 flex gap-2 justify-end mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 border-red-600 hover:bg-red-50"
                            onClick={handleCancelNewExperience}
                          >
                            <X className="h-4 w-4 mr-1" />
                            Cancel
                          </Button>
                          <Button
                            size="sm"
                            className="bg-green-600 hover:bg-green-700"
                            onClick={handleSaveNewExperience}
                          >
                            <Check className="h-4 w-4 mr-1" />
                            Save
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                  {experiences.map(exp => (
                    <div key={exp.id} className="border-b pb-6 last:border-none last:pb-0">
                      <Dialog open={editingExpId === exp.id} onOpenChange={(open) => {
                        if (!open) handleCancelEditExperience();
                      }}>
                        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle>Edit Work Experience</DialogTitle>
                          </DialogHeader>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                            <div>
                              <p className="text-sm text-gray-500 mb-1">Job Title</p>
                              <Input 
                                name="jobTitle"
                                value={tempExp.jobTitle}
                                onChange={handleExpChange}
                                className="edu-form-field"
                              />
                            </div>
                            <div>
                              <p className="text-sm text-gray-500 mb-1">Company Name</p>
                              <Input 
                                name="companyName"
                                value={tempExp.companyName}
                                onChange={handleExpChange}
                                className="edu-form-field"
                              />
                            </div>
                            <div>
                              <p className="text-sm text-gray-500 mb-1">Company Location</p>
                              <Input 
                                name="companyLocation"
                                className="edu-form-field"
                                value={tempExp.companyLocation}
                                onChange={handleExpChange}
                              />
                            </div>
                            <div>
                              <p className="text-sm text-gray-500 mb-1">Employment Type</p>
                              <Select value={tempExp.employmentType} onValueChange={(value) => setTempExp(prev => ({ ...prev, employmentType: value }))}>
                                <SelectTrigger className="edu-form-field">
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="FULL_TIME">Full Time</SelectItem>
                                  <SelectItem value="PART_TIME">Part Time</SelectItem>
                                  <SelectItem value="CONTRACT">Contract</SelectItem>
                                  <SelectItem value="INTERNSHIP">Internship</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500 mb-1">Start Year</p>
                              <Input 
                                name="startYear"
                                type="number"
                                value={tempExp.startYear}
                                onChange={handleExpChange}
                                className="edu-form-field"
                              />
                            </div>
                            <div>
                              <p className="text-sm text-gray-500 mb-1">Start Month</p>
                              <Input 
                                name="startMonth"
                                type="number"
                                min="1"
                                max="12"
                                className="edu-form-field"
                                value={tempExp.startMonth}
                                onChange={handleExpChange}
                              />
                            </div>
                            <div>
                              <p className="text-sm text-gray-500 mb-1">End Year</p>
                              <Input 
                                name="endYear"
                                type="number"
                                  className="edu-form-field"
                                value={tempExp.endYear}
                                onChange={handleExpChange}
                                disabled={tempExp.currentlyWorking}
                              />
                            </div>
                            <div>
                              <p className="text-sm text-gray-500 mb-1">End Month</p>
                              <Input 
                                name="endMonth"
                                type="number"
                                min="1"
                                className="edu-form-field"
                                max="12"
                                value={tempExp.endMonth}
                                onChange={handleExpChange}
                                disabled={tempExp.currentlyWorking}
                              />
                            </div>
                            <div className="md:col-span-2">
                              <div className="flex items-center mb-2">
                                <input
                                  type="checkbox"
                                  checked={tempExp.currentlyWorking}
                                  onChange={(e) => setTempExp(prev => ({ ...prev, currentlyWorking: e.target.checked }))}                                  
                                  className="edu-form-field w-4 h-4 mr-2"
                                />
                                <label>Currently working here</label>
                              </div>
                            </div>
                            <div className="md:col-span-2">
                              <p className="text-sm text-gray-500 mb-1">Description</p>
                              <Textarea 
                                name="description"
                                value={tempExp.description}
                                onChange={handleExpChange}
                                rows={3}
                              />
                            </div>
                            <div className="md:col-span-2 flex gap-2 justify-end mt-4">
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600 border-red-600 hover:bg-red-50"
                                onClick={handleCancelEditExperience}
                              >
                                <X className="h-4 w-4 mr-1" />
                                Cancel
                              </Button>
                              <Button
                                size="sm"
                                className="bg-green-600 hover:bg-green-700"
                                onClick={() => handleUpdateExperience(exp.id)}
                              >
                                <Check className="h-4 w-4 mr-1" />
                                Save
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                                            <>
                          <div className="flex justify-between mb-2">
                            <h3 className="font-semibold text-lg">{exp.jobTitle}</h3>
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditExperience(exp.id)}
                                className="h-8 w-8 p-0 text-purple-600 border-purple-600 hover:bg-purple-50"
                              >
                                <Pencil className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                className="h-8 w-8 p-0 text-red-600 border-red-600 hover:bg-red-50"
                                onClick={() => handleRemoveExperience(exp.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <div className="flex items-center text-gray-700 mb-3">
                            <Building className="h-4 w-4 mr-2" />
                            <span>{exp.companyName}</span>
                          </div>
                          <div className="flex items-center text-gray-500 text-sm mb-4">
                            <Calendar className="h-4 w-4 mr-2" />
                            <span>
                              {exp.startMonth}/{exp.startYear} - {exp.currentlyWorking ? 'Present' : `${exp.endMonth}/${exp.endYear}`}
                            </span>
                          </div>
                          <div className="text-gray-700">
                            <p>{exp.description}</p>
                          </div>
                         
                        </>
                    </div>
                      )
                    
                  )}
                                  </div>
              </div>
            </Card>

               <Card className="overflow-hidden mb-6">
              <div className="p-6 ">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold">Certificates & Awards</h2>
                  {editSection === 'certificates' ? (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1 text-red-600 border-red-600 hover:bg-red-50"
                        onClick={handleCancel}
                      >
                        <X className="h-4 w-4" />
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                     //   onClick={() => handleSave('certificates')}
                      >
                        <Check className="h-4 w-4" />
                        Save
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        className="flex items-center gap-2 text-blue-600 border-blue-600 hover:bg-blue-50"
                        onClick={() => setShowAddForm(true)}
                      >
                        <Plus className="h-4 w-4" />
                        Add
                      </Button>
                    </div>
                  )}
                </div>
              </div>
                <div className="space-y-4 pl-6 pr-6 mb-8">
                    <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
                      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>{editingCertId ? 'Edit Certificate' : 'Add Certificate'}</DialogTitle>
                        </DialogHeader>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                          <div>
                            <p className="text-sm text-gray-500 mb-1">Certification Name</p>
                            <Input 
                              name="certificationName"
                              value={newCert.certificationName}
                              onChange={(e) => {
                                handleCertificateChange(e);
                                if (certErrors.certificationName) setCertErrors(prev => ({ ...prev, certificationName: "" }));
                              }}
                              className={certErrors.certificationName ? "border-red-500" : "edu-form-field"}
                            />
                            {certErrors.certificationName && <p className="text-sm text-red-500 mt-1">{certErrors.certificationName}</p>}
                          </div>
                          <div>
                            <p className="text-sm text-gray-500 mb-1">Certification Level</p>
                            <Select 
                              value={newCert.certificationLevel} 
                              onValueChange={(value) => {
                                setNewCert(prev => ({ ...prev, certificationLevel: value }));
                                if (certErrors.certificationLevel) setCertErrors(prev => ({ ...prev, certificationLevel: "" }));
                              }}
                            >
                              <SelectTrigger className={certErrors.certificationLevel ? "border-red-500" : "edu-form-field"}>
                                <SelectValue placeholder="Select level" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Beginner">Beginner</SelectItem>
                                <SelectItem value="Intermediate">Intermediate</SelectItem>
                                <SelectItem value="Advance">Advance</SelectItem>
                                <SelectItem value="Expert">Expert</SelectItem>
                              </SelectContent>
                            </Select>
                            {certErrors.certificationLevel && <p className="text-sm text-red-500 mt-1">{certErrors.certificationLevel}</p>}
                          </div>
                          <div>
                            <p className="text-sm text-gray-500 mb-1">Issuing Organization</p>
                            <Input 
                              name="issuingOrganization"
                              value={newCert.issuingOrganization}
                              onChange={(e) => {
                                handleCertificateChange(e);
                                if (certErrors.issuingOrganization) setCertErrors(prev => ({ ...prev, issuingOrganization: "" }));
                              }}
                              className={certErrors.issuingOrganization ? "border-red-500" : "edu-form-field"}
                            />
                            {certErrors.issuingOrganization && <p className="text-sm text-red-500 mt-1">{certErrors.issuingOrganization}</p>}
                          </div>
                          <div>
                            <p className="text-sm text-gray-500 mb-1">Issue Year</p>
                            <Input 
                              name="issueYear"
                              type="number"
                              value={newCert.issueYear}
                              onChange={(e) => {
                                handleCertificateChange(e);
                                if (certErrors.issueYear) setCertErrors(prev => ({ ...prev, issueYear: "" }));
                              }}
                              className={certErrors.issueYear ? "border-red-500" : "edu-form-field"}
                            />
                            {certErrors.issueYear && <p className="text-sm text-red-500 mt-1">{certErrors.issueYear}</p>}
                          </div>
                          <div>
                            <p className="text-sm text-gray-500 mb-1">Expiry Year</p>
                            <Input 
                              name="expiryYear"
                              type="number"
                              value={newCert.expiryYear}
                              onChange={(e) => {
                                handleCertificateChange(e);
                                if (certErrors.expiryYear) setCertErrors(prev => ({ ...prev, expiryYear: "" }));
                              }}
                              className={certErrors.expiryYear ? "border-red-500" : "edu-form-field"}
                            />
                            {certErrors.expiryYear && <p className="text-sm text-red-500 mt-1">{certErrors.expiryYear}</p>}
                          </div>
                          <div className="md:col-span-2 flex gap-2 justify-end mt-4">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 border-red-600 hover:bg-red-50"
                              onClick={handleCancelCertificate}
                            >
                              <X className="h-4 w-4 mr-1" />
                              Cancel
                            </Button>
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={handleSaveCertificate}
                            >
                            <Check className="h-4 w-4 mr-1" />
                            Save
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                    {certificates.map(cert => (
                      <div key={cert.certificationId || cert.id} className="relative border-l-2 border-purple-500 pl-4">
                        <div className="absolute top-0 right-0 flex gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditCertificate(cert.certificationId || cert.id)}
                            className="h-8 w-8 p-0 text-purple-600 border-purple-600 hover:bg-purple-50"
                          >
                            <Pencil className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-8 w-8 p-0 text-red-600 border-red-600 hover:bg-red-50"
                            onClick={() => handleRemoveCertificate(cert.certificationId || cert.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                        <h4 className="font-medium">{cert.certificationName}</h4>
                        <p className="text-sm text-gray-500">{cert.issuingOrganization} - {cert.certificationLevel}</p>
                        <p className="text-sm text-gray-500">{cert.issueYear} - {cert.expiryYear}</p>
                      </div>
                    ))}
                  
                  
                </div>
            </Card>
         
          </div>
        </div>
      </div>
    </div>
  );
}
