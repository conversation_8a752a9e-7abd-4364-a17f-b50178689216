import { Payment } from "@/types";
import paymentsData from "@/data/payments.json";


// Helper function to convert JSON date strings to Date objects
const convertDates = (Payments: any[]): Payment[] => {
  return Payments.map(Payment => ({
    ...Payment,
    createdAt: new Date(Payment.createdAt),
    fileUrl: null, // Set fileUrl to null by default for all announcements
  }));
};
 
// Get all Payments
export const getAllPaymentAnalytics = async (accessToken : string,startDate :string,endDate :string) => {
    const response = await fetch(   `/api/paymentManagement/v1/analytics?startDate=${startDate}&endDate=${endDate}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json"
      }
    });
    if (response.status === 401) {
      window.location.href = "/";
      return;
    }
    if (!response.ok) {
      throw new Error("Failed to fetch payment statistics");
    }
    return response.json();
  };
  export async function getPaymentHistory(accessToken: string, classroomId: string,
  startDate?: string,
  endDate?: string) {
  // Build query string if dates are provided
  let url = `/api/paymentManagement/v1/classrooms/${classroomId}/payments`;
  if (startDate && endDate) {
    url += `?startDate=${startDate}&endDate=${endDate}`;
  }
  const response = await fetch(url, {
    method: "GET",
 
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json"
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch payment history");
  }
  return response.json();
};

  export async function getPaymentStatistics(accessToken: string, classroomId: string,
  startDate?: string,
  endDate?: string) {
    // Build query string if dates are provided
    let url = `/api/paymentManagement/v1/classrooms/${classroomId}/payments/statistics`;
    if (startDate && endDate) {
      url += `?startDate=${startDate}&endDate=${endDate}`;
    }
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json"
      }
    });
    if (response.status === 401) {
      window.location.href = "/";
      return;
    }
    if (!response.ok) {
      throw new Error("Failed to fetch payment statistics");
    }
    return response.json();
}



export async function createClassPayment(accessToken: string, classroomId: string, paymentData: any) {
  const { classId,memo,userId,finalAmount,discountPercentage,...payload } = paymentData;
  const response = await fetch(`/api/paymentManagement/v1/classrooms/${classroomId}/payments`, {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json"
    },
    body: JSON.stringify(payload)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create payment");
  }
  return response.json();
}

export async function getStudentPayments(accessToken: string, classroomId: string,studentId : string) {
    // Build query string if dates are provided
    let url = `/api/paymentManagement/v1/classrooms/${classroomId}/students/payments`;
    
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json"
      }
    });
    if (response.status === 401) {
      window.location.href = "/";
      return;
    }
    if (!response.ok) {
      throw new Error("Failed to fetch payment statistics");
    }
    return response.json();
}