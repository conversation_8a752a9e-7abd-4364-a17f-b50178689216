import React, { useState, useEffect } from "react";
import { getCurrentTimeInTimezone } from "@/utils/timezone";

export const TimezoneDateTime: React.FC = () => {
  const [currentTime, setCurrentTime] = useState<string>("");

  useEffect(() => {
    const updateTime = () => {
      setCurrentTime(getCurrentTimeInTimezone());
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="text-sm text-gray-600">
      {currentTime}
    </div>
  );
};