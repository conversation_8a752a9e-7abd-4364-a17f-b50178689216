
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { LLMConfig } from '@/utils/llmService';
import { toast } from 'sonner';

interface LLMSettingsProps {
  onSave: (config: LLMConfig) => void;
  initialConfig?: Partial<LLMConfig>;
}

const LLMSettings: React.FC<LLMSettingsProps> = ({ onSave, initialConfig = {} }) => {
  const [config, setConfig] = useState<LLMConfig>({
    provider: initialConfig.provider || 'openai',
    apiKey: initialConfig.apiKey || '',
    model: initialConfig.model || '',
    temperature: initialConfig.temperature || 0.7,
    maxTokens: initialConfig.maxTokens || 1000,
  });

  const [showApiKey, setShowApiKey] = useState(false);

  const handleSave = () => {
    if (!config.apiKey) {
      toast.error("API Key is required");
      return;
    }
    
    onSave(config);
    toast.success("LLM settings saved successfully");
  };

  const getProviderModels = () => {
    switch (config.provider) {
      case 'openai':
        return [
          { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
          { value: 'gpt-4o', label: 'GPT-4o' },
          { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
        ];
      case 'perplexity':
        return [
          { value: 'llama-3.1-sonar-small-128k-online', label: 'Llama 3.1 Sonar Small' },
          { value: 'llama-3.1-sonar-large-128k-online', label: 'Llama 3.1 Sonar Large' },
          { value: 'llama-3.1-sonar-huge-128k-online', label: 'Llama 3.1 Sonar Huge' },
        ];
      default:
        return [{ value: 'custom', label: 'Custom Model' }];
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>LLM Configuration</CardTitle>
        <CardDescription>
          Configure your preferred LLM provider for generating questions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="provider">LLM Provider</Label>
          <Select 
            value={config.provider} 
            onValueChange={(value: any) => setConfig({ ...config, provider: value, model: '' })}
          >
            <SelectTrigger id="provider">
              <SelectValue placeholder="Select provider" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="perplexity">Perplexity</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <Label htmlFor="apiKey">API Key</Label>
            <div className="flex items-center space-x-2">
              <Label htmlFor="showKey" className="text-xs text-gray-500">Show Key</Label>
              <Switch 
                id="showKey" 
                checked={showApiKey} 
                onCheckedChange={setShowApiKey} 
              />
            </div>
          </div>
          <Input
            id="apiKey"
            className='edu-form-field'
              
            type={showApiKey ? "text" : "password"}
            value={config.apiKey}
            onChange={(e) => setConfig({ ...config, apiKey: e.target.value })}
            placeholder="Enter your API key"
          />
          <p className="text-xs text-gray-500">
            Your API key is stored locally and not sent to our servers.
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="model">Model</Label>
          <Select 
            value={config.model || ''} 
            onValueChange={(value) => setConfig({ ...config, model: value })}
          >
            <SelectTrigger id="model">
              <SelectValue placeholder="Select model" />
            </SelectTrigger>
            <SelectContent>
              {getProviderModels().map((model) => (
                <SelectItem key={model.value} value={model.value}>
                  {model.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <Label>Temperature ({config.temperature})</Label>
          </div>
          <Slider
            value={[config.temperature || 0.7]}
            min={0}
            max={1}
            step={0.1}
            onValueChange={(value) => setConfig({ ...config, temperature: value[0] })}
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>More Deterministic</span>
            <span>More Creative</span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <Label>Max Tokens ({config.maxTokens})</Label>
          </div>
          <Slider
            value={[config.maxTokens || 1000]}
            min={100}
            max={4000}
            step={100}
            onValueChange={(value) => setConfig({ ...config, maxTokens: value[0] })}
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleSave} className="w-full">Save Configuration</Button>
      </CardFooter>
    </Card>
  );
};

export default LLMSettings;
