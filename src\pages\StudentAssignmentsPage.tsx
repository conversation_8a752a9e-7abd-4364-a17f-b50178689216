
import React from "react";
import { useAuth } from "@/context/AuthContext";
import { useApp } from "@/context/AppContext";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import AssignmentList from "@/components/assignment/AssignmentList";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { BookCheck, Clock, ChevronLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

export default function StudentAssignmentsPage() {
  const { user } = useAuth();
  const { classes, assignments } = useApp();
  
  const studentClasses = classes.filter(c => 
    c.students.includes(user?.id || '')
  );

  const pendingAssignments = assignments
    .filter(assignment => 
      studentClasses.some(c => c.id === assignment.classId) &&
      !assignment.submittedBy.includes(user?.id || '')
    )
    .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50">
      <DashboardHeader variant="student" />
      
      <div className="p-4 sm:p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild className="gap-2">
              <Link to="/student-dashboard">
                <ChevronLeft className="h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>
            <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              My Assignments
            </h1>
          </div>
        </div>

        <Card className="overflow-hidden border-none shadow-xl bg-white/70 backdrop-blur-sm">
          <CardHeader className="bg-gradient-to-r from-purple-100 to-blue-100 border-b border-purple-200/30">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-purple-600 text-white">
                <BookCheck className="h-5 w-5" />
              </div>
              <div className="space-y-1">
                <CardTitle className="text-xl text-purple-900">Pending Assignments</CardTitle>
                <p className="text-sm text-purple-700 flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  Sorted by due date
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="p-6 bg-gradient-to-b from-white/50 to-transparent">
              <AssignmentList 
                assignments={pendingAssignments}
                classId=""
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
