import React, { useState } from "react";
import { AssignmentSubmission } from "@/types";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SubmissionGradingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  studentName: string;
  submission: AssignmentSubmission;
  onSave: (submission: AssignmentSubmission) => void;
}

export function SubmissionGradingDialog({
  open,
  onOpenChange,
  studentName,
  submission,
  onSave,
}: SubmissionGradingDialogProps) {
  const [status, setStatus] = useState<"pending" | "complete" | "graded">(
    submission.status
  );
  const [score, setScore] = useState(submission.score?.toString() || "");
  const [feedback, setFeedback] = useState(submission.feedback || "");

  const handleSave = () => {
    onSave({
      ...submission,
      status,
      score: score ? parseFloat(score) : undefined,
      feedback: feedback || undefined,
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Grade Submission - {studentName}</DialogTitle>
          <DialogDescription>
            Review and grade the student's assignment submission
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="status">Status</Label>
            <Select value={status} onValueChange={(value: any) => setStatus(value)}>
              <SelectTrigger id="status">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending Review</SelectItem>
                <SelectItem value="complete">Complete</SelectItem>
                <SelectItem value="graded">Graded</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="score">Score (optional)</Label>
            <Input
              id="score"
              type="number"
              className='edu-form-field'
              
              placeholder="e.g., 85"
              value={score}
              onChange={(e) => setScore(e.target.value)}
              min="0"
              max="100"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="feedback">Feedback (optional)</Label>
            <Textarea
              id="feedback"
              placeholder="Enter your feedback for the student..."
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              rows={4}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
