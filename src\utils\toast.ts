import { toast } from 'sonner';

/**
 * Utility functions for displaying toast notifications
 */
export const showToast = {
  /**
   * Display a default toast notification with red text
   */
  default: (message: string, options = {}) => {
    return toast(message, { 
      position: 'top-center', 
      style: { color: 'red' },
      ...options 
    });
  },
  
  /**
   * Display a success toast notification with red text
   */
  success: (message: string, options = {}) => {
    return toast.success(message, { 
      position: 'top-center', 
      style: { color: 'red' },
      ...options 
    });
  },
  
  /**
   * Display an error toast notification with red text
   */
  error: (message: string, options = {}) => {
    return toast.error(message, { 
      position: 'top-center', 
      style: { color: 'red' },
      ...options 
    });
  },
  
  /**
   * Display an info toast notification with red text
   */
  info: (message: string, options = {}) => {
    return toast.info(message, { 
      position: 'top-center', 
      style: { color: 'red' },
      ...options 
    });
  },
  
  /**
   * Display a warning toast notification with red text
   */
  warning: (message: string, options = {}) => {
    return toast.warning(message, { 
      position: 'top-center', 
      style: { color: 'red' },
      ...options 
    });
  },
  
  /**
   * Display a loading toast notification with red text
   */
  loading: (message: string, options = {}) => {
    return toast.loading(message, { 
      position: 'top-center', 
      style: { color: 'red' },
      ...options 
    });
  }
};