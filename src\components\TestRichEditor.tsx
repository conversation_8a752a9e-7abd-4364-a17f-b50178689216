import React, { useState } from 'react';
import RichTextEditor from './RichTextEditor';

const TestRichEditor = () => {
  const [content, setContent] = useState('<p>This is <strong>bold</strong> and <em>italic</em> text</p>');

  return (
    <div className="p-4">
      <h2>Test Rich Text Editor</h2>
      <div className="mb-4">
        <label>Current content:</label>
        <pre className="bg-gray-100 p-2 text-sm">{content}</pre>
      </div>
      <RichTextEditor
        value={content}
        onChange={setContent}
        placeholder="Test editor"
      />
    </div>
  );
};

export default TestRichEditor;