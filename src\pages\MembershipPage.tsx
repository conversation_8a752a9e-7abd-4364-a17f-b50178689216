
import { useState, useEffect } from "react";
import { Head<PERSON> } from "@/components/layout/Header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CreditCard, Check, Info, Pause, XCircle, Calendar } from "lucide-react";
import { toast } from "sonner";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import {getFeatures, createMembershipRequest, cancelMembership, myMembership} from '@/services/membershipService';
import { useAuth } from "react-oidc-context";

interface Feature {
  id: string;
  name: string;
  description: string;
  monthlyPrice: number;
  yearlyPrice: number;
  isFree: boolean;
  isDefault: boolean;
}

const YEARLY_DISCOUNT = 0.17; // 17% discount for yearly

export default function MembershipPage() {
  const auth = useAuth();
  const [features, setFeatures] = useState<Feature[]>([]);
  const [isYearly, setIsYearly] = useState(false);
  const [selectedFeatures, setSelectedFeatures] = useState<Set<string>>();
  const [membership, setMembership] = useState(null);

  useEffect(() => {
    const loadMembership = async () => {
      try {
        const membershipData = await myMembership(auth.user?.access_token);
        setMembership(membershipData);
        console.log(membershipData)
      } catch (error) {
        console.error('Failed to load membership:', error);
      }
    };
    
    if (auth.user?.access_token) {
      loadMembership();
    }
  }, [auth.user?.access_token]);
     useEffect(() => {
    const loadFeatures = async () => {
      try {
        const response = await getFeatures();
        if (response?.features && Array.isArray(response.features)) {
          const mappedFeatures = response.features.map(f => ({
            id: f.id,
            name: f.name,
            description: f.description,
            monthlyPrice: f.monthlyPrice,
            yearlyPrice: f.yearlyPrice,
            isFree: f.free,
            isDefault: f.default
          }));
          setFeatures(mappedFeatures);
          setSelectedFeatures(new Set(mappedFeatures.filter(f => f.isDefault).map(f => f.id)));
        }
      } catch (error) {
        console.error('Failed to load features:', error);
      }
    };
    
    if (auth.user?.access_token) {
      loadFeatures();
    }
  }, [auth.user?.access_token]);
  const [pauseDuration, setPauseDuration] = useState("1");
  const [hasActiveMembership] = useState(true); // Mock active membership

  const toggleFeature = (featureId: string) => {
    if (!Array.isArray(features)) return;
    const feature = features.find(f => f.id === featureId);
    if (feature?.isFree) return;

    const newSelected = new Set(selectedFeatures);
    if (newSelected.has(featureId)) {
      newSelected.delete(featureId);
    } else {
      newSelected.add(featureId);
    }
    setSelectedFeatures(newSelected);
  };

  const calculateTotal = () => {
    if (!selectedFeatures || !Array.isArray(features) || !features.length) {
      return { subtotal: 0, discount: 0, total: 0 };
    }
    
    const selectedFeaturesArray = features.filter(f => selectedFeatures.has(f.id));
    const subtotal = selectedFeaturesArray.reduce((sum, feature) => {
      return sum + (isYearly ? feature.yearlyPrice : feature.monthlyPrice);
    }, 0);

    return {
      subtotal,
      discount: 0,
      total: subtotal,
    };
  };

  const { subtotal, discount, total } = calculateTotal();

  const handleSubscribe = async () => {
    const featureIds = Array.from(selectedFeatures || []);
    const billingCycle = isYearly ? "YEARLY" : "MONTHLY";    
    try {
      await createMembershipRequest(auth.user?.access_token, featureIds,billingCycle);
      const membershipData = await myMembership(auth.user?.access_token);
      setMembership(membershipData);
      toast.success("Subscription Updated", {
        description: `Updated subscription with ${featureIds.length} features (${billingCycle})`,
      });
    } catch (error) {
      toast.error("Subscription Failed", {
        description: "Failed to update subscription. Please try again.",
      });
    }
  };

  const handlePauseMembership = () => {
    toast.success("Membership Paused", {
      description: `Your membership has been paused for ${pauseDuration} month(s)`,
    });
  };

  const handleCancelMembership = async () => {
    try {
      await cancelMembership(auth.user?.access_token);
      const membershipData = await myMembership(auth.user?.access_token);
      setMembership(membershipData);
      
      toast.success("Membership Cancelled", {
        description: "Your membership will remain active until the end of the billing period",
      });
    } catch (error) {
      toast.error("Cancellation Failed", {
        description: "Failed to cancel membership. Please try again.",
      });
    }
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
    
        
        
        <main className="container mx-auto px-4 py-4 max-w-7xl">
          <div className="mb-4">
             <div className="flex items-center gap-3"><Link to="/dashboard" className="flex items-center"><ArrowLeft className="h-5 w-5 text-gray-700" /></Link><h1 className="text-lg font-medium">Membership Management</h1></div>
            <p className="text-sm text-muted-foreground">
              Manage your subscription and select features
            </p>
          </div>

          <Tabs defaultValue="features" className="w-full">
            <TabsList  className="h-12 bg-transparent border-0 p-0">
              <TabsTrigger value="features" className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-purple-600 data-[state=active]:bg-transparent px-4">
                Select Features
              </TabsTrigger>
              {hasActiveMembership && (
                <TabsTrigger value="manage" className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-purple-600 data-[state=active]:bg-transparent px-4">
                  Manage Subscription
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="features" className="mt-0 border-0 p-0">
              {/* Billing Period Toggle */}
              <Card className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex items-center justify-center gap-3">
                    <Label 
                      htmlFor="billing-toggle" 
                      className={`text-sm font-medium ${!isYearly ? 'text-purple-700' : 'text-muted-foreground'}`}
                    >
                      Monthly
                    </Label>
                    <Switch
                      id="billing-toggle"
                      checked={isYearly}
                      onCheckedChange={setIsYearly}
                      className="data-[state=checked]:bg-purple-600"
                    />
                    <Label 
                      htmlFor="billing-toggle" 
                      className={`text-sm font-medium flex items-center gap-2 ${isYearly ? 'text-purple-700' : 'text-muted-foreground'}`}
                    >
                      Yearly
                      <Badge className="bg-green-100 text-green-800 text-xs">Save 17%</Badge>
                    </Label>
                  </div>
                </CardContent>
              </Card>

              <div className="grid lg:grid-cols-4 gap-4">
                {/* Features List - Compact */}
                <div className="lg:col-span-3 grid md:grid-cols-2 gap-2">
                  {Array.isArray(features) && features.map((feature) => {
                    const isSelected = selectedFeatures?.has(feature.id) || false;
                    
                    return (
                      <Card
                        key={feature.id}
                        className={`cursor-pointer transition-all p-3 ${
                          isSelected
                            ? "border-purple-600 bg-purple-50"
                            : "hover:border-purple-300"
                        } ${feature.isFree ? "bg-gray-50" : ""}`}
                        onClick={() => !feature.isFree && toggleFeature(feature.id)}
                      >
                        <div className="flex items-start gap-2">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => toggleFeature(feature.id)}
                            disabled={feature.isFree}
                            className={`mt-0.5 ${isSelected ? 'border-purple-600 bg-purple-600' : ''}`}
                            onClick={(e) => e.stopPropagation()}
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between gap-2">
                              <h3 className="text-sm font-semibold text-purple-900 truncate">
                                {feature.name}
                              </h3>
                              {feature.isFree ? (
                                <Badge className="text-xs bg-green-100 text-green-800 shrink-0">Free</Badge>
                              ) : (
                                <span className="text-sm font-bold text-purple-700 shrink-0">
                                  ${isYearly ? feature.yearlyPrice : feature.monthlyPrice}
                                </span>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground mt-0.5">
                              {feature.description}
                            </p>
                          </div>
                        </div>
                      </Card>
                    );
                  })}
                </div>

                {/* Pricing Summary - Compact */}
                <div className="lg:col-span-1">
                  <Card className="border-purple-200 bg-purple-50 sticky top-4">
                    <CardHeader className="pb-2 pt-3 px-3">
                      <CardTitle className="text-base flex items-center gap-2">
                        <CreditCard className="h-4 w-4 text-purple-700" />
                        Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3 px-3 pb-3">
                      {/* Selected Count */}
                      <div className="text-xs text-muted-foreground">
                        {features.filter(f => selectedFeatures.has(f.id)).length} features selected
                      </div>

                      <Separator />

                      {/* Pricing */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Subtotal:</span>
                          <span className="font-semibold">${calculateTotal().subtotal}</span>
                        </div>
                        <div className="flex justify-between text-base font-bold text-purple-700">
                          <span>Total:</span>
                          <span>
                            ${calculateTotal().total}
                            <span className="text-xs font-normal text-muted-foreground">
                              /{isYearly ? "yr" : "mo"}
                            </span>
                          </span>
                        </div>
                      </div>

                      {isYearly && (
                        <div className="bg-green-50 border border-green-200 rounded p-2 text-xs text-green-800">
                          Saving 17% with yearly billing
                        </div>
                      )}

                      <Button
                        className="w-full bg-purple-600 hover:bg-purple-700 text-sm py-2"
                        onClick={handleSubscribe}
                      >
                        <CreditCard className="mr-2 h-4 w-4" />
                        Subscribe
                      </Button>

                      <p className="text-xs text-center text-muted-foreground">
                        Cancel anytime
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            {/* Manage Subscription Tab */}
            <TabsContent value="manage" className="mt-0 border-0 p-0">
              <Card className="overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Current Subscription</CardTitle>
                  <CardDescription className="text-sm">
                    Manage your active membership
                  </CardDescription>
                </CardHeader>
 <CardContent className="p-6">                  {/* Current Plan Info */}
                  <div className="bg-purple-50 rounded p-3 space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Plan:</span>
                      <Badge className="bg-purple-600">Pro Plan</Badge>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span>Billing:</span>
                      <span className="font-medium">{membership?.billingCycle || "N/A"}</span>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span>Next billing:</span>
                      <span className="font-medium">Dec 15, 2025</span>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span>Amount:</span>
                      <span className="font-bold text-purple-700">${membership?.totalAmount || 0}</span>
                    </div>
                  </div>

                  <Separator />

                  {/* Pause Membership */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Pause Membership</Label>
                    <p className="text-xs text-muted-foreground mb-2">
                      Temporarily pause your subscription
                    </p>
                    <div className="flex gap-2">
                      <Select value={pauseDuration} onValueChange={setPauseDuration}>
                        <SelectTrigger className="edu-form-field flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 month</SelectItem>
                          <SelectItem value="2">2 months</SelectItem>
                          <SelectItem value="3">3 months</SelectItem>
                          <SelectItem value="6">6 months</SelectItem>
                        </SelectContent>
                      </Select>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" className="border-purple-300 text-purple-700">
                            <Pause className="h-4 w-4 mr-1" />
                            Pause
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Pause Membership?</AlertDialogTitle>
                            <AlertDialogDescription>
                              Your membership will be paused for {pauseDuration} month(s). You won't be charged during this period.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handlePauseMembership} className="bg-purple-600">
                              Confirm Pause
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>

                  <Separator />

                  {/* Cancel Membership */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-red-700">Cancel Membership</Label>
                    <p className="text-xs text-muted-foreground mb-2">
                      Permanently cancel your subscription
                    </p>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" className="w-full">
                          <XCircle className="h-4 w-4 mr-2" />
                          Cancel Membership
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Cancel Membership?</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to cancel? Your membership will remain active until the end of your current billing period.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Keep Membership</AlertDialogCancel>
                          <AlertDialogAction onClick={handleCancelMembership} className="bg-red-600">
                            Yes, Cancel
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </TooltipProvider>
  );
}
