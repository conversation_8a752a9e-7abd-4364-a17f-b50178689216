
import experienceData from '../data/experience.json';

interface WorkExperience {
  id: string;
  position: string;
  institution: string;
  location: string;
  startDate: string;
  endDate: string | null;
  current: boolean;
  description: string;
  achievements: string[];
}

interface Publication {
  id: string;
  title: string;
  journal: string;
  year: number;
  citation: string;
}

interface Presentation {
  id: string;
  title: string;
  event: string;
  location: string;
  date: string;
}

interface Award {
  id: string;
  title: string;
  organization: string;
  year: number;
}

interface ExperienceData {
  workExperience: WorkExperience[];
  publications: Publication[];
  presentations: Presentation[];
  awards: Award[];
}

export const getExperienceData = (): ExperienceData => {
  return experienceData;
};
