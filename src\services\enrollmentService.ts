export async function assignStudentToClassroom(accessToken, classroomId, studentId) {
  const response = await fetch(`/api/enrollmentManagement/v1/teacher/enroll/classroom/${classroomId}/student/${studentId}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to assign student to class");
  }
  return response.json();
}
export async function joinStudentToClassroom(accessToken, joinCode) {
  const response = await fetch(`/api/enrollmentManagement/v1/join/${joinCode}?role=STUDENT`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to assign student to class");
  }
  return response.json();
}
export async function getMyEnrollments(accessToken: string)
{
   const response = await fetch("/api/enrollmentManagement/v1/my-enrollments", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok)
     throw new Error("Failed to fetch announcements");
  return response.json();
}
