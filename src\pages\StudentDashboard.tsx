import React, { useState, useEffect, useRef } from "react";
import { useApp } from "@/context/AppContext";
import { useAuth } from "react-oidc-context"; // Updated import
import { Link } from "react-router-dom";
import { 
  BookOpen, 
  GraduationCap, 
  Calendar, 
  MessageSquare, 
  FileText,
  ClipboardCheck,
  BookCheck,
  Star,
  Award,
  X
} from "lucide-react";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { PerformanceCard } from "@/components/dashboard/PerformanceCard";
import { AnnouncementsSection } from "@/components/dashboard/AnnouncementsSection";
import { ScheduleSection } from "@/components/dashboard/ScheduleSection";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import AssignmentList from "@/components/assignment/AssignmentList";
import { NextClassAlert } from "@/components/dashboard/NextClassAlert";
import { getStudentNextClassAlert } from "@/services/scheduleService";
import { useNavigate } from "react-router-dom";
import { getClassesForStudent, ClassData } from "@/services/classService";
import { ScheduleEvent } from "@/types";
import { useUserRole } from "@/hooks/useUserRole";
import {getProfile} from "@/services/profileService";
import { UserRole } from "@/types";
import { generateAvatarUrl } from "@/lib/utils";
export default function StudentDashboard() {
  
   const auth = useAuth();
  const { updateUserTimezone } = useUserRole();
  const { classes, assignments, schedules } = useApp();
  const isMobile = useIsMobile();
  const navigate = useNavigate();
        const [availableClasses, setAvailableClasses] = useState<ClassData[]>([]);
  const [nextClasss, setNextClasss] = useState<ScheduleEvent | undefined>(undefined);
        const [showWelcomeCard, setShowWelcomeCard] = useState(true);
  const [profileData, setProfileData] = useState({
      name: '',
      phoneno: ''
    });
    const fetchProfile = async () => {
        try {
          const fetchedProfile = await getProfile(auth.user?.access_token);
          console.log('Fetched profile:', fetchedProfile); // Debug log
          if (fetchedProfile) {
            setProfileData({
              name: fetchedProfile.name || '',
              phoneno: fetchedProfile.phoneNumber  || ''
            });
            
            // Set timezone cookie if available
            if (fetchedProfile.timezone) {
              updateUserTimezone(fetchedProfile.timezone);
            }
          }
        } catch (error) {
          console.error('Error fetching profile:', error);
        }
      };
      useEffect(() => {
        fetchProfile();
      }, []);
    // Get user from OIDC
    const user = auth.isAuthenticated ? {
      id: auth.user?.profile.sub || "",
      name: profileData.name  || "User",
      email: auth.user?.profile.email || "",
      role: (auth.user?.profile["custom:role"] as UserRole) ,
      avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
    } : null;
  
    const currentDate = new Date();
  const formattedDate = new Intl.DateTimeFormat('en-US', { 
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(currentDate);
  const hasFetched = useRef(false);

  useEffect(() => {
    if (!auth.isAuthenticated || !auth.user?.access_token || hasFetched.current) return;

    const fetchData = async () => {
      hasFetched.current = true;
      try {
        const [classesResponse, nextClassResponse] = await Promise.all([
          getClassesForStudent(auth.user.access_token),
          getStudentNextClassAlert(auth.user.access_token)
        ]);
        
        setAvailableClasses(classesResponse.content || []);
        setNextClasss(nextClassResponse);
      } catch (error) {
        console.error("Error fetching data:", error);
        setAvailableClasses([]);
        setNextClasss(undefined);
      }
    };

    fetchData();
  }, [auth.isAuthenticated, auth.user?.access_token]);

  const studentClasses = classes.filter(c => 
    c.students.includes( '')
  );
  
  

  const pendingAssignments = assignments
    .filter(assignment => 
      studentClasses.some(c => c.id === assignment.classroomId) &&
      !assignment.submittedBy.includes(user?.id || '')
    )
    .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
    .slice(0, 3);

  const navigationItems = [
    {
      name: "My Classes",
      path: "/student-classes",
      icon: BookOpen,
      color: "bg-blue-100 text-blue-600"
    },
    {
      name: "Schedule",
      path: "/student-schedule/all",
      icon: Calendar,
      color: "bg-purple-100 text-purple-600"
    },
    {
      name: "Chat",
      path: "/chat",
      icon: MessageSquare,
      color: "bg-amber-100 text-amber-600"
    },
    {
      name: "Attendance",
      path: "/student-attendance",
      icon: ClipboardCheck,
      color: "bg-pink-100 text-pink-600"
    }
  ];

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 17) return "Good afternoon";
    return "Good evening";
  };

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="flex-grow">
        <DashboardHeader variant="student" />

        <div className="container mx-auto px-4 py-4 sm:py-6">
          {/* Welcome Card */}
          {showWelcomeCard && (
            <div className="mb-6 bg-white rounded-2xl p-4 sm:p-6 shadow-lg border border-slate-200 hover:shadow-xl transition-shadow duration-300 relative">
              <button
                onClick={() => setShowWelcomeCard(false)}
                className="absolute top-4 right-4 p-1 rounded-full hover:bg-slate-100 transition-colors"
              >
                <X className="h-5 w-5 text-slate-400 hover:text-slate-600" />
              </button>
              <h1 className="text-xl sm:text-2xl font-bold text-slate-800 mb-2 flex items-center gap-2 pr-8">
                {getGreeting()}, {user?.name?.split(' ')[0]}! 
                <GraduationCap className="text-purple-600 h-6 w-6 sm:h-8 sm:w-8" />
              </h1>
              <p className="text-slate-600 text-sm sm:text-base">
                Your learning journey continues today. Let's make it extraordinary!
              </p>
            </div>
          )}

          {/* Next Class Alert */}
          <div className="mb-4 sm:mb-6">
             <NextClassAlert nextClass={nextClasss} />
          </div>

          {/* Navigation Grid */}
          <div className="grid grid-cols-2 gap-2 sm:grid-cols-4 sm:gap-4 mb-4 sm:mb-6">
            {navigationItems.map((item) => (
              <Link 
                to={item.path} 
                key={item.name} >
                <div className={`flex items-center gap-3 p-4 rounded-xl ${item.color} hover:opacity-90 transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md h-full`}>
                  <div className="rounded-full bg-white/90 p-2 flex-shrink-0">
                    <item.icon className="h-5 w-5" />
                  </div>
                  <span className="font-medium">{item.name}</span>
                </div>
              </Link>
            ))}
          </div>

         {/*          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 mb-4 sm:mb-6">
           
            <div className="group">
              <Card className="overflow-hidden border-slate-200 shadow-sm group-hover:shadow-xl transition-all duration-300">
                <CardContent className="p-4 sm:p-6 bg-gradient-to-br from-purple-50 to-indigo-100">
                  <div className="flex items-center justify-between mb-4 sm:mb-6">
                    <h2 className="text-lg sm:text-xl font-bold text-slate-800 flex items-center gap-2">
                      <Star className="h-5 w-5 sm:h-6 sm:w-6 text-amber-400" />
                      Lecture Performance
                    </h2>
                  </div>

                  <div className="flex items-center gap-4 sm:gap-6 mb-4 sm:mb-6">
                    <div className="h-16 w-16 sm:h-20 sm:w-20 rounded-full bg-slate-100 p-4">
                      <Award className="h-full w-full text-amber-400" />
                    </div>
                    <div>
                      <div className="text-3xl sm:text-4xl font-bold text-slate-800 mb-1">91.2%</div>
                      <div className="text-sm sm:text-base text-slate-600">Overall Performance</div>
                    </div>
                  </div>

                  <div className="bg-slate-50 rounded-xl p-3 sm:p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xl sm:text-2xl font-bold text-slate-800">86%</span>
                      <div className="flex gap-2">
                        <div className="p-1.5 bg-slate-100 rounded-full">
                          <Award className="h-4 w-4 sm:h-5 sm:w-5 text-amber-400" />
                        </div>
                      </div>
                    </div>
                    <p className="text-xs sm:text-sm text-slate-600">Your Lesson Planning Score increased by 25% from last month. Keep up the great work! 🌟</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="group">
              <Card className="overflow-hidden border-slate-200 shadow-sm group-hover:shadow-xl transition-all duration-300">
                <CardContent className="p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-cyan-100">
                  <div className="flex items-center justify-between mb-4 sm:mb-6">
                    <h2 className="text-lg sm:text-xl font-bold text-slate-800 flex items-center gap-2">
                      <BookCheck className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500" />
                      Pending Assignments
                    </h2>
                    <Button variant="outline" size="sm" asChild className="border-slate-200 hover:bg-slate-50">
                      <Link to="/student-assignments">View All</Link>
                    </Button>
                  </div>

                  <div className="flex items-center gap-4 sm:gap-6 mb-4 sm:mb-6">
                    <div className="h-16 w-16 sm:h-20 sm:w-20 rounded-full bg-slate-100 p-4">
                      <BookOpen className="h-full w-full text-blue-500" />
                    </div>
                    <div>
                      <div className="text-3xl sm:text-4xl font-bold text-slate-800 mb-1">{pendingAssignments.length}</div>
                      <div className="text-sm sm:text-base text-slate-600">Assignments to Complete</div>
                    </div>
                  </div>

                  <div className="space-y-2 sm:space-y-3">
                    {pendingAssignments.length > 0 ? (
                      pendingAssignments.map((assignment) => (
                        <div key={assignment.id} className="flex items-center justify-between bg-slate-50 p-2 sm:p-3 rounded-xl hover:bg-slate-100 transition-colors">
                          <div className="flex items-center gap-2 sm:gap-3 flex-1 min-w-0">
                            <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-slate-400 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm sm:text-base text-slate-700 truncate">{assignment.title}</p>
                              <p className="text-xs sm:text-sm text-slate-500">
                                Due: {new Date(assignment.dueDate).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            asChild
                            className="border-slate-200 hover:bg-slate-100 ml-2 flex-shrink-0 text-xs sm:text-sm px-2 sm:px-3"
                          >
                            <Link to={`/class/${assignment.classId}/submit-assignments/${assignment.id}`}>
                              View
                            </Link>
                          </Button>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 sm:py-6">
                        <p className="text-slate-500 text-sm sm:text-base">You have no pending assignments</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
 */}
          {/* Schedule and Announcements Grid */}
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-3 sm:gap-4">
            <div className="lg:col-span-2 order-2 lg:order-1 bg-white rounded-xl shadow-sm p-4 sm:p-4 border border-slate-200 hover:shadow-md transition-shadow">
              <ScheduleSection />
            </div>
            <div className="order-1 lg:order-2 bg-white rounded-xl shadow-sm p-4 sm:p-4 border border-slate-200 hover:shadow-md transition-shadow">
              <StudentAnnouncementsSection />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

const StudentAnnouncementsSection: React.FC = () => {
  return (
    <div>
      <div className="flex justify-between items-center mb-2">
        <h2 className="text-lg sm:text-xl font-semibold">Announcement</h2>
      </div>
      <AnnouncementsSection hideCreateButton={true} />
    </div>
  );
};
