
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { Calendar as CalendarIcon, Video } from "lucide-react";
import { cn } from "@/lib/utils";

interface StepTwoFormProps {
  formData: {
    startDate: Date;
    endDate: Date;
    daysOfWeek: string[];
    startTime: string;
    endTime: string;
    recurring: boolean;
    onlineMeeting?: {
      platform: string;
      link: string;
    };
  };
  updateFormData: (data: Partial<StepTwoFormProps["formData"]>) => void;
}

export default function StepTwoForm({ formData, updateFormData }: StepTwoFormProps) {
  const daysOfWeek = [
    { value: "monday", label: "Monday" },
    { value: "tuesday", label: "Tuesday" },
    { value: "wednesday", label: "Wednesday" },
    { value: "thursday", label: "Thursday" },
    { value: "friday", label: "Friday" },
    { value: "saturday", label: "Saturday" },
    { value: "sunday", label: "Sunday" },
  ];

  const handleDayToggle = (day: string) => {
    if (formData.daysOfWeek.includes(day)) {
      updateFormData({
        daysOfWeek: formData.daysOfWeek.filter(d => d !== day)
      });
    } else {
      updateFormData({
        daysOfWeek: [...formData.daysOfWeek, day]
      });
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-medium mb-4">Schedule Setup</h2>
      <p className="text-gray-500 mb-6">
        Set the schedule for your class sessions
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label>Start Date*</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.startDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.startDate ? format(formData.startDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.startDate}
                onSelect={(date) => updateFormData({ startDate: date || new Date() })}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <Label>End Date*</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.endDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.endDate ? format(formData.endDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.endDate}
                onSelect={(date) => updateFormData({ endDate: date || new Date() })}
                initialFocus
                disabled={(date) => date < formData.startDate}
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <Label htmlFor="startTime">Start Time*</Label>
          <Input
            id="startTime"
            type="time"
            value={formData.startTime}
            onChange={(e) => updateFormData({ startTime: e.target.value })}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="endTime">End Time*</Label>
          <Input
            id="endTime"
            type="time"
            value={formData.endTime}
            onChange={(e) => updateFormData({ endTime: e.target.value })}
            required
          />
        </div>
        
        {/* Online Meeting Section */}
        <div className="col-span-full">
          <div className="p-4 border rounded-md space-y-4 mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Video className="h-5 w-5 text-blue-500" />
              <h3 className="font-medium">Online Meeting Details</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="meetingPlatform">Platform</Label>
                <Select 
                  value={formData.onlineMeeting?.platform || ""}
                  onValueChange={(value) => updateFormData({
                    onlineMeeting: {
                      platform: value,
                      link: formData.onlineMeeting?.link || ""
                    }
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="zoom">Zoom</SelectItem>
                    <SelectItem value="teams">Microsoft Teams</SelectItem>
                    <SelectItem value="google">Google Meet</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="meetingLink">Meeting Link/ID</Label>
                <Input 
                  id="meetingLink"
                  placeholder="Enter meeting link or ID"
                  value={formData.onlineMeeting?.link || ""}
                  onChange={(e) => updateFormData({
                    onlineMeeting: {
                      platform: formData.onlineMeeting?.platform || "",
                      link: e.target.value
                    }
                  })}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4 col-span-full">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="recurring"
              checked={formData.recurring}
              onCheckedChange={(checked) => {
                updateFormData({ recurring: checked === true });
              }}
            />
            <Label htmlFor="recurring">This is a recurring class</Label>
          </div>
          
          {formData.recurring && (
            <div className="pt-4">
              <Label className="mb-3 block">Select Days</Label>
              <div className="flex flex-wrap gap-3">
                {daysOfWeek.map((day) => (
                  <Button
                    key={day.value}
                    type="button"
                    variant={formData.daysOfWeek.includes(day.value) ? "default" : "outline"}
                    className={cn(
                      "bg-white border",
                      formData.daysOfWeek.includes(day.value) && "bg-purple-600 text-white hover:bg-purple-700"
                    )}
                    onClick={() => handleDayToggle(day.value)}
                  >
                    {day.label.substring(0, 3)}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
