import React, { useRef, useEffect } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  readOnly?: boolean;
}

const modules = {
  toolbar: [
    [{ 'header': [1, 2, false] }],
    ['bold', 'italic', 'underline'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    ['clean']
  ],
};

const formats = [
  'header', 'bold', 'italic', 'underline',
  'list', 'bullet'
];

export function RichTextEditor({ value, onChange, placeholder, className, readOnly }: RichTextEditorProps) {
  const quillRef = useRef<ReactQuill>(null);

  useEffect(() => {
    // Suppress findDOMNode warning in development
    const originalError = console.error;
    console.error = (...args) => {
      if (args[0]?.includes?.('findDOMNode is deprecated')) {
        return;
      }
      originalError(...args);
    };

    return () => {
      console.error = originalError;
    };
  }, []);

  return (
    <>
      <style dangerouslySetInnerHTML={{
        __html: `
          .ql-toolbar {
            border: none !important;
            border-bottom: 1px solid #e5e7eb !important;
          }
          .ql-container {
            border: none !important;
          }
          .ql-editor {
            min-height: 150px;
          }
        `
      }} />
      <div className={`rounded-lg border border-gray-300 overflow-hidden focus-within:border-purple-400 focus-within:ring focus-within:ring-purple-100 focus-within:ring-opacity-50 ${className}`}>
        <ReactQuill
          ref={quillRef}
          theme="snow"
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          modules={modules}
          formats={formats}
          readOnly={readOnly}
          style={{ minHeight: '200px' }}
        />
      </div>
    </>
  );
}