import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'sonner';
import { CookiesProvider } from 'react-cookie';
import { useAuth as useOidcAuth } from 'react-oidc-context';
import { AuthProvider } from './context/AuthContext';
import { AppProvider } from './context/AppContext';
import { UserRole } from './types';
import Index from './pages/Index';
import AboutPage from './pages/AboutPage';
//import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import RoleSelectionPage from './pages/RoleSelectionPage';
import ClassesPage from './pages/ClassesPage';
import CreateClassPage from './pages/CreateClassPage';
import ClassDetailPage from './pages/ClassDetailPage';
import JoinClassPage from './pages/JoinClassPage';
import TeacherDashboard from './pages/TeacherDashboard';
import StudentDashboard from './pages/StudentDashboard';
import ParentDashboard from './pages/ParentDashboard';
import ClassroomPage from './pages/ClassroomPage';
import ProfilePage from './pages/ProfilePage';
import BlogPage from './pages/BlogPage';
import BlogDetailPage from './pages/BlogDetailPage';
import PaymentsPage from './pages/PaymentsPage';
import StudentPaymentsPage from './pages/StudentPaymentsPage';
import StudentClassesPage from './pages/StudentClassesPage';
import ReviewsPage from './pages/ReviewsPage';
import ExpensesPage from './pages/ExpensesPage';
import AnnouncementsPage from './pages/AnnouncementsPage';
import StudentsPage from './pages/StudentsPage';
import ChatPage from './pages/ChatPage';
import ExperiencePage from './pages/ExperiencePage';
import AddExperiencePage from './pages/AddExperiencePage';
import AttendancePage from './pages/AttendancePage';
import AttendanceViewPage from './pages/AttendanceViewPage';
import ScheduleViewPage from './pages/ScheduleViewPage';
import SubscriptionPage from './pages/SubscriptionPage';
import NotFound from './pages/NotFound';
import AssignmentDetailPage from './pages/AssignmentDetailPage';
import QuestionGeneratorPage from './pages/QuestionGeneratorPage';
import CreateAssignmentPage from './pages/CreateAssignmentPage';
import MeetingSchedulerPage from './pages/MeetingSchedulerPage';
import ParentMeetingsPage from './pages/ParentMeetingsPage';
import ScheduleAllPage from "@/pages/ScheduleAllPage";
import StudentScheduleAllPage from './pages/StudentScheduleAllPage';
import AddStudentPage from './pages/AddStudentPage';
import FeaturesPage from './pages/FeaturesPage';
import StudentClassManageDetailPage from './pages/StudentClassManageDetailPage';
import StudentAttendancePage from './pages/StudentAttendancePage';
import StudentAssignmentsPage from './pages/StudentAssignmentsPage';
import SubmitAssignment  from './pages/SubmitAssignment.tsx'; 
import StudentProgressPage from './pages/StudentProgressPage';
import OAuthCallback from './components/auth/OAuthCallback';
import AddStudentToClass from './pages/AddStudentToClass';
import RecordAttendance from './pages/RecordAttendance';
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import ClassSchedule from './components/class/ClassSchedule';
import { useUserRole } from "@/hooks/useUserRole";
import { UserDataProvider } from '@/components/UserDataProvider';

import { setUserData, setSelectedRole } from '@/redux/userSlice';
import { PublicPage } from './pages/PublicPage';
import SettingsPage from './pages/SettingsPage';
import TermsOfServicePage from './pages/TermsOfServicePage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import CookiePolicyPage from './pages/CookiePolicyPage';
import SecurityPage from './pages/SecurityPage';
import HelpCenterPage from './pages/HelpCenterPage';  
import FAQPage from './pages/FAQPage';
import Footer from './components/Footer.tsx';
import ContactUs from './pages/ContactUs';
import MembershipPage from './pages/MembershipPage';

// Define the RoleBasedRedirect component using OIDC context
const RoleBasedRedirect = () => {
  const auth = useOidcAuth();
  
  if (!auth.isAuthenticated) {
    return <Navigate to="/login" />;
  }
  // Determine role from OIDC claims
 const { selectedRole } = useUserRole();  
  switch (selectedRole) {
    case UserRole.TEACHER:
      return <Navigate to="/teacher-dashboard" />;
    case UserRole.STUDENT:
      return <Navigate to="/student-dashboard" />;
    case UserRole.PARENT:
      return <Navigate to="/parent-dashboard" />;
    default:
      return <Navigate to="/role-selection" />;
  }
};

function App() {
  const auth = useOidcAuth();
  const dispatch = useDispatch();
  

  
  if (auth.isLoading) {
    return <div className="min-h-screen w-full flex items-center justify-center">Loading...</div>;
  }
  
  // Handle OIDC error state
  if (auth.error) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600">Authentication Error</h2>
          <p className="mt-2">{auth.error.message}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded "
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  return (
    <CookiesProvider>
      <BrowserRouter>
        <AuthProvider>
          <AppProvider>
              <UserDataProvider>
            <div className="min-h-screen w-full">
              <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/features" element={<FeaturesPage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/login" element={<Index />} />
              <Route path="/login/oauth2/code/cognito" element={<OAuthCallback />} />
              <Route path="/logout" element={<Navigate to="/" />} />
              <Route path="/register" element={<RegisterPage />} />
              <Route path="/role-selection" element={<RoleSelectionPage />} />
              <Route path="/classes" element={<ClassesPage />} />
              <Route path="/class/:classId/add-student" element={<AddStudentToClass/>}/>
              <Route path="/create-class" element={<CreateClassPage />} />
              <Route path="/class/:classId" element={<ClassDetailPage />} />
              <Route path="/class/:classId/student-manageclass" element={<StudentClassManageDetailPage />} />
              <Route path="/class/:classId/assignments/:assignmentId" element={<AssignmentDetailPage />} />
              <Route path="/class/:classId/create-assignment" element={<CreateAssignmentPage />} />
              <Route path="/class/:classId/submit-assignments/:assignmentId" element={<SubmitAssignment />} />
              <Route path="/join-class" element={<JoinClassPage />} />
              <Route path="/join/:joinCode" element={<JoinClassPage />} />
              <Route path="/teacher-dashboard" element={<TeacherDashboard />} />
              <Route path="/student-dashboard" element={<StudentDashboard />} />
              <Route path="/parent-dashboard" element={<ParentDashboard />} />
              <Route path="/classroom/:classId" element={<ClassroomPage />} />
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/blog" element={<BlogPage />} />
              <Route path="/blog/:blogId" element={<BlogDetailPage />} />
              <Route path="/payments" element={<PaymentsPage />} />
              <Route path="/student-payments" element={<StudentPaymentsPage />} />
              <Route path="/student-classes" element={<StudentClassesPage />} />
              <Route path="/reviews" element={<ReviewsPage />} />
              <Route path="/expenses" element={<ExpensesPage />} />
              <Route path="/announcements" element={<AnnouncementsPage />} />
              <Route path="/enrollments" element={<StudentsPage />} />
              <Route path="/chat" element={<ChatPage />} />
              <Route path="/experience" element={<ExperiencePage />} />
              <Route path="/add-experience" element={<AddExperiencePage />} />
              <Route path="/attendance" element={<AttendancePage />} />
              <Route path="/attendance/:attendanceId" element={<AttendanceViewPage />} />
              <Route path="/dashboard" element={<RoleBasedRedirect />} />
              <Route path="/schedule" element={<Navigate to="/schedule/all" replace />} />
              <Route path="/schedule/all" element={<ScheduleAllPage />} />
              <Route path="/student-schedule/all" element={<StudentScheduleAllPage classId="all" />} />
              <Route path="/subscription" element={<SubscriptionPage />} />
              <Route path="/question-generator" element={<QuestionGeneratorPage />} />
              <Route path="/meeting-scheduler" element={<MeetingSchedulerPage />} />
              <Route path="/parent-meetings" element={<ParentMeetingsPage />} />
              <Route path="/add-student" element={<AddStudentPage />} />
              <Route path="/student-attendance" element={<StudentAttendancePage />} />
              <Route path="/student-assignments" element={<StudentAssignmentsPage />} />
              <Route path="/student-progress" element={<StudentProgressPage />} />
              <Route path="/public-class/:classId" element={<PublicPage />} />
              <Route path="/settingsPage" element={<SettingsPage />} />
              <Route path="/terms" element={<TermsOfServicePage />} />
              <Route path="/privacy" element={<PrivacyPolicyPage />} />
              <Route path="/cookies" element={<CookiePolicyPage />} />
              <Route path="/security" element={<SecurityPage />} />
              <Route path="/help" element={<HelpCenterPage />} />
              <Route path="/faq" element={<FAQPage />} />
             <Route path="/footer" element={<Footer />} />
                <Route path="/contactus" element={<ContactUs />} />
              <Route path="/membershipPage" element={<MembershipPage />} />
              <Route path="*" element={<NotFound />} />
              </Routes>
            </div>
            <Toaster
              position="top-center" 
              richColors 
            />
              </UserDataProvider>
          </AppProvider>
        </AuthProvider>
      </BrowserRouter>
    </CookiesProvider>
  );
}

export default App;
