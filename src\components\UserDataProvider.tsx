import { useEffect } from 'react';
import { useAuth } from 'react-oidc-context';
import { useDispatch } from 'react-redux';
import { setUserData, setSelectedRole } from '@/redux/userSlice';
import { getProfile } from '@/services/profileService';

export const UserDataProvider = ({ children }: { children: React.ReactNode }) => {
  const auth = useAuth();
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchUserData = async () => {
      if (auth.isAuthenticated && auth.user?.access_token && !auth.isLoading) {
        try {
          const userData = await getProfile(auth.user.access_token);
          if (userData) {
            dispatch(setUserData(userData));
            
            // Check if we already have a role in cookies before setting from API
            const cookies = document.cookie.split(';').reduce<Record<string, string>>((acc, cookie) => {
              const [key, value] = cookie.trim().split('=');
              acc[key] = value;
              return acc;
            }, {});
            
            // Only set the role from API if no role is found in cookies
            if (!cookies.userRole && userData.roles?.[0]?.role) {
              console.log('Setting role from API:', userData.roles[0].role);
              dispatch(setSelectedRole(userData.roles[0].role));
            } else if (cookies.userRole) {
              console.log('Using existing role from cookie:', cookies.userRole);
            }
          }
        } catch (error) {
          console.error('Failed to fetch user data:', error);
          // Don't throw error, just log it - user can still use the app
        }
      }
    };

    // Add a small delay to ensure auth is fully loaded
    const timer = setTimeout(fetchUserData, 100);
    return () => clearTimeout(timer);
  }, [auth.isAuthenticated, auth.user?.access_token, auth.isLoading, dispatch]);

  return <>{children}</>;
};