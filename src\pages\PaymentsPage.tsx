import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  Download,
  Filter,
  Plus,
  Search,
  CreditCard,
  CheckCircle,
  ArrowLeft,
  DollarSign,
  AlertTriangle,
  TrendingUp,
  Clock,
  Users,
  Calendar
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import PaymentForm from "@/components/PaymentForm";
import { Payment, UserRole } from "@/types";
import { getAllPaymentAnalytics } from "@/services/paymentService";
import { toast } from "sonner";
import { generateAvatarUrl } from "@/lib/utils";



import { useUserRole } from "@/hooks/useUserRole";


import { useAuth } from "react-oidc-context"; // Updated import

export default function PaymentsPage() {
  const auth = useAuth();
  // Get user from OIDC
    const user = auth.isAuthenticated ? {
      id: auth.user?.profile.sub || "",
      name: auth.user?.profile.name || "User",
      email: auth.user?.profile.email || "",
      role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
      avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
    } : null;
     const { selectedRole } = useUserRole();  

  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  const [showAddPayment, setShowAddPayment] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("overview");

  const [isLoading, setIsLoading] = useState(true);
  const [startDate, setStartDate] = useState<string>(new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const hasFetched = useRef(false);
  
const fetchPayments = async (start = startDate, end = endDate) => {
  try {
    setIsLoading(true);
    const data = await getAllPaymentAnalytics(auth.user?.access_token, start, end);
    setAnalyticsData(data);
    setIsLoading(false);
  } catch (error) {
    console.error("Error fetching payments:", error);
    toast.error("Failed to load payments");
    setAnalyticsData(null);
    setIsLoading(false);
  }
};
  // Fetch announcements on component mount
useEffect(() => {
  if (auth.user?.access_token && !hasFetched.current) {
    hasFetched.current = true;
    fetchPayments();
  }
}, [auth.user?.access_token]);

  // No individual payments to filter in analytics view
  const filteredPayments = [];
 
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "paid":
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case "overdue":
        return <Badge className="bg-red-100 text-red-800">Overdue</Badge>;
      default:
        return null;
    }
  };
 
  const formatDate = (dateInput: string | number) => {
    const date = new Date(dateInput);
    return date.toISOString().split('T')[0];
  };
 
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Use analytics data from service
  const totalRevenue = analyticsData?.totalRevenue || 0;
  const paidAmount = analyticsData?.totalPaid || 0;
  const pendingAmount = analyticsData?.totalPending || 0;
  const overdueAmount = analyticsData?.totalOverdue || 0;
  const totalClasses = analyticsData?.totalClasses || 0;
  const totalStudents = analyticsData?.totalStudents || 0;
  const studentsWithOverduePayments = analyticsData?.studentsWithOverduePayments || 0;
  const studentsWithPendingPayments = analyticsData?.studentsWithPendingPayments || 0;
  const analyticsStartDate = analyticsData?.analyticsStartDate ? new Date(analyticsData.analyticsStartDate) : new Date();
  const analyticsEndDate = analyticsData?.analyticsEndDate ? new Date(analyticsData.analyticsEndDate) : new Date();
  const generatedDate = analyticsData?.generatedDate ? new Date(analyticsData.generatedDate) : new Date();
 
  const handleDownloadReport = () => {
    alert("Downloading payment report...");
  };

  const handleBackToDashboard = () => {
    if (selectedRole === UserRole.TEACHER) {
      navigate('/teacher-dashboard');
    } else if (selectedRole=== UserRole.STUDENT) {
      navigate('/student-dashboard');
    } else if (selectedRole === UserRole.PARENT) {
      navigate('/parent-dashboard');
    } else {
      navigate('/');
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="flex-grow">
        <header className="bg-white p-4 border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-4">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleBackToDashboard}
                    className="h-8 w-8"
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                  <div>
                    <h1 className="text-2xl font-bold text-foreground">Payment Analytics</h1>
                  </div>
                </div>
               
                
              </div>
              
            </div>
          </div>
        </header>
       
        <div className="p-6 min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto space-y-6">
            {showAddPayment ? (
              <Card>
                <CardHeader>
                  <CardTitle>Add New Payment</CardTitle>
                  <CardDescription>
                    Enter the payment details below
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PaymentForm
                    onCancel={() => setShowAddPayment(false)}
                    onSuccess={() => {
                      setShowAddPayment(false);
                    }}
                  />
                </CardContent>
              </Card>
            ) : (
              <>
                {/* Date Range and Summary Counts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Summary Counts */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Users className="h-5 w-5" />
                        Summary Overview
                      </CardTitle>
                      <CardDescription>
                        Key metrics across all classes
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-muted-foreground">Total Classes</p>
                          <p className="text-2xl font-bold text-foreground">{totalClasses}</p>
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-muted-foreground">Total Students</p>
                          <p className="text-2xl font-bold text-foreground">{totalStudents}</p>
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-muted-foreground">Students w/ Overdue</p>
                          <p className="text-2xl font-bold text-red-600">{studentsWithOverduePayments}</p>
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-muted-foreground">Students w/ Pending</p>
                          <p className="text-2xl font-bold text-yellow-600">{studentsWithPendingPayments}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Date Range Analytics */}
                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="flex items-center gap-2">
                            <Calendar className="h-5 w-5" />
                            Analytics Period
                          </CardTitle>
                          <CardDescription>
                            Current reporting timeframe
                          </CardDescription>
                        </div>
                       </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                          <div className="space-y-1">
                            <div className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                              <div className="relative inline-block">
                                <input 
                                  type="date" 
                                  className="opacity-0 absolute inset-0 w-6 h-6 cursor-pointer" 
                                  value={startDate}
                                  onChange={(e) => setStartDate(e.target.value)}
                                />
                                <Calendar className="h-4 w-4 cursor-pointer" />
                              </div>
                              Start Date: {startDate}
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                              <div className="relative inline-block">
                                <input 
                                  type="date" 
                                  className="opacity-0 absolute inset-0 w-6 h-6 cursor-pointer" 
                                  value={endDate}
                                  onChange={(e) => setEndDate(e.target.value)}
                                />
                                <Calendar className="h-4 w-4 cursor-pointer" />
                              </div>
                              End Date: {endDate}
                            </div>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-muted-foreground">Generated</p>
                            <p className="text-lg font-semibold text-foreground">{formatDate(generatedDate.toISOString())}</p>
                          </div>
                     <div className="flex justify-between items-center">
                        <Button 
                          size="sm" 
                          className="bg-primary text-white" 
                          onClick={() => fetchPayments(startDate, endDate)}
                        >
                          Apply Filter
                        </Button>
                        <Download className="h-5 w-5 text-muted-foreground cursor-pointer hover:text-foreground" />
                      </div>
                        </div>
                      

                    </CardContent>
                  </Card>

                </div>

                {/* Payment Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <Card className="border-l-4 border-l-primary">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                          <p className="text-2xl font-bold text-foreground">{formatCurrency(totalRevenue)}</p>
                        </div>
                        <DollarSign className="h-8 w-8 text-primary" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-green-500">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Paid</p>
                          <p className="text-2xl font-bold text-green-600">{formatCurrency(paidAmount)}</p>
                        </div>
                        <CheckCircle className="h-8 w-8 text-green-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-yellow-500">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Pending</p>
                          <p className="text-2xl font-bold text-yellow-600">{formatCurrency(pendingAmount)}</p>
                        </div>
                        <Clock className="h-8 w-8 text-yellow-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-red-500">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Overdue</p>
                          <p className="text-2xl font-bold text-red-600">{formatCurrency(overdueAmount)}</p>
                        </div>
                        <AlertTriangle className="h-8 w-8 text-red-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Red Flags Section */}
                {overdueAmount > 0 && (
                  <Card className="border-red-200 bg-red-50">
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-red-500" />
                        <CardTitle className="text-red-700">Payment Alerts</CardTitle>
                      </div>
                      <CardDescription className="text-red-600">
                        {formatCurrency(overdueAmount)} in overdue payments require attention
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="p-4 bg-white rounded-lg border border-red-200">
                        <p className="font-medium text-red-800">Total Overdue Amount</p>
                        <p className="text-2xl font-bold text-red-600">{formatCurrency(overdueAmount)}</p>
                        <p className="text-sm text-red-600 mt-2">{studentsWithOverduePayments} students affected</p>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Class-wise Analytics */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      Quick Stats by Class
                    </CardTitle>
                    <CardDescription>
                      Revenue and student analytics for each class
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {analyticsData?.classStats && analyticsData.classStats.length > 0 ? (
                        analyticsData.classStats.map((classData: any, index: number) => (
                          <div key={index} className="p-4 border border-border rounded-lg bg-card">
                            <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
                              <div>
                                <p className="text-sm font-medium text-muted-foreground">Class Name</p>
                                <p className="text-lg font-semibold text-foreground">{classData.className || `Class ${index + 1}`}</p>
                              </div>
                              <div>
                                <p className="text-sm font-medium text-muted-foreground">Revenue</p>
                                <p className="text-lg font-semibold text-green-600">{formatCurrency(classData.totalRevenue || 0)}</p>
                              </div>
                              <div>
                                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                                <p className="text-lg font-semibold text-yellow-600">{formatCurrency(classData.pendingAmount || 0)}</p>
                              </div>
                              <div>
                                <p className="text-sm font-medium text-muted-foreground">Students</p>
                                <p className="text-lg font-semibold text-foreground">{classData.studentCount || 0}</p>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : null}
                      {(!analyticsData?.classStats || analyticsData.classStats.length === 0) && (
                        <p className="text-center text-muted-foreground py-8">No class data available</p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Payment History Table */}
                <Card>
                  <CardHeader>
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                      <div>
                        <CardTitle>Payment History</CardTitle>
                        <CardDescription>
                          Detailed view of all payment records across classes
                        </CardDescription>
                      </div>
                     
                      <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                        <div className="relative">
                          <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            className="pl-10 w-full sm:w-64"
                            placeholder="Search payments..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                          />
                        </div>
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                          <SelectTrigger className="w-full sm:w-32">
                            <SelectValue placeholder="Status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="paid">Paid</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="overdue">Overdue</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID</TableHead>
                          <TableHead>Student</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredPayments.length > 0 ? (
                          filteredPayments.map((payment) => (
                            <TableRow key={payment.id}>
                              <TableCell className="font-medium">{payment.id}</TableCell>
                              <TableCell className="font-medium">{payment.studentId}</TableCell>
                              <TableCell>{payment.description}</TableCell>
                              <TableCell>{formatDate(payment.date)}</TableCell>
                              <TableCell>{formatCurrency(payment.amount)}</TableCell>
                              <TableCell>{getStatusBadge(payment.status)}</TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  {payment.status !== "paid" && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-green-600 border-green-200 hover:bg-green-50 hover:text-green-700"
                                    >
                                      <CheckCircle className="h-4 w-4 mr-1" /> Mark Paid
                                    </Button>
                                  )}
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <CreditCard className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                              No payments found matching your filters.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </CardContent>
                  <CardFooter className="flex justify-between border-t pt-4">
                    <div className="text-sm text-muted-foreground">
                      Analytics view - individual payments not available
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" disabled>
                        Previous
                      </Button>
                      <Button variant="outline" size="sm" disabled>
                        Next
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}