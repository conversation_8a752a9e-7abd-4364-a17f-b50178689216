import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "react-oidc-context";
import { useApp } from "@/context/AppContext";
import { toast } from "sonner";
import { PlusCircle, Search, Edit, Trash, Check, X, FileImage, Filter, Bell ,ArrowLeft } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Sheet, SheetContent, Sheet<PERSON>es<PERSON>, SheetHeader, Sheet<PERSON>itle, SheetTrigger } from "@/components/ui/sheet";
import { UserRole, Announcement } from "@/types";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import {getClassIdsAndNames, getAllAnnouncements, createNewAnnouncement,createNewBulkAnnouncement, updateExistingAnnouncement, deleteExistingAnnouncement } from "@/services/announcementService";
import { useSelector } from "react-redux";
import { fileUpload, fileDownload } from "@/utils/fileUpload";
import { Upload, File } from "lucide-react"; 

import { useUserRole } from "@/hooks/useUserRole";

interface AttachedFile {
 
  id: string;
  originalFilename: string;
  fileSize: number;
  downloadUrl: string;
}
export default function AnnouncementsPage() {
  const auth = useAuth();
  const [classOptions, setClassOptions] = useState<{ id: string; className: string }[]>([]);
   const user =  {
      id: auth.user?.profile.sub || "",
      name: auth.user?.profile.name || "User",
      email: auth.user?.profile.email || "",
      role: (auth.user?.profile["custom:role"] as UserRole) ,
     // avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
    } ;
       const userData = useSelector((state: any) => state.user.userData);
  const { selectedRole: userRole } = useUserRole();
    const { classes } = useApp();
  const navigate = useNavigate();
  const location = useLocation();
  const selectedId = location.state?.selectedAnnouncementId;
  const selectedRef = React.useRef<HTMLDivElement>(null);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedClass, setSelectedClass] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [newTitle, setNewTitle] = useState<string>("");
  const [newContent, setNewContent] = useState<string>("");
  const [newClassId, setNewClassId] = useState<string>("");
  const [showForm, setShowForm] = useState<boolean>(false);
  const [editingAnnouncementId, setEditingAnnouncementId] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState({
    title: "",
    content: "",
    classId: ""
  });
  const [editAttachedFiles, setEditAttachedFiles] = useState<AttachedFile[]>([]);
  const [sheetOpen, setSheetOpen] = useState(false);
  const [showFullContent, setShowFullContent] = useState(false);
  const isTeacher = userRole === UserRole.TEACHER;
      const [attachedFiles, setAttachedFiles] = useState<AttachedFile[]>([]);
    const [uploading, setUploading] = useState(false);
  const [validationErrors, setValidationErrors] = useState({
    classId: "",
    title: "",
    content: ""
  });
  const [editValidationErrors, setEditValidationErrors] = useState({
    title: "",
    content: ""
  });
  const hasInitialized = useRef(false);
  
  const fetchInitialData = async () => {
    if (!auth.user?.access_token || hasInitialized.current) return;
    
    hasInitialized.current = true;
    try {
      setIsLoading(true);
      const [announcementsData, classesData] = await Promise.all([
        getAllAnnouncements(auth.user.access_token),
        getClassIdsAndNames(auth.user.access_token)
      ]);
      
      setAnnouncements(announcementsData.map(a => ({
        ...a,
        createdAt: Number(a.createdAt)
      })));
      setClassOptions(classesData);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInitialData();
  }, []);
  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesClass = selectedClass === "all" || announcement.classroomId === selectedClass;
    
    // Strip HTML tags from content for search
    const stripHtml = (html: string) => {
      const tmp = document.createElement('div');
      tmp.innerHTML = html;
      return tmp.textContent || tmp.innerText || '';
    };
    
    const matchesSearch = searchTerm === "" || 
      (announcement.title && announcement.title.toLowerCase().includes(searchTerm.toLowerCase())) || 
      (announcement.content && stripHtml(announcement.content).toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesClass && matchesSearch;
  }).sort((a, b) => new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime());



  const validateAnnouncementForm = () => {
    const errors = {
      classId: "",
      title: "",
      content: ""
    };
    
    if (!newClassId || newClassId.trim() === "") {
      errors.classId = "Class selection is required";
    }
    if (!newTitle || newTitle.trim() === "") {
      errors.title = "Title is required";
    }
    if (!newContent || newContent.trim() === "" || newContent === "<p></p>") {
      errors.content = "Content is required";
    }
    
    setValidationErrors(errors);
    return Object.values(errors).every(error => error === "");
  };

  const handleCreateAnnouncement = async () => {
    if (!validateAnnouncementForm()) {
      return;
    }
  
  try {
    const now = Date.now();
    
    if (newClassId === "all") {
      // Create announcements for all classes
      await createNewBulkAnnouncement(
        auth.user.access_token,
       {
          title: newTitle,
          content: newContent,
          attachedFileIds: attachedFiles.map((file) => file.id),
        }
      );
      toast.success("Announcements created for all classes");
    } else {
      // Create announcement for a single class
      const newAnnouncement = await createNewAnnouncement(
        auth.user.access_token,
        newClassId,
        {
          title: newTitle,
          content: newContent,
          attachedFileIds: attachedFiles.map((file) => file.id)
        }
      );
      setAnnouncements(prev => [newAnnouncement, ...prev]);
    }
    
    // Reset form
    setNewTitle("");
    setNewContent("");
    setNewClassId("");
    setAttachedFiles([]);
    setValidationErrors({ classId: "", title: "", content: "" });
    setShowForm(false);
    
    // Refresh announcements to show the newly created ones
    fetchInitialData();
    
  } catch (error) {
    console.error("Error creating announcement:", error);
    toast.error("Failed to create announcement");
  }
};

  const handleEditClick = (announcement: Announcement) => {
    setEditingAnnouncementId(announcement.id);
    setEditFormData({
      title: announcement.title,
      content: announcement.content,
      classId: announcement.classroomId
    });
    // Convert file IDs to file objects for display
    const existingFiles = (announcement.attachedFileIds || []).map(fileId => ({
      id: fileId,
      originalFilename: fileId, // Using fileId as filename for now
      fileSize: 0, // Size unknown for existing files
      downloadUrl: ''
    }));
    setEditAttachedFiles(existingFiles);
  };
  const validateEditForm = () => {
    const errors = {
      title: "",
      content: ""
    };
    
    if (!editFormData.title || editFormData.title.trim() === "") {
      errors.title = "Title is required";
    }
    if (!editFormData.content || editFormData.content.trim() === "" || editFormData.content === "<p></p>") {
      errors.content = "Content is required";
    }
    
    setEditValidationErrors(errors);
    return Object.values(errors).every(error => error === "");
  };

  const handleSaveEdit = async (id: string) => {
    if (!validateEditForm()) {
      return;
    }
    try {
      const updatedAnnouncement = await updateExistingAnnouncement(auth.user.access_token,id, {
        title: editFormData.title,
        content: editFormData.content,
        classId: editFormData.classId,
        attachedFileIds: editAttachedFiles.map((file) => file.id)
      });
      setAnnouncements(prev => prev.map(a => a.id === id ? updatedAnnouncement : a));
      setEditingAnnouncementId(null);
      setEditValidationErrors({ title: "", content: "" });
      toast.success("Announcement updated");
    } catch (error) {
      console.error("Error updating announcement:", error);
      toast.error("Failed to update announcement");
    }
  };
  const handleCancelEdit = () => {
    setEditingAnnouncementId(null);
    setEditAttachedFiles([]);
    setEditValidationErrors({ title: "", content: "" });
  };
  
  const handleDeleteAnnouncement = async (id: string) => {
  try {
    await deleteExistingAnnouncement(auth.user.access_token,id);
    setAnnouncements(prev => prev.filter(a => a.id !== id));
    toast.success("Announcement deleted");
  } catch (error) {
    console.error("Error deleting announcement:", error);
    toast.error("Failed to delete announcement");
  }
};
  
  const handleBackToDashboard = () => {
    
    if (userRole === UserRole.TEACHER) {
      navigate('/teacher-dashboard');
    } else if (userRole === UserRole.STUDENT) {
      navigate('/student-dashboard');
    } else if (userRole === UserRole.PARENT) {
      navigate('/parent-dashboard');
    } else {
      navigate('/');
    }
  };
  const closeFilterSheet = () => {
    setSheetOpen(false);
  };
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files);
    setUploading(true);
    try {
      const uploadedFiles = await fileUpload(auth.user?.access_token, files);
       
      setAttachedFiles(prev => [...prev, ...uploadedFiles]);
    } catch (error) {
      console.error('File upload error:', error);
      if (error.message === 'Failed to fetch' || error.message.includes('ERR_CONNECTION_RESET')) {
        toast.error('Connection lost during upload. Please check your internet connection and try again.');
      } else {
        toast.error('Failed to upload file. Please try again.');
      }
    } finally {
      setUploading(false);
    }
  };
   const removeFile = (fileId: string) => {
      setAttachedFiles(prev => prev.filter(file => file.id !== fileId));
    };

  const handleEditFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files);
    setUploading(true);
    try {
      const uploadedFiles = await fileUpload(auth.user?.access_token, files);
      setEditAttachedFiles(prev => [...prev, ...uploadedFiles]);
    } catch (error) {
      console.error('File upload error:', error);
      if (error.message === 'Failed to fetch' || error.message.includes('ERR_CONNECTION_RESET')) {
        toast.error('Connection lost during upload. Please check your internet connection and try again.');
      } else {
        toast.error('Failed to upload file. Please try again.');
      }
    } finally {
      setUploading(false);
    }
  };

  const removeEditFile = (fileId: string) => {
    setEditAttachedFiles(prev => prev.filter(file => file.id !== fileId));
  };
  
    const formatFileSize = (bytes: number) => {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

  const handleDownloadFile = async (fileId: string) => {
    await fileDownload(auth.user?.access_token, fileId, fileId);
  };
   
  React.useEffect(() => {
    if (selectedId && selectedRef.current) {
      selectedRef.current.scrollIntoView({
        behavior: 'smooth'
      });
    }
  }, [selectedId, announcements]);
  return <div className="min-h-screen bg-gray-50">
      <div className="flex-1 flex flex-col">
        <header className="bg-white p-4 flex justify-between items-center border-b border-gray-200 h-14 sticky top-0 z-10">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              className="h-8 w-8 text-zinc-700" 
              onClick={handleBackToDashboard}
            >
              <ArrowLeft className="h-4 w-4 md:h-5 md:w-5" />
            </Button>
            <h1 className="text-xl font-semibold">Announcements</h1>
          </div>
          <div className="flex items-center gap-2">
            {isTeacher && <Button onClick={() => setShowForm(!showForm)} className="bg-purple-600 hover:bg-purple-700 text-white flex items-center gap-1 relative z-10" size="sm">
                <PlusCircle className="h-4 w-4" /> 
                <span className="hidden sm:inline">{showForm ? "Hide Form" : "New Announcement"}</span>
                <span className="sm:hidden">New</span>
              </Button>}
          </div>
        </header>
        
        <div className="p-4 sm:p-6 flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto">
            {showForm && isTeacher && <Card className="mb-8">
                <CardHeader className="pb-2 sm:pb-4">
                  <CardTitle className="text-lg sm:text-xl">Create New Announcement</CardTitle>
                  <CardDescription>
                    Publish a new announcement for your class
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Class*</label>
                    <Select 
                      value={newClassId} 
                      onValueChange={(value) => {
                        setNewClassId(value);
                        if (validationErrors.classId) setValidationErrors(prev => ({ ...prev, classId: "" }));
                      }}
                    >
                      <SelectTrigger className={validationErrors.classId ? 'border-red-500' : 'edu-form-field'}>
                        <SelectValue placeholder="Select a class" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Classes</SelectItem>
                        {classOptions.map(classItem => <SelectItem key={classItem.id} value={classItem.id}>
                            {classItem.className}
                          </SelectItem>)}
                      </SelectContent>
                    </Select>
                    {validationErrors.classId && <p className="text-red-500 text-xs mt-1">{validationErrors.classId}</p>}
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Title*</label>
                    <Input 
                      placeholder="Enter announcement title" 
                      value={newTitle} 
                      onChange={e => {
                        setNewTitle(e.target.value);
                        if (validationErrors.title) setValidationErrors(prev => ({ ...prev, title: "" }));
                      }}
                      className={validationErrors.title ? 'border-red-500' : 'edu-form-field'}
                    />
                    {validationErrors.title && <p className="text-red-500 text-xs mt-1">{validationErrors.title}</p>}
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Content*</label>
                    <RichTextEditor 
                      value={newContent} 
                      onChange={(value) => {
                        setNewContent(value);
                        if (validationErrors.content) setValidationErrors(prev => ({ ...prev, content: "" }));
                      }}
                      placeholder="Enter announcement content"
                      className={validationErrors.content ? 'border-red-500' : 'edu-form-field'}
                    />
                    {validationErrors.content && <p className="text-red-500 text-xs mt-1">{validationErrors.content}</p>}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                       {/* File Upload Section */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Attachments</label>
            
            <Card className="border-dashed border-2 hover:border-primary/50 transition-colors">
              <CardContent className="p-6">
                <label htmlFor="file-upload" className="cursor-pointer">
                  <div className="flex flex-col items-center justify-center text-center">
                    <Upload className="w-full h-8 text-muted-foreground mb-2" />
                    <p className="text-sm font-medium text-muted-foreground">
                      Click to upload files
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Support for multiple file types
                    </p>
                  </div>
                   <input
                  id="file-upload"
                  type="file"
                  multiple
                  className="hidden"
                  onChange={handleFileUpload}
                  accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.mp4,.mp3"
                  disabled={uploading}
                />
                </label>
              </CardContent>
            </Card>

            {/* Attached Files */}
            {attachedFiles.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">
                  Attached Files ({attachedFiles.length})
                </p>
                <div className="space-y-2">
                  {attachedFiles.map((file) => (
                    <Card key={file.id} className="bg-muted/50">
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <File className="h-4 w-4 text-primary" />
                            <div>
                              <p className="text-sm font-medium text-foreground truncate max-w-[200px]">
                                {file.originalFilename}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {formatFileSize(file.fileSize)}
                              </p>
                            </div>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(file.id)}
                            className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </div>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col sm:flex-row justify-between gap-2">
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setShowForm(false);
                      setValidationErrors({ classId: "", title: "", content: "" });
                    }} 
                    className="w-full sm:w-auto"
                  >
                    Cancel
                  </Button>
                  <Button className="bg-purple-600 hover:bg-purple-700 w-full sm:w-auto" onClick={handleCreateAnnouncement}>
                    Publish Announcement
                  </Button>
                </CardFooter>
              </Card>}
            
            <div className="hidden sm:flex justify-between items-center mb-6">
              <div className="w-64">
                 <Select value={selectedClass} onValueChange={setSelectedClass}>
                      <SelectTrigger className="edu-form-field">
                        <SelectValue placeholder="Select a class" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Classes</SelectItem>
                        {classOptions.map(classItem => <SelectItem key={classItem.id} value={classItem.id}>
                            {classItem.className}
                          </SelectItem>)}
                      </SelectContent>
                    </Select>
              </div>
              <div className="relative w-64">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                <Input className="edu-form-field pl-10" placeholder="Search announcements" value={searchTerm} onChange={e => setSearchTerm(e.target.value)} />
              </div>
            </div>
            
            <div className="flex sm:hidden items-center justify-between mb-4">
              <div className="relative flex-1 max-w-[calc(100%-70px)]">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input className="edu-form-field pl-10 h-10" placeholder="Search" value={searchTerm} onChange={e => setSearchTerm(e.target.value)} />
              </div>
              <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon" className="ml-2 h-10 w-10">
                    <Filter className="h-4 w-4" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="bottom" className="h-[300px]">
                  <SheetHeader className="text-left">
                    <SheetTitle>Filter Announcements</SheetTitle>
                    <SheetDescription>
                      Select a class to filter the announcements
                    </SheetDescription>
                  </SheetHeader>
                  <div className="py-6">
                    <Select value={selectedClass} onValueChange={value => {
                    setSelectedClass(value);
                    closeFilterSheet();
                  }}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select a class" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Classes</SelectItem>
                        {classOptions.map(classItem => <SelectItem key={classItem.id} value={classItem.id}>
                            {classItem.className}
                          </SelectItem>)}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex justify-end">
                    <Button onClick={closeFilterSheet}>Done</Button>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
            
            <div className="space-y-4 sm:space-y-6">
              {filteredAnnouncements.length > 0 ? filteredAnnouncements.map(announcement => {
              const classInfo = classOptions.find(c => c.id === announcement.classroomId);
              const isEditing = editingAnnouncementId === announcement.id;
              const isSelected = announcement.id === selectedId;
              return <Card key={announcement.id} ref={isSelected ? selectedRef : undefined} className={`transition-colors ${isSelected ? 'bg-purple-50 border-purple-200' : ''}`}>
                    <CardHeader className="pb-3">
                      {isEditing && isTeacher ? <div className="space-y-2">
                          <Input 
                            value={editFormData.title} 
                            onChange={e => {
                              setEditFormData({
                                ...editFormData,
                                title: e.target.value
                              });
                              if (editValidationErrors.title) setEditValidationErrors(prev => ({ ...prev, title: "" }));
                            }} 
                            className={`edu-form-field font-semibold text-lg ${editValidationErrors.title ? 'border-red-500' : ''}`} 
                          />
                          {editValidationErrors.title && <p className="text-red-500 text-xs mt-1">{editValidationErrors.title}</p>}
                          <Select value={editFormData.classId} onValueChange={value => setEditFormData({
                      ...editFormData,
                      classId: value
                    })}>
                            
                          </Select>
                        </div> : <>
                          <CardTitle className="flex justify-between items-start">
                            <span className="text-base sm:text-lg mr-2">{announcement.title}</span>
                            {isTeacher && <div className="flex gap-1 sm:gap-2 flex-shrink-0">
                                <Button variant="ghost" size="icon" onClick={() => handleEditClick(announcement)} className="h-7 w-7 sm:h-8 sm:w-8">
                                  <Edit className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                                </Button>
                                <Button variant="ghost" size="icon" onClick={() => handleDeleteAnnouncement(announcement.id)} className="h-7 w-7 sm:h-8 sm:w-8 text-red-600">
                                  <Trash className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                                </Button>
                              </div>}
                          </CardTitle>
                          <CardDescription className="flex items-center justify-between flex-wrap text-xs sm:text-sm">
                          <span className="font-medium text-purple-600">{classInfo?.className}</span>
                          <span>
                         {(() => {
                            const date = new Date(announcement.createdDate);
                            return isNaN(date.getTime()) ? "Invalid date" : format(date,"MMM d, yyyy");
                          })()}
                        </span>
                          </CardDescription>
                        </>}
                    </CardHeader>
                    <CardContent>
                      {isEditing && isTeacher ? <div className="space-y-4">
                        <RichTextEditor 
                          value={editFormData.content} 
                          onChange={(content) => {
                            setEditFormData({
                              ...editFormData,
                              content
                            });
                            if (editValidationErrors.content) setEditValidationErrors(prev => ({ ...prev, content: "" }));
                          }}
                          placeholder="Edit announcement content"
                          className={editValidationErrors.content ? 'border-red-500' : ''}
                        />
                        {editValidationErrors.content && <p className="text-red-500 text-xs mt-1">{editValidationErrors.content}</p>}
                        <div className="space-y-3">
                          <label className="text-sm font-medium">Attachments</label>
                          <Card className="border-dashed border-2 hover:border-primary/50 transition-colors">
                            <CardContent className="p-4">
                              <label htmlFor="edit-file-upload" className="cursor-pointer">
                                <div className="flex flex-col items-center justify-center text-center">
                                  <Upload className="w-6 h-6 text-muted-foreground mb-2" />
                                  <p className="text-sm font-medium text-muted-foreground">Click to upload files</p>
                                </div>
                                <input
                                  id="edit-file-upload"
                                  type="file"
                                  multiple
                                  className="hidden"
                                  onChange={handleEditFileUpload}
                                  accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.mp4,.mp3"
                                  disabled={uploading}
                                />
                              </label>
                            </CardContent>
                          </Card>
                          {editAttachedFiles.length > 0 && (
                            <div className="space-y-2">
                              <p className="text-sm font-medium text-muted-foreground">
                                Attached Files ({editAttachedFiles.length})
                              </p>
                              {editAttachedFiles.map((file) => (
                                <Card key={file.id} className="bg-muted/50">
                                  <CardContent className="p-3">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-3">
                                        <File className="h-4 w-4 text-primary" />
                                        <div>
                                          <p className="text-sm font-medium text-foreground truncate max-w-[200px]">
                                            {file.originalFilename}
                                          </p>
                                          {file.fileSize > 0 && (
                                            <p className="text-xs text-muted-foreground">
                                              {formatFileSize(file.fileSize)}
                                            </p>
                                          )}
                                        </div>
                                      </div>
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeEditFile(file.id)}
                                        className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                                      >
                                        <X className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}
                            </div>
                          )}
                        </div>
                      </div> : <div className="space-y-4">
                        {announcement.content.length > 500 ? (
                          <div className="space-y-2">
                            {showFullContent ? (
                              <div className="prose max-w-none text-sm sm:text-base" dangerouslySetInnerHTML={{ __html: announcement.content }} />
                            ) : (
                              <div className="prose max-w-none text-sm sm:text-base" dangerouslySetInnerHTML={{ __html: announcement.content.substring(0, 200) + '...' }} />
                            )}
                            <Button 
                              variant="link" 
                              size="sm" 
                              onClick={() => setShowFullContent(!showFullContent)}
                              className="text-purple-600 p-0 h-auto"
                            >
                              {showFullContent ? 'View Less' : 'View More'}
                            </Button>
                          </div>
                        ) : (
                          <div className="prose max-w-none text-sm sm:text-base" dangerouslySetInnerHTML={{ __html: announcement.content }} />
                        )}
                        {announcement.attachedFileIds && announcement.attachedFileIds.length > 0 && (
                          <div className="space-y-2">
                            <p className="text-sm font-medium text-muted-foreground">Attachments:</p>
                            <div className="space-y-2">
                              {announcement.attachedFileIds.map((fileId) => (
                                <div 
                                  key={fileId} 
                                  className="text-sm text-purple-600 hover:text-purple-800 cursor-pointer"
                                  onClick={() => handleDownloadFile(fileId)}
                                >
                                  📎 {fileId}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>}
                    </CardContent>
                    {isEditing && isTeacher && <CardFooter className="flex flex-col sm:flex-row justify-end gap-2">
                        <Button variant="outline" size="sm" onClick={handleCancelEdit} className="w-full sm:w-auto">
                          <X className="h-4 w-4 mr-1" /> Cancel
                        </Button>
                        <Button className="bg-purple-600 hover:bg-purple-700 w-full sm:w-auto" size="sm" onClick={() => handleSaveEdit(announcement.id)}>
                          <Check className="h-4 w-4 mr-1" /> Save Changes
                        </Button>
                      </CardFooter>}
                  </Card>;
            }) : <div className="text-center py-8 sm:py-12 bg-white rounded-lg border">
                  <Bell className="h-10 w-10 sm:h-12 sm:w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-700 mb-1">No announcements found</h3>
                  <p className="text-gray-500 mb-4 text-sm sm:text-base">
                    {searchTerm || selectedClass !== "all" ? "Try changing your search or filter criteria" : isTeacher ? "Create your first announcement to get started" : "No announcements available"}
                  </p>
                  {!showForm && isTeacher && <Button className="bg-purple-600 hover:bg-purple-700" onClick={() => setShowForm(true)}>
                      <PlusCircle className="h-4 w-4 mr-2" /> New Announcement
                    </Button>}
                </div>}
            </div>
          </div>
        </div>
      </div>
    </div>;
}