
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { 
  Home, 
  BookOpen, 
  Users, 
  MessageSquare, 
  Bell, 
  DollarSign, 
  FileText,
  Calendar,
  ClipboardCheck,
} from "lucide-react";
import { Button } from "@/components/ui/button";

export interface SidebarNavigationProps {
  activeItem?: string;
}

const SidebarNavigation: React.FC<SidebarNavigationProps> = ({ activeItem }) => {
  const location = useLocation();
  const currentPath = location.pathname;

  const navigationItems = [
    {
      name: "Dashboard",
      path: "/teacher-dashboard",
      icon: Home
    },
    {
      name: "Classes",
      path: "/classes",
      icon: BookOpen
    },
    {
      name: "Students",
      path: "/students",
      icon: Users
    },
    {
      name: "Schedule",
      path: "/schedule/all",
      icon: Calendar
    },
    {
      name: "Chat",
      path: "/chat",
      icon: MessageSquare
    },
    {
      name: "Announcements",
      path: "/announcements",
      icon: Bell
    },
    {
      name: "Payment",
      path: "/payments",
      icon: DollarSign
    },
    {
      name: "Expenses",
      path: "/expenses",
      icon: FileText
    },
    {
      name: "Attendance",
      path: "/attendance",
      icon: ClipboardCheck
    }
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col z-10">
      <div className="p-5 flex items-center gap-2 border-b border-gray-200 h-14">
        <div className="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
          <BookOpen className="text-white h-5 w-5" />
        </div>
        <span className="text-lg font-medium">Manage Class</span>
      </div>

      <nav className="flex-grow">
        <ul className="space-y-0.5 py-2">
          {navigationItems.map((item) => (
            <li key={item.name} className="px-3">
              <Link 
                to={item.path} 
                className={`flex items-center gap-3 px-3 py-2 rounded-md relative z-20 ${
                  (activeItem === item.name.toLowerCase() || currentPath === item.path) 
                    ? "bg-purple-100 text-purple-700" 
                    : "text-gray-700 hover:bg-gray-100"
                }`}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.name}</span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      <div className="mt-auto p-4 border-t border-gray-200">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center justify-center mb-2">
            <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center border border-gray-200">
              <img src="/lovable-uploads/be4a4452-329d-4a54-b616-07af5c82205b.png" alt="Support" className="w-10 h-10" />
            </div>
          </div>
          <h3 className="text-center font-semibold">Support 24/7</h3>
          <p className="text-center text-sm text-gray-500">Contact us anytime</p>
          <Button className="w-full mt-3 bg-purple-600 hover:bg-purple-700 relative z-20">Chat</Button>
        </div>
      </div>
    </div>
  );
};

export default SidebarNavigation;
