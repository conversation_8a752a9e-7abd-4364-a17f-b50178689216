
import aboutData from '../data/about.json';

interface Education {
  id: string;
  degree: string;
  institution: string;
  year: string;
}

interface Certification {
  id: string;
  name: string;
  issuer: string;
  year: string;
}

interface Contact {
  email: string;
  phone: string;
  office: string;
  officeHours: string;
}

interface AboutData {
  title: string;
  name: string;
  profileImage: string;
  headline: string;
  bio: string;
  education: Education[];
  certifications: Certification[];
  specialties: string[];
  contact: Contact;
}

export const getAboutData = (): AboutData => {
  return aboutData;
};
