import React from 'react';
import { Button } from '@/components/ui/button';

interface DaySelectorProps {
  selectedDays: string[];
  
  onDaysChange: (days: string[]) => void;
}

const DaySelector: React.FC<DaySelectorProps> = ({ selectedDays, onDaysChange }) => {
  const days = [
    { short: 'MON', full: 'MONDAY' },
    { short: 'TUE', full: 'TUESDAY' },
    { short: 'WED', full: 'WEDNESDAY' },
    { short: 'THU', full: 'THURSDAY' },
    { short: 'FRI', full: 'FRIDAY' },
    { short: 'SAT', full: 'SATURDAY' },
    { short: 'SUN', full: 'SUNDAY' },
  ];

  const toggleDay = (day: string) => {
    if (selectedDays.includes(day)) {
      onDaysChange(selectedDays.filter(d => d !== day));
    } else {
      onDaysChange([...selectedDays, day]);
    }
  };

  return (
    <div className="flex flex-wrap gap-2">
      {days.map((day) => (
        <Button
          key={day.full}
          variant={selectedDays.includes(day.full) ? "default" : "outline"}
          size="sm"
          onClick={() => toggleDay(day.full)}
          className={`px-4 py-2 text-sm font-medium transition-all duration-200 ${
            selectedDays.includes(day.full)
              ? 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600'
              : 'bg-white hover:bg-gray-50 text-gray-700 border-gray-300'
          }`}
        >
          {day.short}
        </Button>
      ))}
    </div>
  );
};

export default DaySelector;