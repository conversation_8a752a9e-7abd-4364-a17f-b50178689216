import { SyllabusTopicList, type SyllabusTopic } from "@/components/syllabus/SyllabusTopicList";
import { SyllabusTopicForm } from "@/components/syllabus/SyllabusTopicForm";
import AITopicForm from "@/components/AITopicForm";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { useState, useEffect,useRef } from "react";
import { toast } from "sonner";
import { useApp } from "@/context/AppContext";
import { addSyllabusToClassroom,getAllSyllabus } from "@/services/syllabusService";
import { useParams } from "react-router-dom";
import { Arrow } from "@radix-ui/react-dropdown-menu";
import { ArrowLeft } from "lucide-react";
const SyllabusTab = ({ currentClass, auth, refreshTrigger }: { currentClass?: any; auth?: any; refreshTrigger?: number }) => {
   const params = useParams<{ classId: string }>();
     const classId = currentClass?.id || params.classId;
const [activeSyllabusTab, setActiveSyllabusTab] = useState("add");
    const [showTabs, setShowTabs] = useState(false); // controls whether tabs are shown (create mode) or list is shown
     
  const hasFetchedSyllabus = useRef(false);
    const [syllabusTopics, setSyllabusTopics] = useState<SyllabusTopic[]>();
    const [isAddTopicOpen, setIsAddTopicOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [isCompleted, setIsCompleted] = useState(() => currentClass?.completed || false);
 // ...existing code...
   
  const fetchSyllabus = async () => {
        try {
          setIsLoading(true);
          const data = await getAllSyllabus(auth.user.access_token, classId);
          setSyllabusTopics(data);
          console.log(syllabusTopics)
          setIsLoading(false);
        } catch (error) {
          console.error("Error fetching syllabus:", error);
          toast.error("Failed to load syllabus");
          setIsLoading(false);
        }
      }; 
  useEffect(() => {
      if (!auth?.user?.access_token || !classId) return;
      if (!hasFetchedSyllabus.current || typeof refreshTrigger !== 'undefined') {
        hasFetchedSyllabus.current = true;
        fetchSyllabus();
      }
    }, [auth?.user?.access_token, classId, refreshTrigger]);
 const handleUpdateTopicStatus = (topicId: string, newStatus: SyllabusTopic['status']) => {
    setSyllabusTopics(prev => prev.map(topic => topic.id === topicId ? {
      ...topic,
      status: newStatus
    } : topic));
   // toast.success(`Topic status updated to ${newStatus}`);
  };
  const handleAddTopic = async (data: {
      title: string;
      description: string;
      attachedFiles?: any[];
    }) => {
      const newTopic = {
        //id: Date.now().toString(),
        title: data.title,
        content: data.description,
      attachedFileIds: data.attachedFiles?.map(file => file.id) || [], // Only IDs
        //status : "OPEN",
        //fileUrl: "JSDHLSJLJ"
      };
      try {
      console.log(newTopic);
        await addSyllabusToClassroom(auth.user.access_token, classId, newTopic);
      //setSyllabusTopics(prev => [...(prev || []), newTopic]);
      setIsAddTopicOpen(false);
      toast.success("Topic added successfully");
       // Refresh the syllabus topic list after successful add
      fetchSyllabus();
    
    } catch (error) {
      toast.error("Failed to add topic");
    }
    };
  
     
  return (
    <div className="max-w-6xl mx-auto">
                                    {/* Top create button */}
                  {!showTabs && (
                <>    <div>
                      <Button className="bg-purple-600 hover:bg-purple-700 mb-4" 
                     disabled={currentClass.completed}
                     onClick={() => { setShowTabs(true); setActiveSyllabusTab("add"); }} >
                        Create syllabus
                      </Button>
                    </div>
                      <SyllabusTopicList
                       topics={syllabusTopics}
                      onUpdateStatus={handleUpdateTopicStatus}
                      onAddTopic={() => {
                        setActiveSyllabusTab("add");
                        setShowTabs(true);
                      }}
                      onRefresh={fetchSyllabus}
                    />
              </>
                  )}
                  {/* Tabs (shown when creating/generating topics) */}
                  {showTabs && (
                   <>  <Button variant="outline" onClick={() => setShowTabs(false)} className="ml-4">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                          Go back
                        </Button>
                      
                    <Tabs value={activeSyllabusTab} onValueChange={setActiveSyllabusTab} className="w-full mt-1">
                      
                      <div className="flex items-center justify-between ">
                       
                        <TabsList className="grid w-full grid-cols-2 h-12 bg-transparent border-0 p-0">
                          <TabsTrigger className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-purple-600 data-[state=active]:bg-transparent px-4" value="add" disabled={isCompleted}>
                            Add
                          </TabsTrigger>
                          <TabsTrigger className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-purple-600 data-[state=active]:bg-transparent px-4" value="generate" disabled={isCompleted}>
                            Generate with AI
                          </TabsTrigger>
                        </TabsList>
                      </div>

                      <TabsContent value="add" className="mt-4">
                        <div className="border rounded-lg p-6 bg-white">
                          <p className="text-sm text-gray-600 mb-4">Create a new syllabus topic for this class</p>
                          <SyllabusTopicForm
                            onSubmit={async (data) => { await handleAddTopic(data); setShowTabs(false); }}
                            onCancel={() => setShowTabs(false)}
                          />
                        </div>
                     </TabsContent>

                      <TabsContent value="generate" className="mt-4">
                        <div className="border rounded-lg p-6 bg-white">
                         <p className="text-sm text-gray-600 mb-4">Create a new syllabus topic for this class</p>
                          <AITopicForm
                            className={currentClass?.subjectName}
                           gradeLevel={currentClass?.level}
                            onCancel={() => setShowTabs(false)}
                            onSuccess={() => { fetchSyllabus(); setShowTabs(false); }}
                          />
                        </div>
                      </TabsContent>
                    </Tabs>
                    </>
                  )}
                   </div>

                  
                  
  );
};

export default SyllabusTab;
