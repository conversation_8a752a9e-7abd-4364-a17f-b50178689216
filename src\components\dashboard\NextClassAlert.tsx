import React from "react";
import { ScheduleEvent, UserRole } from "@/types";
import { Card, CardContent } from "@/components/ui/card";
import { CalendarClock, Clock, BookOpen } from "lucide-react";
import { format, parse } from "date-fns";
import { useNavigate } from "react-router-dom";
import { useUserRole } from "@/hooks/useUserRole";
import { formatInTimeZone } from "date-fns-tz";
import { convertUTCToLocalTime } from "@/utils/convertFromUTC";

interface NextClassAlertProps {
  nextClass: ScheduleEvent | undefined;
}

export const NextClassAlert: React.FC<NextClassAlertProps> = ({ nextClass }) => {
  const navigate = useNavigate();
  const { selectedRole, userTimezone } = useUserRole();
  const timezone = userTimezone || 'UTC';
  
  // If nextClass is undefined, return null
  if (!nextClass) return null;
  
  const handlePrepareForClass = () => {
    const route = selectedRole === UserRole.TEACHER 
      ? `/class/${nextClass.classroomId}`
      : `/class/${nextClass.classroomId}/student-manageclass/`;
    navigate(route);
  };
  

  
  return (
    <Card className="bg-gradient-to-br from-purple-100 to-indigo-100 border-none shadow-lg hover:shadow-xl transition-all duration-300">
      <CardContent className="p-4">
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
          <div className="flex items-start gap-4 flex-1">
            <div className="rounded-full bg-purple-500/20 p-3">
              <CalendarClock className="h-6 w-6 text-purple-600" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg text-purple-900 mb-1">Next Class</h3>
              <p className="font-medium text-purple-800 truncate">{nextClass.classroomName}</p>
              <div className="mt-2 flex items-start gap-2 text-sm text-purple-700">
                <Clock className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <span className="break-words">{convertUTCToLocalTime(nextClass.startDate,nextClass.sessionStartTime, timezone).date} {convertUTCToLocalTime(nextClass.startDate,nextClass.sessionStartTime, timezone).time } - {convertUTCToLocalTime(nextClass.startDate,nextClass.sessionEndTime, timezone).time }  ({userTimezone})</span>
              </div>
              {nextClass.status && (
                <p className="mt-2 text-sm text-purple-700">
                  Status: <span className={nextClass.status === "CANCELLED" ? "text-red-500" : "text-green-500"}>
                    {nextClass.status}
                  </span>
                </p>
              )}
            </div>
          </div>
          <button 
            onClick={handlePrepareForClass}
            className="flex items-center justify-center gap-1.5 bg-purple-500 hover:bg-purple-600 transition-colors text-white px-3 py-1.5 rounded-md text-sm font-medium w-full sm:w-auto sm:whitespace-nowrap"
          >
            <BookOpen className="h-4 w-4" />
            Prepare for Class
          </button>
        </div>
      </CardContent>
    </Card>
  );
};