import React, { useEffect, useRef } from 'react';
import { Bold, Italic, Underline, List, ListOrdered } from 'lucide-react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({ 
  value, 
  onChange, 
  placeholder = "Write your content here..." 
}) => {
  const editorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value || '';
    }
  }, [value]);

  const handleInput = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  const execCommand = (command: string) => {
    document.execCommand(command, false);
    editorRef.current?.focus();
    handleInput();
  };

  return (
    <div className="border rounded-md">
      <div className="flex items-center gap-1 p-2 border-b bg-muted/50">
        <button 
          type="button" 
          onClick={() => execCommand('bold')}
          className="p-2 rounded hover:bg-muted"
          title="Bold"
        >
          <Bold className="h-4 w-4" />
        </button>
        <button 
          type="button" 
          onClick={() => execCommand('italic')}
          className="p-2 rounded hover:bg-muted"
          title="Italic"
        >
          <Italic className="h-4 w-4" />
        </button>
        <button 
          type="button" 
          onClick={() => execCommand('underline')}
          className="p-2 rounded hover:bg-muted"
          title="Underline"
        >
          <Underline className="h-4 w-4" />
        </button>
        <div className="h-4 w-px bg-border mx-1" />
        <button 
          type="button" 
          onClick={() => execCommand('insertUnorderedList')}
          className="p-2 rounded hover:bg-muted"
          title="Bullet List"
        >
          <List className="h-4 w-4" />
        </button>
        <button 
          type="button" 
          onClick={() => execCommand('insertOrderedList')}
          className="p-2 rounded hover:bg-muted"
          title="Numbered List"
        >
          <ListOrdered className="h-4 w-4" />
        </button>
      </div>
      <div
        ref={editorRef}
        contentEditable
        className="p-3 min-h-[150px] focus:outline-none prose max-w-none"
        onInput={handleInput}
        data-placeholder={placeholder}
        style={{
          minHeight: '150px'
        }}
      />
      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
      `}</style>
    </div>
  );
};

export default RichTextEditor;
