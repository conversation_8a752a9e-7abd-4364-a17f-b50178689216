
import React from "react";
import { Card, CardContent } from "@/components/ui/card";

export const StatsCards: React.FC = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
      <Card>
        <CardContent className="p-4 sm:p-6">
          <h3 className="text-gray-500 mb-2 text-sm sm:text-base">Total Working Hours</h3>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-2xl sm:text-3xl font-bold">32h 42m</div>
              <div className="text-xs sm:text-sm text-green-600">↗️ 25% Increase</div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4 sm:p-6">
          <h3 className="text-gray-500 mb-2 text-sm sm:text-base">Total Students</h3>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-2xl sm:text-3xl font-bold">25.3K</div>
              <div className="text-xs sm:text-sm text-green-600">↗️ 80% Increase in 20 Days</div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4 sm:p-6">
          <h3 className="text-gray-500 mb-2 text-sm sm:text-base">Paid Students</h3>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-2xl sm:text-3xl font-bold">1,335</div>
              <div className="text-xs sm:text-sm text-green-600">✓ Paid Last Day</div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4 sm:p-6">
          <h3 className="text-gray-500 mb-2 text-sm sm:text-base">Unpaid Students</h3>
          <div className="flex justify-between items-center">
            <div>
              <div className="text-2xl sm:text-3xl font-bold">453</div>
              <div className="text-xs sm:text-sm text-red-600">↓ Pending</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
