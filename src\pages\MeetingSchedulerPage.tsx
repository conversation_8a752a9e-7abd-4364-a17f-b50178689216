
import React, { useState, useMemo, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { 
  ChevronLeft, 
  Search, 
  Filter,
  Calendar,
  Plus,
  Edit,
  Trash2,
  Bell
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useApp } from "@/context/AppContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { ParentTeacherMeeting } from "@/types";
import { getMeetingsByTeacher, createMeeting, deleteMeeting } from "@/services/meetingService";

// Mock parent data (in real app, this would come from your backend)
const mockParents = {
  "student1": { id: "parent1", name: "John Smith", email: "<EMAIL>" },
  "student2": { id: "parent2", name: "Jane Doe", email: "<EMAIL>" },
};

// Mock student data
const mockStudents = [
  { id: "student1", name: "Alex Smith", classId: "class1" },
  { id: "student2", name: "Emma Doe", classId: "class2" },
];

const MeetingSchedulerPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { classes } = useApp();
  const { toast } = useToast();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [selectedClassId, setSelectedClassId] = useState("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState("");
  const [meetings, setMeetings] = useState<ParentTeacherMeeting[]>([]);
  
  const teacherClasses = classes.filter(c => c.teacherId === user?.id);
  
  const [newMeeting, setNewMeeting] = useState({
    title: "",
    description: "",
    classId: "",
    date: new Date().toISOString().split('T')[0],
    studentId: "",
    parentId: "",
  });

  // Filter students based on selected class
  const filteredStudents = useMemo(() => {
    return mockStudents.filter(student => 
      newMeeting.classId ? student.classId === newMeeting.classId : true
    );
  }, [newMeeting.classId]);

  // Get parent info for selected student
  const selectedParent = selectedStudent ? mockParents[selectedStudent] : null;

  useEffect(() => {
    const fetchMeetings = async () => {
      const fetchedMeetings = await getMeetingsByTeacher(user?.id || "t1");
      setMeetings(fetchedMeetings);
    };
    fetchMeetings();
  }, [user?.id]);

  // Filter meetings based on search, status, and class
  const filteredMeetings = meetings.filter(meeting => {
    const matchesSearch = meeting.title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' 
      || (filterStatus === 'upcoming' && meeting.isActive)
      || (filterStatus === 'completed' && !meeting.isActive);
    const matchesClass = selectedClassId === 'all' || meeting.classId === selectedClassId;
    return matchesSearch && matchesStatus && matchesClass;
  });

  // Update the create meeting handler to use the new service
  const handleCreateMeeting = async () => {
    if (!newMeeting.title || !newMeeting.classId || !newMeeting.date || !selectedStudent) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }
    
    const defaultSlots = [
      {
        startTime: new Date(`${newMeeting.date}T09:00:00`),
        endTime: new Date(`${newMeeting.date}T09:30:00`)
      },
      {
        startTime: new Date(`${newMeeting.date}T10:00:00`),
        endTime: new Date(`${newMeeting.date}T10:30:00`)
      }
    ];
    
    const createdMeeting = await createMeeting({
      ...newMeeting,
      teacherId: user?.id || "t1",
      date: new Date(newMeeting.date),
      isActive: true,
    }, defaultSlots);
    
    setMeetings(prev => [...prev, createdMeeting]);
    setIsCreateDialogOpen(false);
    toast({
      title: "Meeting scheduled",
      description: "The meeting has been successfully scheduled.",
    });
    setNewMeeting({
      title: "",
      description: "",
      classId: "",
      date: new Date().toISOString().split('T')[0],
      studentId: "",
      parentId: "",
    });
    setSelectedStudent("");
  };

  // Update the delete meeting handler
  const handleDeleteMeeting = async (id: string) => {
    await deleteMeeting(id);
    setMeetings(prev => prev.filter(m => m.id !== id));
    toast({
      title: "Meeting deleted",
      description: "The meeting has been successfully deleted.",
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex-1 flex flex-col">
        <header className="bg-white p-4 flex justify-between items-center border-b border-gray-200 h-14 sticky top-0 z-10">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => navigate('/teacher-dashboard')}
              className="h-8 w-8"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-xl font-semibold">Meeting Scheduler</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setIsCreateDialogOpen(true)}
              className="bg-purple-600 hover:bg-purple-700 text-white flex items-center gap-1 relative z-10"
              size="sm"
            >
              <Plus className="h-4 w-4" /> 
              <span className="hidden sm:inline">Schedule Meeting</span>
              <span className="sm:hidden">New</span>
            </Button>
          </div>
        </header>

        <div className="p-4 sm:p-6">
          <div className="max-w-4xl mx-auto">
            <div className="grid gap-4 md:grid-cols-4 mb-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Total Meetings
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-purple-500 mr-2" />
                    <span className="text-2xl font-bold">{meetings.length}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Active Meetings
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-green-500 mr-2" />
                    <span className="text-2xl font-bold">
                      {meetings.filter(m => m.isActive).length}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Booked Slots
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-blue-500 mr-2" />
                    <span className="text-2xl font-bold">
                      {meetings.reduce((acc, meeting) => 
                        acc + meeting.slots.filter(slot => slot.isBooked).length, 0
                      )}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Available Slots
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-orange-500 mr-2" />
                    <span className="text-2xl font-bold">
                      {meetings.reduce((acc, meeting) => 
                        acc + meeting.slots.filter(slot => !slot.isBooked).length, 0
                      )}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="hidden sm:flex justify-between items-center mb-6">
              <div className="w-64">
                <Select value={selectedClassId} onValueChange={setSelectedClassId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by class" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Classes</SelectItem>
                    {teacherClasses.map((classItem) => (
                      <SelectItem key={classItem.id} value={classItem.id}>
                        {classItem.className}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="relative w-64">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                <Input 
                  className="edu-form-field pl-10"
                  placeholder="Search meetings" 
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="flex sm:hidden items-center justify-between mb-4">
              <div className="relative flex-1 max-w-[calc(100%-70px)]">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input 
                  className="edu-form-field  pl-10 h-10"
                  placeholder="Search" 
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon" className="ml-2 h-10 w-10">
                    <Filter className="h-4 w-4" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="bottom" className="h-[300px]">
                  <SheetHeader className="text-left">
                    <SheetTitle>Filter Meetings</SheetTitle>
                    <SheetDescription>
                      Select a class to filter the meetings
                    </SheetDescription>
                  </SheetHeader>
                  <div className="py-6">
                    <Select value={selectedClassId} onValueChange={setSelectedClassId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a class" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Classes</SelectItem>
                        {teacherClasses.map((classItem) => (
                          <SelectItem key={classItem.id} value={classItem.id}>
                            {classItem.className}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </SheetContent>
              </Sheet>
            </div>

            <div className="space-y-4">
              {filteredMeetings.length === 0 ? (
                <Card className="text-center py-8">
                  <CardContent>
                    <Bell className="h-10 w-10 sm:h-12 sm:w-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-700 mb-1">No meetings found</h3>
                    <p className="text-gray-500 mb-4">
                      {searchQuery || selectedClassId !== "all" 
                        ? "Try changing your search or filter criteria" 
                        : "Schedule your first meeting to get started"}
                    </p>
                    <Button
                      className="bg-purple-600 hover:bg-purple-700"
                      onClick={() => setIsCreateDialogOpen(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" /> Schedule Meeting
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                filteredMeetings.map((meeting) => {
                  const classInfo = classes.find(c => c.id === meeting.classId);
                  const availableSlots = meeting.slots.filter(slot => !slot.isBooked).length;
                  const totalSlots = meeting.slots.length;

                  return (
                    <Card key={meeting.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex justify-between items-start">
                          <span className="text-base sm:text-lg mr-2">{meeting.title}</span>
                          <div className="flex gap-1 sm:gap-2 flex-shrink-0">
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              onClick={() => navigate(`/meeting/${meeting.id}`)}
                              className="h-7 w-7 sm:h-8 sm:w-8"
                            >
                              <Edit className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              onClick={() => handleDeleteMeeting(meeting.id)}
                              className="h-7 w-7 sm:h-8 sm:w-8 text-red-600"
                            >
                              <Trash2 className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                            </Button>
                          </div>
                        </CardTitle>
                        <CardDescription>
                          <div className="text-xs sm:text-sm">
                            <span className="font-medium text-purple-600">{classInfo?.className}</span> • {format(meeting.date, "MMM d, yyyy")}
                          </div>
                          <div className="mt-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              meeting.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                            }`}>
                              {meeting.isActive ? 'Active' : 'Completed'}
                            </span>
                            <span className="text-sm text-gray-500 ml-2">
                              {availableSlots}/{totalSlots} slots available
                            </span>
                          </div>
                        </CardDescription>
                      </CardHeader>
                      {meeting.description && (
                        <CardContent>
                          <p className="text-sm text-gray-600">{meeting.description}</p>
                        </CardContent>
                      )}
                    </Card>
                  );
                })
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Create Meeting Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Schedule New Meeting</DialogTitle>
            <DialogDescription>
              Create a new parent-teacher meeting schedule
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="title">Title</label>
              <Input
                id="title"
                className="edu-form-field"
                value={newMeeting.title}
                onChange={(e) => setNewMeeting({...newMeeting, title: e.target.value})}
                placeholder="Meeting title"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="class">Select Class</label>
              <Select
                value={newMeeting.classId}
                onValueChange={(value) => setNewMeeting({...newMeeting, classId: value})}
              >
                <SelectTrigger className="edu-form-field ">
                  <SelectValue placeholder="Select a class" />
                </SelectTrigger>
                <SelectContent>
                  {teacherClasses.map((classItem) => (
                    <SelectItem key={classItem.id} value={classItem.id}>
                      {classItem.className}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label htmlFor="student">Select Student</label>
              <Select
                value={selectedStudent}
                onValueChange={(value) => {
                  setSelectedStudent(value);
                  if (mockParents[value]) {
                    setNewMeeting({
                      ...newMeeting,
                      studentId: value,
                      parentId: mockParents[value].id
                    });
                  }
                }}
              >
                <SelectTrigger className="edu-form-field ">
                  <SelectValue placeholder="Select a student" />
                </SelectTrigger>
                <SelectContent>
                  {filteredStudents.map((student) => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {selectedParent && (
              <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium">Parent Information</h4>
                <p>Name: {selectedParent.name}</p>
                <p>Email: {selectedParent.email}</p>
              </div>
            )}
            <div className="space-y-2">
              <label htmlFor="date">Date</label>
              <Input
                id="date"
                type="date"
                value={newMeeting.date}
                 className="edu-form-field"
                onChange={(e) => setNewMeeting({...newMeeting, date: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="description">Description (optional)</label>
              <Input
                id="description"
                value={newMeeting.description}
                className="edu-form-field "
                onChange={(e) => setNewMeeting({...newMeeting, description: e.target.value})}
                placeholder="Describe the meeting purpose"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>Cancel</Button>
            <Button
              className="bg-purple-600 hover:bg-purple-700"
              onClick={handleCreateMeeting}
            >
              Schedule Meeting
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MeetingSchedulerPage;
