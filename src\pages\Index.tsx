
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import Header from "@/components/layout/Header";
import { 
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  CalendarDays, 
  BookOpen, 
  CircleDollarSign, 
  MessageSquare, 
  Star, 
  Mail, 
  PhoneCall, 
  MapPin,
  Sparkles,
  Rocket,
  Users,
  Target,
  Trophy,
  GraduationCap,
  Heart,
   Users as UsersIcon
} from "lucide-react";
import { useState } from "react";
import { sendContactMessage } from "@/services/contactService";
import { toast } from "sonner";
import Footer from "@/components/Footer"; // or your preferred toast library
import { useAuth } from "react-oidc-context";
//import {handleSignUp Footer } from "react-day-picker";

// Import feature images
import smartLearningImg from "@/assets/smart-learning.jpg";
import easyCommunicationImg from "@/assets/easy-communication.jpg";
import trackProgressImg from "@/assets/track-progress.jpg";
import collaborationImg from "@/assets/collaboration.jpg";
import studentParentExcitementImg from "@/assets/student-parent-excitement.jpg";
import teacherExcitementImg from "@/assets/teacher-excitement.jpg";

export default function Index() {
  const blogPosts = [
    {
      id: 1,
      title: "10 Effective Strategies for Online Teaching",
      excerpt: "Discover the most effective strategies to engage students in virtual learning environments...",
      date: "April 4, 2025",
      category: "Teaching Tips",
      imageUrl: "https://images.unsplash.com/photo-1610484826967-09c5720778c7?q=80&w=2070&auto=format&fit=crop",
    },
    {
      id: 2,
      title: "How to Keep Students Engaged in Remote Learning",
      excerpt: "Learn practical approaches to maintain student engagement and motivation during remote learning...",
      date: "April 1, 2025",
      category: "Student Engagement",
      imageUrl: "https://images.unsplash.com/photo-1577896851231-70ef18881754?q=80&w=2070&auto=format&fit=crop",
    },
    {
      id: 3,
      title: "Building Effective Parent-Teacher Communication",
      excerpt: "Explore strategies to create strong partnerships with parents for student success...",
      date: "March 28, 2025",
      category: "Communication",
      imageUrl: "https://images.unsplash.com/photo-1560066984-138dadb4c035?q=80&w=2074&auto=format&fit=crop",
    }
  ];
const [contactForm, setContactForm] = useState({ name: "", email: "", message: "" });
  const [isSending, setIsSending] = useState(false);
const handleContactChange = (e) => {
    setContactForm({ ...contactForm, [e.target.id]: e.target.value });
  };
    const auth = useAuth();
    const [isSubmitting, setIsSubmitting] = useState(false);

const handleSignIn = async () => {
    setIsSubmitting(true);
    try {
      await auth.signinRedirect();
    } catch (error: any) {
      toast.error(`Authentication failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };
  const handleContactSubmit = async (e) => {
    e.preventDefault();
    
    if (!contactForm.name || !contactForm.email || !contactForm.message) {
      toast.error("Please fill in all fields");
      return;
    }
    
    setIsSending(true);
    try {
      await sendContactMessage(contactForm);
      toast.success("Message sent successfully!");
      setContactForm({ name: "", email: "", message: "" });
    } catch (error) {
      toast.error("Failed to send message.");
    } finally {
      setIsSending(false);
    }
  };
 
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
        <main className="flex-grow">
        {/* Hero Section with gradient background */}
        <section className="py-16 md:py-24 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-blue-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto relative z-10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
              <div className="max-w-2xl text-center md:text-left">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white leading-tight">
                  Transform <span className="text-yellow-300">Education</span> Through Innovation
                </h1>
                <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
                  Join thousands of educators and students in revolutionizing the learning experience
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <Button asChild size="lg" className="w-full sm:w-auto bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full shadow-lg">
                    <Link to="/register">Get Started Free</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="w-full sm:w-auto border-2 bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full">
                    <Link to="/subscription">View Pricing</Link>
                  </Button>
                </div>
                <div className="mt-12 flex flex-col sm:flex-row items-center justify-center md:justify-start gap-6">
                  <div className="flex -space-x-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="w-12 h-12 rounded-full border-4 border-purple-600 bg-white shadow-lg flex items-center justify-center text-purple-600 font-bold text-lg">
                        {String.fromCharCode(64 + i)}
                      </div>
                    ))}
                  </div>
                  <p className="text-lg text-white/90">
                    Joined by <span className="font-bold text-yellow-300">2000+</span> educators
                  </p>
                </div>
              </div>
              <div className="w-full md:w-2/5 relative">
                <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-transform duration-500">
                  <img 
                    src="https://images.unsplash.com/photo-1571260899304-425eee4c7efc?q=80&w=2070&auto=format&fit=crop" 
                    alt="EduConnect Platform" 
                    className="w-full h-64 md:h-96 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800">Modern Learning Experience</h3>
                    <p className="text-gray-600">Interactive tools for better engagement</p>
                  </div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-xl shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <p className="text-lg font-bold flex items-center gap-2">
                    <Sparkles className="h-5 w-5" />
                    AI-Powered Learning
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>



        {/* Features Section with gradient cards */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-blue-500 bg-clip-text text-transparent">
                Powerful Features for Modern Education
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Everything you need to create an engaging and effective educational environment
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
              <Card className="bg-gradient-to-br from-purple-50 to-white hover:shadow-xl transition-all duration-300 border-0 overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="relative w-full h-32 mb-4 rounded-xl overflow-hidden">
                    <img 
                      src={smartLearningImg} 
                      alt="Smart Learning" 
                      className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-purple-500/20 to-transparent"></div>
                  </div>
                  <CardTitle className="text-2xl">Smart Learning</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Intuitive tools designed for modern education
                  </p>
                </CardContent>
              </Card>
              
              <Card className="bg-gradient-to-br from-blue-50 to-white hover:shadow-xl transition-all duration-300 border-0 overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="relative w-full h-32 mb-4 rounded-xl overflow-hidden">
                    <img 
                      src={easyCommunicationImg} 
                      alt="Easy Communication" 
                      className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-blue-500/20 to-transparent"></div>
                  </div>
                  <CardTitle className="text-2xl">Easy Communication</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Connect seamlessly with students and parents
                  </p>
                </CardContent>
              </Card>
              
              <Card className="bg-gradient-to-br from-cyan-50 to-white hover:shadow-xl transition-all duration-300 border-0 overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="relative w-full h-32 mb-4 rounded-xl overflow-hidden">
                    <img 
                      src={trackProgressImg} 
                      alt="Track Progress" 
                      className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-cyan-500/20 to-transparent"></div>
                  </div>
                  <CardTitle className="text-2xl">Track Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Monitor and analyze student performance
                  </p>
                </CardContent>
              </Card>
              
              <Card className="bg-gradient-to-br from-teal-50 to-white hover:shadow-xl transition-all duration-300 border-0 overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="relative w-full h-32 mb-4 rounded-xl overflow-hidden">
                    <img 
                      src={collaborationImg} 
                      alt="Collaboration" 
                      className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-teal-500/20 to-transparent"></div>
                  </div>
                  <CardTitle className="text-2xl">Collaboration</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Foster teamwork and interactive learning
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Teachers Excitement Section */}
        <section className="py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                Empower Your Teaching Like Never Before!
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Join thousands of educators who are revolutionizing their classrooms with EduConnect
              </p>
            </div>
            
            <div className="flex flex-col lg:flex-row items-center gap-12">
              <div className="lg:w-1/2 space-y-8">
                <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 hover:scale-105">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="bg-gradient-to-r from-blue-500 to-indigo-500 p-3 rounded-full">
                      <Rocket className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800">Streamline Your Workflow</h3>
                  </div>
                  <ul className="space-y-3 text-gray-600">
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      Manage all classes from one dashboard
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse"></div>
                      Automate attendance and grade tracking
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                      Create and distribute assignments instantly
                    </li>
                  </ul>
                </div>
                
                <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 hover:scale-105">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="bg-gradient-to-r from-indigo-500 to-purple-500 p-3 rounded-full">
                      <UsersIcon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800">Enhanced Communication</h3>
                  </div>
                  <ul className="space-y-3 text-gray-600">
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse"></div>
                      Instant messaging with students and parents
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                      Broadcast announcements to entire classes
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-pink-500 rounded-full animate-pulse"></div>
                      Schedule parent-teacher meetings effortlessly
                    </li>
                  </ul>
                </div>
                
                <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-6 text-white shadow-lg animate-fade-in">
                  <div className="flex items-center gap-4 mb-4">
                    <Trophy className="h-8 w-8 text-yellow-300" />
                    <h3 className="text-2xl font-bold">Teacher Success Story</h3>
                  </div>
                  <p className="text-lg opacity-90 mb-4">
                    "EduConnect saved me 15 hours per week! I can focus on what matters most - teaching."
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="font-semibold">- Michael Chen, Math Teacher</span>
                    <div className="flex gap-1">
                      {[1, 2, 3, 4, 5].map((i) => (
                        <Star key={i} className="h-4 w-4 text-yellow-300 fill-yellow-300" />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="lg:w-1/2">
                <img 
                  src={teacherExcitementImg} 
                  alt="Excited teachers with modern educational technology" 
                  className="w-full h-auto rounded-3xl shadow-2xl transform hover:scale-105 transition-transform duration-500"
                />
              </div>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8 mt-16">
              <div className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CalendarDays className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-gray-800 mb-2">Smart Scheduling</h4>
                <p className="text-gray-600">AI-powered class scheduling that adapts to your preferences</p>
              </div>
              
              <div className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="bg-gradient-to-r from-indigo-500 to-purple-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CircleDollarSign className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-gray-800 mb-2">Payment Management</h4>
                <p className="text-gray-600">Secure and automated fee collection with detailed reports</p>
              </div>
              
              <div className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-gray-800 mb-2">AI-Powered Tools</h4>
                <p className="text-gray-600">Generate quizzes, grade assignments, and get teaching insights</p>
              </div>
            </div>
            
            <div className="text-center mt-16">
              <div className="bg-white rounded-2xl p-8 max-w-lg mx-auto shadow-lg">
                <div className="flex items-center justify-center gap-4 mb-6">
                  <div className="text-4xl">🎯</div>
                  <div>
                    <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      95% Teacher Satisfaction
                    </div>
                    <p className="text-gray-600">Based on 5,000+ teacher reviews</p>
                  </div>
                </div>
                <Button size="lg" asChild 
                onClick={handleSignIn}
                                     className="bg-purple-600 hover:bg-purple-700 ml-auto flex items-center gap-2 rounded-full px-8"
>
                  <Link to="/">Start Teaching Smarter Today!</Link>
                </Button>
                <p className="text-sm text-gray-500 mt-4">
                  ✨ 14-day free trial • No credit card required
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Students & Parents Excitement Section */}
        <section className="py-20 bg-gradient-to-br from-yellow-50 via-pink-50 to-orange-50 relative overflow-hidden">
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-orange-600 via-pink-500 to-purple-600 bg-clip-text text-transparent">
                Made for Students & Parents Who Dream Big!
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Join thousands of families who are transforming their educational journey with EduConnect
              </p>
            </div>
            
            <div className="flex flex-col lg:flex-row items-center gap-12">
              <div className="lg:w-1/2">
                <img 
                  src={studentParentExcitementImg} 
                  alt="Happy students and parents celebrating together" 
                  className="w-full h-auto rounded-3xl shadow-2xl transform hover:scale-105 transition-transform duration-500"
                />
              </div>
              
              <div className="lg:w-1/2 space-y-8">
                <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 transform hover:-translate-y-2">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-3 rounded-full">
                      <GraduationCap className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800">For Students</h3>
                  </div>
                  <ul className="space-y-3 text-gray-600">
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Access all your courses in one place
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      Track your progress and achievements
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                      Connect with classmates and teachers
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      Submit assignments easily
                    </li>
                  </ul>
                </div>
                
                <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 transform hover:-translate-y-2">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="bg-gradient-to-r from-pink-500 to-orange-500 p-3 rounded-full">
                      <Heart className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800">For Parents</h3>
                  </div>
                  <ul className="space-y-3 text-gray-600">
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                      Stay connected with your child's education
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      Receive real-time updates and notifications
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      Communicate directly with teachers
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      Manage payments and fees securely
                    </li>
                  </ul>
                </div>
                
                <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-6 text-white shadow-lg">
                  <div className="flex items-center gap-4 mb-4">
                    <Trophy className="h-8 w-8 text-yellow-300" />
                    <h3 className="text-2xl font-bold">Success Stories</h3>
                  </div>
                  <p className="text-lg opacity-90 mb-4">
                    "My daughter's grades improved by 40% since we started using EduConnect!"
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="font-semibold">- Sarah Johnson, Parent</span>
                    <div className="flex gap-1">
                      {[1, 2, 3, 4, 5].map((i) => (
                        <Star key={i} className="h-4 w-4 text-yellow-300 fill-yellow-300" />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="text-center mt-16">
              <div className="bg-white rounded-2xl p-8 max-w-md mx-auto shadow-lg">
                <div className="flex items-center justify-center gap-2 mb-4">
                  <UsersIcon className="h-8 w-8 text-purple-600" />
                  <span className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    10,000+
                  </span>
                </div>
                <p className="text-gray-600 mb-6">Happy families already using EduConnect</p>
                <Button size="lg" onClick={handleSignIn} asChild className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-lg py-6 rounded-full shadow-lg transform hover:scale-105 transition-all duration-300">
                  <Link to="/">Start Your Journey Today!</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        <section className="py-12 md:py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-8 md:mb-12">
              <h2 className="text-2xl md:text-4xl font-bold mb-3 md:mb-4">What Our Users Say</h2>
              <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto">
                Hear from educators, students, and parents who are transforming education with EduConnect.
              </p>
            </div>
            
            <div className="max-w-5xl mx-auto px-4 md:px-10">
              <Carousel className="w-full">
                <CarouselContent>
                  <CarouselItem>
                    <div className="edu-card p-8 text-center">
                      <div className="flex justify-center mb-6">
                        {[1, 2, 3, 4, 5].map((i) => (
                          <Star key={i} className="h-6 w-6 text-edu-orange fill-edu-orange" />
                        ))}
                      </div>
                      <p className="italic text-xl text-gray-600 mb-8">
                        "EduConnect has completely transformed how I manage my classroom. The communication tools are intuitive, and the class management features save me hours every week. It's the most comprehensive platform I've used in my 15 years of teaching."
                      </p>
                      <div className="flex flex-col items-center">
                        <div className="w-16 h-16 rounded-full bg-edu-blue flex items-center justify-center text-white font-bold text-xl mb-4">
                          MS
                        </div>
                        <div>
                          <h4 className="font-semibold text-lg">Maria Smith</h4>
                          <p className="text-gray-500">High School Math Teacher</p>
                        </div>
                      </div>
                    </div>
                  </CarouselItem>
                  
                  <CarouselItem>
                    <div className="edu-card p-8 text-center">
                      <div className="flex justify-center mb-6">
                        {[1, 2, 3, 4, 5].map((i) => (
                          <Star key={i} className="h-6 w-6 text-edu-orange fill-edu-orange" />
                        ))}
                      </div>
                      <p className="italic text-xl text-gray-600 mb-8">
                        "As a parent, I love being able to easily stay informed about my child's education. The payment system is also very convenient for managing school fees. EduConnect has made parent-teacher communication so much more efficient."
                      </p>
                      <div className="flex flex-col items-center">
                        <div className="w-16 h-16 rounded-full bg-edu-orange flex items-center justify-center text-white font-bold text-xl mb-4">
                          JD
                        </div>
                        <div>
                          <h4 className="font-semibold text-lg">James Davis</h4>
                          <p className="text-gray-500">Parent of Two Students</p>
                        </div>
                      </div>
                    </div>
                  </CarouselItem>
                  
                  <CarouselItem>
                    <div className="edu-card p-8 text-center">
                      <div className="flex justify-center mb-6">
                        {[1, 2, 3, 4, 5].map((i) => (
                          <Star key={i} className="h-6 w-6 text-edu-orange fill-edu-orange" />
                        ))}
                      </div>
                      <p className="italic text-xl text-gray-600 mb-8">
                        "EduConnect makes it easy to keep track of all my classes, assignments, and communications with teachers. I can access everything in one place, which helps me stay organized and on top of my studies."
                      </p>
                      <div className="flex flex-col items-center">
                        <div className="w-16 h-16 rounded-full bg-edu-green flex items-center justify-center text-white font-bold text-xl mb-4">
                          AP
                        </div>
                        <div>
                          <h4 className="font-semibold text-lg">Alex Parker</h4>
                          <p className="text-gray-500">High School Student</p>
                        </div>
                      </div>
                    </div>
                  </CarouselItem>
                </CarouselContent>
                <CarouselPrevious className="left-2" />
                <CarouselNext className="right-2" />
              </Carousel>
            </div>
          </div>
        </section>

        <section className="py-12 md:py-20 bg-gradient-to-b from-white to-edu-light">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-center mb-8 md:mb-12">
              <div className="text-center md:text-left mb-4 md:mb-0">
                <h2 className="text-2xl md:text-4xl font-bold mb-2">Latest from our Blog</h2>
                <p className="text-base md:text-lg text-gray-600">
                  Educational insights, tips, and resources for teachers and parents
                </p>
              </div>
              <Button asChild variant="outline" className="w-full md:w-auto border-edu-blue text-edu-blue hover:bg-edu-blue hover:text-white">
                <Link to="/blog">View All Articles</Link>
              </Button>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-6">
              {blogPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <div className="h-48 overflow-hidden">
                    <img 
                      src={post.imageUrl} 
                      alt={post.title} 
                      className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                    />
                  </div>
                  <CardHeader>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-edu-blue font-medium">{post.category}</span>
                      <span className="text-sm text-gray-500">{post.date}</span>
                    </div>
                    <CardTitle className="text-xl hover:text-edu-blue transition-colors">
                      <Link to={`/blog/${post.id}`}>{post.title}</Link>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">{post.excerpt}</p>
                  </CardContent>
                  <CardFooter>
                    <Button variant="ghost" asChild className="px-0 text-edu-blue hover:text-edu-blue hover:bg-transparent">
                      <Link to={`/blog/${post.id}`}>Read More →</Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </div>
        </section>

        <section className="py-12 md:py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-8 md:mb-12">
              <h2 className="text-2xl md:text-4xl font-bold mb-3 md:mb-4">Get in Touch</h2>
              <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto">
                Have questions? We're here to help. Send us a message and we'll respond as soon as possible.
              </p>
            </div>

            <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="bg-edu-blue/10 p-3 rounded-full">
                    <Mail className="h-6 w-6 text-edu-blue" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Email Us</h3>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="bg-edu-blue/10 p-3 rounded-full">
                    <PhoneCall className="h-6 w-6 text-edu-blue" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Call Us</h3>
                    <p className="text-gray-600">+****************</p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="bg-edu-blue/10 p-3 rounded-full">
                    <MapPin className="h-6 w-6 text-edu-blue" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Visit Us</h3>
                    <p className="text-gray-600">123 Education Street<br />Learning City, ED 12345</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-lg p-4 md:p-6">
                <form className="space-y-4">
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input id="name" placeholder="Your name" />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" placeholder="<EMAIL>" />
                  </div>
                  <div>
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      placeholder="How can we help you?"
                      className="h-32"
                    />
                  </div>
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">
                    Send Message
                  </Button>
                </form>
              </div>
            </div>
          </div>
        </section>

        <section className="py-12 md:py-20 bg-edu-dark text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-pattern opacity-10"></div>
          <div className="container mx-auto px-4 text-center relative z-10">
            <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6 max-w-3xl mx-auto leading-tight">
              Ready to Transform Your Educational Experience?
            </h2>
            <p className="text-lg md:text-xl text-gray-300 mb-6 md:mb-10 max-w-2xl mx-auto">
              Join thousands of educators, students, and parents on our platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" onClick={handleSignIn}                       className="bg-purple-600 hover:bg-purple-700 ml-auto flex items-center gap-2 rounded-full px-8"
>
               Get Started
              </Button>
              <Button asChild size="lg"  className="w-full sm:w-auto bg-purple-600 hover:bg-purple-700 text-base md:text-lg">
                <Link to="/subscription">View Pricing Plans</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>


           <footer className="bg-gray-900 text-white py-8 md:py-12">
        <Footer/>
      </footer>
              
    </div>
  );
}
