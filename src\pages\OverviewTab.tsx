import React, { useState,useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle2, Edit, Bell } from "lucide-react";
import { toast } from "sonner";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import {createClassNotes,getClassNotes,updateClassNotes,MarkClassAsComplete,
    getClassOverViewForTeacher
} from "@/services/classService";
import { useAuth } from "react-oidc-context"; // Updated import
import { useApp } from "@/context/AppContext";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

const OverviewTab = ({ classId,   currentClass, onComplete }: any) => {
     const [classNotes, setClassNotes] = useState("Welcome to our class! Here are some important notes for students...");
      const [classNotesId , setClassNotesId] = useState("");
      const [isEditingNotes, setIsEditingNotes] = useState(false);
      const [notesExist, setNotesExist] = useState(false);
      const auth = useAuth();
      const [showCompleteDialog, setShowCompleteDialog] = useState(false);
      const [overView, setOverView] = useState<any>(null);
      const [overViewLoading, setOverViewLoading] = useState(false);
        const [isCompleted ,setIsCompleted] = useState(false);
        const [marking, setMarking] = useState(false);
        const { updateClass } = useApp();
      const fetchOverview = async () => {
            if (!auth.user?.access_token || !classId) return;
            setOverViewLoading(true);
            try {
              const data = await getClassOverViewForTeacher(classId,auth.user.access_token);
              setOverView(data);
            } catch (error) {
              toast.error("Failed to load payment history");
            } finally {
              setOverViewLoading(false);
            }
          };
            const handleMarkComplete = async () => {
                        try {
                                                setMarking(true);
                                                await MarkClassAsComplete(auth.user.access_token, classId);
                                                setIsCompleted(true);
                                                setShowCompleteDialog(false);
                                                toast.success("Class marked as complete. No further modifications can be made.");
                                                
                                                // Update currentClass.completed in global context
                                                updateClass(classId, { ...(currentClass || {}), completed: true });

                                                // Notify parent to refresh all tabs
                                                if (typeof onComplete === 'function') onComplete();
                                                } catch (error) {
                                                toast.error("Failed to mark class as complete");
                                            } finally {
                                                setMarking(false);
                                            }
                    };
          
          const fetchClassNotes = async () => {
            if (!auth.user?.access_token || !classId) return;   
            try {
              const data = await getClassNotes(auth.user.access_token, classId);
              if (data && data.length > 0) {
                setClassNotes(data[0].content);
      
                setClassNotesId(data[0].id);
                setNotesExist(true);
              }
            } catch (error) {
              setNotesExist(false);
            }
          };  
        useEffect(() => {
            if (!auth.user?.access_token || !classId) return;
            fetchOverview();
            fetchClassNotes();
        }, [auth.user?.access_token, classId]);

        // Initialize isCompleted state based on currentClass.completed
        useEffect(() => {
            if (currentClass?.completed) {
                setIsCompleted(true);
            }
        }, [currentClass?.completed]);
        
           
  return (
    <div className="max-w-6xl mx-auto">
    <div className="bg-gray-50 -m-4 md:-m-6 p-4 md:p-6 min-h-[400px]">
    {/* Stats Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4 mb-6">
    {/* Completion Status */}
    <Card className="overflow-hidden">
        <CardContent className="p-6">
        <div className="text-center">
            <div className="relative w-20 h-20 mx-auto mb-4">
            <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                <path
                d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="hsl(var(--muted))"
                strokeWidth="2"
                />
                <path
                d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="hsl(var(--primary))"
                strokeWidth="2"
                strokeDasharray={`${Math.round(overView?.completionStatus?.completionPercentage || 0)}, 100`}
                />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold text-primary">{Math.round(overView?.completionStatus?.completionPercentage || 0)}%</span>
            </div>
            </div>
            <h3 className="font-semibold mb-1">Completion Status</h3>
            <div className="text-sm text-muted-foreground space-y-1">
            <div>{overView?.completionStatus?.completedSchedules || 0}/{overView?.completionStatus?.totalSchedules || 0} Completed</div>
            <div>{overView?.completionStatus?.pendingSchedules || 0} Pending</div>
            </div>
        </div>
        </CardContent>
    </Card>

    {/* Attendance */}
    <Card>
        <CardContent className="p-6">
        <div className="text-center">
            <div className="relative w-20 h-20 mx-auto mb-4">
            <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                <path
                d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="hsl(var(--muted))"
                strokeWidth="2"
                />
                <path
                d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="hsl(var(--chart-2))"
                strokeWidth="2"
                strokeDasharray={`${Math.round(overView?.attendanceStatistics?.overallPresencePercentage || 0)}, 100`}
                />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold text-orange-500">{Math.round(overView?.attendanceStatistics?.overallPresencePercentage || 0)}%</span>
            </div>
            </div>
            <h3 className="font-semibold mb-1">Attendance</h3>
            <div className="text-sm text-muted-foreground space-y-1">
            <div>{Math.round(overView?.attendanceStatistics?.overallPresencePercentage || 0)}% Present</div>
            <div>{Math.round(overView?.attendanceStatistics?.overallAbsencePercentage || 100)}% Absent</div>
            </div>
        </div>
        </CardContent>
    </Card>

    {/* Assignment */}
    <Card>
        <CardContent className="p-6">
        <div className="text-center">
            <div className="relative w-20 h-20 mx-auto mb-4">
            <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                <path
                d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="hsl(var(--muted))"
                strokeWidth="2"
                />
                <path
                d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="hsl(var(--destructive))"
                strokeWidth="2"
                strokeDasharray={`${Math.round(overView?.assignmentStatistics?.averageSubmissionRate || 0)}, 100`}
                />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold text-red-500">{Math.round(overView?.assignmentStatistics?.averageSubmissionRate || 0)}%</span>
            </div>
            </div>
            <h3 className="font-semibold mb-1">Assignment</h3>
            <div className="text-sm text-muted-foreground space-y-1">
            <div>{overView?.assignmentStatistics?.totalSubmissions || 0} Submitted</div>
            <div>{overView?.assignmentStatistics?.publishedAssignments || 0} Published</div>
            </div>
        </div>
        </CardContent>
    </Card>

    {/* Payments */}
    <Card>
        <CardContent className="p-6">
        <div className="text-center">
            <div className="relative w-20 h-20 mx-auto mb-4">
            <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                <path
                d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="hsl(var(--muted))"
                strokeWidth="2"
                />
                <path
                d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="hsl(var(--chart-3))"
                strokeWidth="2"
                strokeDasharray={`${Math.round(overView?.paymentStatistics?.paymentCompletedPercentage || 0)}, 100`}
                />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold text-green-500">{Math.round(overView?.paymentStatistics?.paymentCompletedPercentage || 0)}%</span>
            </div>
            </div>
            <h3 className="font-semibold mb-1">Payments</h3>
            <div className="text-sm text-muted-foreground space-y-1">
            <div>{overView?.paymentStatistics?.studentsWithCompletedPayment || 0} Paid</div>
            <div>{overView?.paymentStatistics?.studentsWithPendingPayment || 0} Pending</div>
            </div>
        </div>
        </CardContent>
    </Card>
    </div>
    {/* Mark Complete Button */}
        <div className="flex justify-end mb-6">
                <Button
                    disabled={isCompleted || currentClass?.completed || marking}
                    onClick={() => setShowCompleteDialog(true)}
                    className="bg-green-600 hover:bg-green-700"
                >
                    <CheckCircle2 className="h-4 w-4 mr-2" />
                    Mark Class as Complete
                </Button>
        </div>

    {/* Completion Status Alert */}
    {isCompleted && (
    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center text-green-800">
        <CheckCircle2 className="h-5 w-5 mr-3" />
        <div>
            <p className="font-medium">This class has been marked as complete</p>
            <p className="text-sm text-green-700 mt-1">
            Schedule creation, fee updates, and syllabus modifications are now disabled.
            </p>
        </div>
        </div>
    </div>
    )}

    {/* Main Content Area */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
    {/* Class Notes - Left Side */}
    <div className="lg:col-span-2">
        <Card>
        <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Class Notes</h3>
            <Button
                variant="ghost"
                size={isEditingNotes ? "default" : "icon"}
                onClick={async () => {
                if (isEditingNotes) {
                    try {
                    // const plainText = classNotes.replace(/<[^>]*>/g, '');
                    if (notesExist) {
                        await updateClassNotes(auth.user.access_token, classId, classNotes,classNotesId);
                    } else {
                        await createClassNotes(auth.user.access_token, classId, classNotes);
                        setNotesExist(true);
                    }
                    toast.success("Class notes saved successfully!");
                    } catch (error) {
                    toast.error("Failed to save class notes");
                    }
                }
                setIsEditingNotes(!isEditingNotes);
                }}
            >
                {isEditingNotes ? (
                'Save'
                ) : (
                <Edit className="h-4 w-4" />
                )}
            </Button>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <div className="flex items-center text-blue-800 text-sm">
                <Bell className="h-4 w-4 mr-2" />
                <span className="font-medium">Note: These notes are visible to all students in this class.</span>
            </div>
            </div>
            {isEditingNotes ? (
            <RichTextEditor
                value={classNotes}
                onChange={setClassNotes}
                placeholder="Add important notes for your students..."
            />
            ) : (
                <div 
        className="prose prose-sm max-w-none [&_ul]:list-disc [&_ul]:ml-6 [&_ol]:list-decimal [&_ol]:ml-6 [&_li]:mb-1 "

                    dangerouslySetInnerHTML={{ 
            __html:  classNotes
        }}
        />
    
            
            )}
        </CardContent>
        </Card>
    </div>

    {/* Daily Activities - Right Side */}
    <div>
        <Card>
        <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">Daily Activities</h3>
            <div className="space-y-4">
            <div>
                <h4 className="font-medium mb-2">• Topic Name 1</h4>
                <p className="text-sm text-muted-foreground">
                Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text.
                </p>
            </div>
            <div>
                <h4 className="font-medium mb-2">• Topic Name 2</h4>
                <p className="text-sm text-muted-foreground">
                Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text.
                </p>
            </div>
            <div>
                <h4 className="font-medium mb-2">• Topic Name 3</h4>
                <p className="text-sm text-muted-foreground">
                Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text.
                </p>
            </div>
            </div>
        </CardContent>
        </Card>
    </div>
    </div>
    </div>
    

 {/* Mark Complete Confirmation Dialog */}
      <AlertDialog open={showCompleteDialog} onOpenChange={setShowCompleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Mark Class as Complete?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will mark the class as complete. Once marked as complete:
              <ul className="list-disc pl-6 mt-2 space-y-1">
                <li>No new schedules can be created</li>
                <li>Fee structures cannot be added or modified</li>
                <li>Syllabus topics cannot be added, edited, or deleted</li>
              </ul>
              <p className="mt-3 font-medium">This action cannot be undone. Are you sure you want to proceed?</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleMarkComplete} className="bg-green-600 hover:bg-green-700">
              Mark as Complete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
export default OverviewTab;