import React, { useState, useEffect, useRef } from "react";
import { useApp } from "@/context/AppContext";
import { Link, useNavigate } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

import { 
  BookOpen, 
  Clock, 
  Bell, 
  User, 
  Search,
  Menu,
  ArrowLeft
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { fileUpload, downloadAndCreateImageUrl } from "@/utils/fileUpload";

import { useUserRole } from "@/hooks/useUserRole";
import { toast } from "sonner"; 
import { useDispatch } from "react-redux";
import { clearUserData } from '@/redux/userSlice';

import { ClassData, getClassesForStudent } from "@/services/classService";
import class1Img from "@/assets/smart-learning.jpg";
import {getProfile} from "@/services/profileService";

import { generateAvatarUrl } from "@/lib/utils";
import { useAuth } from "react-oidc-context"; 
import { UserRole } from "@/types";
export default function StudentClassesPage() {
 
  const { classes } = useApp();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState("");
  const [studentClasses, setStudentClasses] = useState<ClassData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const auth = useAuth();
   const { selectedRole } = useUserRole();  
 const handleSwitchRoleClick=()=>{
    navigate('/role-selection');
  };
     const dispatch = useDispatch();
   const [profileData, setProfileData] = useState({
       name: '',
       phoneno: ''
     });
     const fetchProfile = async () => {
         try {
           const fetchedProfile = await getProfile(auth.user?.access_token);
           console.log('Fetched profile:', fetchedProfile); // Debug log
           if (fetchedProfile) {
             setProfileData({
               name: fetchedProfile.name || '',
               phoneno: fetchedProfile.phoneNumber  || ''
             });
           }
         } catch (error) {
           console.error('Error fetching profile:', error);
         }
       };
       useEffect(() => {
         fetchProfile();
       }, []);
     // Get user from OIDC
     const user = auth.isAuthenticated ? {
       id: auth.user?.profile.sub || "",
       name: profileData.name  || "User",
       email: auth.user?.profile.email || "",
       role: (auth.user?.profile["custom:role"] as UserRole) ,
       avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
     } : null;
  const hasFetched = useRef(false);

  useEffect(() => {
    if (hasFetched.current) return;
    
    const fetchClasses = async () => {
      hasFetched.current = true;
      setIsLoading(true);
      try {
        const allClasses = await getClassesForStudent(auth.user.access_token);
         const classesWithUser = await Promise.all(
                  (Array.isArray(allClasses) ? allClasses : allClasses || []).map(async (classItem: ClassData) => {
                    let imageUrl = classItem.image || class1Img;
                    
                    // If class has posterFileId, download the image
                    if (classItem.posterFileId) {
                      const downloadedImageUrl = await downloadAndCreateImageUrl(auth.user.access_token, classItem.posterFileId);
                      if (downloadedImageUrl) {
                        imageUrl = downloadedImageUrl;
                      }
                    }
                    
                    return {
                      ...classItem,
                      image: imageUrl
                    };
                  })
                );
              
      //  const userClasses = allClasses.filter((_, index) => index < 4);
        setStudentClasses(classesWithUser || []);
      } catch (error) {
        console.error("Error fetching classes:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchClasses();
  }, [user?.id]);
  
  const handleBackToDashboard = () => {
    navigate('/student-dashboard');
  };

  const filteredClasses = searchTerm 
    ? studentClasses.filter(c => 
        c.className.toLowerCase().includes(searchTerm.toLowerCase()) || 
        c.subjectName.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : studentClasses;

  const [editImageClassId, setEditImageClassId] = useState<string | null>(null);

   const handleImageSelect  = async (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file || !editImageClassId) return;
  
      try {
        // Upload the image file
        const uploaded = await fileUpload(auth.user?.access_token, [file]);
        // Assuming the API returns the file URL as 'url'
        const imageUrl = uploaded[0]?.url;
  
        if (imageUrl) {
          // Update the class image in your state or backend as needed
          setStudentClasses(prev =>
            prev.map(cls =>
              cls.id === editImageClassId ? { ...cls, image: imageUrl } : cls
            )
          );
          // Optionally, call an API to update the class image on the backend here
        }
        setEditImageClassId(null); // Close dialog after upload
      } catch (error) {
        // Handle error (show toast, etc.)
        console.error("Image upload failed:", error);
      }
    };
    const handleLogout = async () => {
            await auth.removeUser();
            dispatch(clearUserData()); // Clears user data from Redux
            window.location.href = window.location.origin + "/logout";
          };
   const handleProfileClick = () => {
    navigate('/profile');
  };
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex-grow">
        <header className="bg-white p-4 md:p-6 flex justify-between items-center border-b border-gray-200">
          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              className="p-2 h-9 w-9 md:h-10 md:w-10 rounded-full" 
              onClick={handleBackToDashboard}
            >
              <ArrowLeft className="h-4 w-4 md:h-5 md:w-5" />
            </Button>
            <h1 className="text-xl md:text-2xl font-semibold">My Classes</h1>
          </div>
          <div className="flex items-center gap-2 md:gap-4">
            {isMobile ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon" className="h-9 w-9">
                    <Menu className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => navigate('/join-class')}>
                    Join Class
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/announcements')}>
                    Announcements
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/profile')}>
                    Profile
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <>
                <Button className="bg-purple-600 hover:bg-purple-700 flex gap-2" onClick={() => navigate('/join-class')}>
                  Join Class
                </Button>
                <Link to="/announcements">
                  <button className="relative">
                    <Bell className="h-6 w-6 text-gray-500" />
                    <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
                  </button>
                </Link>
                   <DropdownMenu>
              <DropdownMenuTrigger asChild>
              <button >
                <User className="h-6 w-6 text-gray-500" />
              </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{user?.name}</p>
                    <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
                    <p className="text-xs leading-none text-muted-foreground capitalize">{selectedRole}</p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleProfileClick}>
                  Profile
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleSwitchRoleClick}>
                  Switch Role
                </DropdownMenuItem>
                
                <DropdownMenuItem asChild>
                  <Link to="/blogs">Blogs</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/profile">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>Log out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          
              </>
            )}
          </div>
        </header>

        <div className="p-4 md:p-6">
          <div className="max-w-screen-xl mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4 md:mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input 
                  className="edu-form-field pl-10" 
                  placeholder="Search" 
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
        
            {isLoading ? (
              <div className="text-center py-12">
                <p className="text-gray-500">Loading classes...</p>
              </div>
            ) : filteredClasses.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6  auto-rows-fr">
                {filteredClasses.map((classItem) => (
                  <Card key={classItem.classroomId} className="overflow-hidden flex flex-col h-full">
                     <div className="relative">
                      {classItem.posterFileId ? (
                        <img 
                          key={classItem.imageKey || classItem.id}
                          src={classItem.image}
                          alt={classItem.className} 
                          className="w-full h-40 object-cover"
                        />
                      ) : (
                        <div className="w-full h-40 bg-gray-400"></div>
                      )}              
                  <div className="absolute top-2 left-2 bg-white rounded px-2 py-1 text-xs">Online</div>
                    </div>
                    <CardContent className="p-4 flex-grow">
                      <div className="flex items-center mb-2 text-xs text-gray-500">
                        <span>{classItem.subjectName}</span>
                        <span className="mx-2">•</span>
                        <span>{classItem.batchName}</span>                      </div>
                      <h3 className="font-medium mb-4">{classItem.className}</h3>
                      
                      <div className="w-full h-1 bg-gray-200 rounded-full mb-1">
                        <div 
                          className="h-1 bg-green-500 rounded-full" 
                          style={{ width: `${classItem.totalSchedules > 0 ? (( classItem.pendingSchedules / classItem.totalSchedules )* 100) : 0}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mb-3">
                        <span>{classItem.pendingSchedules || 0} Classes Pending ({classItem.totalSchedules > 0 ? Math.round((classItem.pendingSchedules / classItem.totalSchedules) * 100) : 0}%)</span>
                      </div>
                
                    </CardContent>
                    <CardFooter className="flex w-full justify-between flex-wrap gap-2">
                      <Button 
                          variant="outline" 
                          size="sm" 
                          className="text-purple-600 border-purple-600 hover:bg-purple-50" 
                          onClick={e => {
                            console.log("Manage class clicked", classItem);
                            e.stopPropagation();
                            navigate(`/class/${classItem.classroomId}/student-manageclass`);
                          }}
                        >
                          Manage
                        </Button>
                    {/*  {classItem.pendingSchedules > 0 && (
                        <Button 
                          className="bg-purple-600 hover:bg-purple-700" 
                          size="sm" 
                          onClick={() => navigate(`/classroom/${classItem.classroomId}`)}
                        >
                          Join Now
                        </Button>
                      )}*/}
                       <span className={`text-sm px-2 py-1 rounded ${classItem.completed ? 'bg-gray-100 text-gray-600' : 'bg-green-100 text-green-600'}`}>
                            {classItem.completed ? 'Completed' : 'Active'}
                          </span>
                    </CardFooter>
                  </Card>
                ))}
              
              </div>
            ) : (
              <div className="text-center py-12 md:py-16 bg-white rounded-lg border border-gray-200">
                <div className="mb-4">
                  <BookOpen className="h-10 w-10 md:h-12 md:w-12 text-gray-300 mx-auto" />
                </div>
                <h2 className="text-lg md:text-xl font-medium mb-2">No Classes Found</h2>
                <p className="text-gray-500 mb-6">
                  {searchTerm ? "No classes match your search criteria." : "You haven't joined any classes yet."}
                </p>
                
              </div>
            )}
          </div>
        </div>
        
      </div>
    </div>
  );
}
