
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Loader2, Wand2 } from "lucide-react";
import { toast } from "sonner";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {  QuestionGenParams } from "@/utils/llmService";
import { generateAIQuestions } from "@/services/assignmentService";
import { useAuth } from "react-oidc-context";
interface QuestionGeneratorProps {
  onQuestionsGenerated: (questions: any[]) => void;
}

const QuestionGenerator: React.FC<QuestionGeneratorProps> = ({ onQuestionsGenerated }) => {
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [count, setCount] = useState(5);
  const [difficulty, setDifficulty] = useState<"easy" | "medium" | "hard">("medium");
  const [type, setType] = useState<"multiple-choice" | "essay" | "true-false" | "all">("all");
  const auth = useAuth();
   const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt first");
      return;
    }
    const token = auth.user?.access_token;
    if (!token) {
      toast.error("You must be signed in to generate questions");
      return;
    }

    setIsGenerating(true);

    try {
      // map UI values to service payload shape
      const difficultyMap: Record<string, string> = {
        easy: "EASY",
        medium: "MEDIUM",
        hard: "HARD",
      };

      const typeMap: Record<string, string[]> = {
        "multiple-choice": ["MULTIPLE_CHOICE"],
        essay: ["SHORT_ANSWER"],
        "true-false": ["TRUE_FALSE"],
        all: ["MULTIPLE_CHOICE", "SHORT_ANSWER", "TRUE_FALSE"],
      };

      const payload = {
        topic: "General", // adjust or expose as an input if you want user-controlled topic
        difficultyLevel: difficultyMap[difficulty] || "MEDIUM",
        numberOfQuestions: count,
        questionTypes: typeMap[type] || ["SHORT_ANSWER"],
        additionalInstructions: prompt.trim(),
        gradeLevel: "Grade 10", // change or expose if needed
        marksPerQuestion: 2, // change or expose if needed
      };

      console.log("generateAIQuestions payload:", payload);
       const response = await generateAIQuestions(token, payload as any);
      // service returns an array of question objects (or may return { questions: [...] })
      const data: any[] = Array.isArray(response) ? response : (response?.questions ?? []);
      console.log("generateAIQuestions response length:", data.length);

      if (data.length > 0) {
        // pass the object array to AssignmentForm which maps it into UI shape
        onQuestionsGenerated(data);
        toast.success(`Generated ${data.length} questions`);
        setPrompt("");
      } else {
        toast.error("Failed to generate questions");
      }
   } catch (error) {
      console.error("Error generating questions:", error);
      toast.error("An error occurred while generating questions");
    } finally {
      setIsGenerating(false);
    }
  };
  return (
    <div className="border rounded-lg p-6 bg-muted/30">
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">Generate AI Questions</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Enter a prompt to generate questions for your assignment.
          </p>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="prompt">Prompt</Label>
          <Textarea
            id="prompt"
            placeholder="E.g., 'Generate comprehension questions about photosynthesis'"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            className="min-h-[100px]"
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="count">Number of Questions</Label>
            <Input
              id="count"
              type="number"
              className='edu-form-field'
              
              min={1}
              max={20}
              value={count}
              onChange={(e) => setCount(Number(e.target.value))}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="difficulty">Difficulty</Label>
            <Select value={difficulty} onValueChange={(value: any) => setDifficulty(value)}>
              <SelectTrigger id="difficulty">
                <SelectValue placeholder="Select difficulty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="easy">Easy</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="hard">Hard</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="space-y-2">
          <Label>Question Type</Label>
          <RadioGroup 
            value={type} 
            onValueChange={(value: any) => setType(value)}
            className="flex flex-col space-y-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="all" />
              <Label htmlFor="all">Mixed Types</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="multiple-choice" id="multiple-choice" />
              <Label htmlFor="multiple-choice">Multiple Choice</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="essay" id="essay" />
              <Label htmlFor="essay">Essay/Short Answer</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="true-false" id="true-false" />
              <Label htmlFor="true-false">True/False</Label>
            </div>
          </RadioGroup>
        </div>
        
        <Button 
          onClick={handleGenerate}
          disabled={isGenerating || !prompt.trim()}
          className="w-full"
        >
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Wand2 className="mr-2 h-4 w-4" />
              Generate Questions
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default QuestionGenerator;
