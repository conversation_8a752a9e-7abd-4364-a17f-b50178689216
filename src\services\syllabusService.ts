
import { Syllabus } from "@/types";
import syllabusData from "@/data/syllabus.json";

// Helper function to convert JSON date strings to Date objects
const convertDates = (syllabuss: any[]): Syllabus[] => {
  return syllabuss.map(syllabus => ({
    ...syllabus
  }));
};

//generic file upload
export async function uploadFile(accessToken: string, filesData: { file: File, description?: string, tags?: string, relatedEntityType?: string, fileCategory?: string }) {
  const formData = new FormData();
  formData.append("file", filesData.file);
  if (filesData.description) formData.append("description", filesData.description);
  if (filesData.tags) formData.append("tags", filesData.tags);
  formData.append("relatedEntityType", filesData.relatedEntityType || "class");
  formData.append("fileCategory", filesData.fileCategory || "image");

  const response = await fetch(`/api/file/v1/files`, {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${accessToken}`
    },
    body: formData
  });

  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to upload file");
  }
  return response.json();
}

export async function downloadSyllabusFile(accessToken: string, fileId: string, fileName: string) {
  const url = `/api/file/v1/files/${fileId}/download`;
  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (!response.ok) {
    throw new Error("Failed to download file");
  }
  const blob = await response.blob();
  const downloadUrl = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = downloadUrl;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  a.remove();
  window.URL.revokeObjectURL(downloadUrl);
}

// Get all Syllabus from backend
export const getAllSyllabus = async (accessToken, classroomId) => {
  const response = await fetch(`/api/syllabusManagement/v1/classroom/${classroomId}/syllabus`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch syllabus");
  }
  return response.json();
};

export async function addSyllabusToClassroom(accessToken: string, classroomId: string, syllabusData: any) {
  const response = await fetch(`/api/syllabusManagement/v1/classroom/${classroomId}/syllabus`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(syllabusData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to add syllabus");
  }
  return response.json();
}

export async function addSyllabusWithAI(accessToken: string, classroomId: string, syllabusData: any) {
  const response = await fetch(`/api/syllabusManagement/v1/generate-topics`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(syllabusData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to add syllabus");
  }
  return response.json();
}

export async function removeSyllabusById(accessToken: string, syllabusId: string) {
  const response = await fetch(`/api/syllabusManagement/v1/syllabus/${syllabusId}`, {
    method: "DELETE",
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json"
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to remove syllabus");
  }
  return response.json();
}
export async function editSyllabusById(accessToken: string, syllabusId: string, updatedData: any) {
  const response = await fetch(`/api/syllabusManagement/v1/syllabus/${syllabusId}`, {
    method: "PUT",
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json"
    },
    body: JSON.stringify(updatedData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update syllabus");
  }
  return response.json();
}


export const EditSyllabusStatus = async (accessToken: string ,id: string,value:string)=> {
  const response = await fetch(`/api/syllabusManagement/v1/syllabus/${id}/status/${value}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  
  if (response.status === 401) {
    window.location.href = "/";
    return false;
  }
  
  if (!response.ok) {
    throw new Error("Failed to updated syllabus");
  }
  
  return true;
};
