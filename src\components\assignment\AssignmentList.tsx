// ...existing code...
import React, { useEffect, useState } from "react";
import { Assignment } from "@/types";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/context/AuthContext";
import { UserRole } from "@/types";
import { format, differenceInDays, differenceInHours } from "date-fns";
import { Edit, FileCheck, FileClock, Award,Users,
  Trash2, Clock, Calendar, TrendingUp, AlertCircle, Sparkles,
Eye } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { Progress } from "@/components/ui/progress";
import { useUserRole } from "@/hooks/useUserRole";

interface AssignmentListProps {
  assignments: Assignment[];
  classId: string;
  currentEnrollment?: number;
  onSubmit?: (assignmentId: string) => void;
  onEdit?: (assignment: Assignment) => void;
  onDelete?: (assignmentId: string) => void;
  onViewDetails?: (assignment: Assignment) => void;
  onPublish?: (assignment: Assignment) => void;
  onViewSubmissions?: (assignment: Assignment) => void;
  onRefresh?: () => void; // Callback to refresh assignments after deletion
  refreshKey?: string | number; // Force refresh when this changes
}

export default function AssignmentList({
  assignments,
  classId,
  currentEnrollment = 30,
  onSubmit,
  onEdit,
  onDelete,
  onViewDetails,
  onPublish,
  onViewSubmissions,
  onRefresh,
  refreshKey,
}: AssignmentListProps) {
 const { selectedRole } = useUserRole();  
console.log(selectedRole)
  const  user  = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("published");

  // Log when assignments change to debug the issue
  useEffect(() => {
  }, [assignments, refreshKey]);
  const publishedAssignments = assignments.filter(a => a.status === 'PUBLISHED' );
  const draftAssignments = assignments.filter(a => a.status === 'DRAFT');
  const sortedAssignments = [...assignments].sort(
    (a, b) => new Date(b.createdAt || b.createdAt).getTime() - new Date(a.createdAt || a.createdAt).getTime()
  );

  const isOverdue = (dueDate: Date) => {
    return dueDate < new Date();
  };

  const hasSubmitted = (assignment: Assignment) => {
    return user && (assignment.submittedBy || []).includes(user.user.id);
  };

  const getTimeRemaining = (dueDate: Date) => {
    const now = new Date();
    const target = new Date(dueDate);
    const days = differenceInDays(target, now);
    const hours = differenceInHours(target, now);
    
    if (days > 7) return { text: `${days} days left`, urgent: false };
    if (days > 1) return { text: `${days} days left`, urgent: false };
    if (days === 1) return { text: "Due tomorrow", urgent: true };
    if (hours > 0) return { text: `${hours} hours left`, urgent: true };
    return { text: "Overdue", urgent: true };
  };

  const getSubmissionProgress = (assignment: Assignment) => {
    const submitted = assignment.totalSubmissions || 0;
    return currentEnrollment > 0 ? Math.round((submitted / currentEnrollment) * 100) : 0;
  };

  if (!assignments || assignments.length === 0) {
    return (
      <div className="relative p-12 flex flex-col items-center justify-center text-center overflow-hidden rounded-xl bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 border-2 border-dashed border-purple-200">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="relative z-10">
          <div className="relative mb-6">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full blur-xl opacity-20 animate-pulse"></div>
            <div className="relative rounded-full bg-gradient-to-br from-purple-100 to-blue-100 p-6">
              <Sparkles className="h-12 w-12 text-purple-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold mb-2 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            No assignments yet
          </h3>
          <p className="text-sm text-muted-foreground mb-6 max-w-md">
            {selectedRole === UserRole.TEACHER
              ? "Create your first assignment and watch your students' progress come to life!"
              : "Your teacher hasn't created any assignments yet. Check back soon!"}
          </p>
          {selectedRole === UserRole.TEACHER && (
        <Button 
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
         asChild
        >
          <Link to={`/class/${classId}/create-assignment`}>
                <Sparkles className="h-4 w-4 mr-2" />
                Create Assignment
              </Link>
          Create New Assignment
        </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Card */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-5">
            <Card className="p-6 border-2 border-blue-200 bg-blue-50/50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 mb-1">Total</p>
                  <p className="text-gray-900">{assignments.length} Assignments</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-200 flex items-center justify-center">
                  <Award className="w-6 h-6 text-blue-700" />
                </div>
              </div>
            </Card>

            <Card className="p-6 border-2 border-green-200 bg-green-50/50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 mb-1">Published</p>
                  <p className="text-gray-900">{publishedAssignments.length} Active</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-200 flex items-center justify-center">
                  <Eye className="w-6 h-6 text-green-700" />
                </div>
              </div>
            </Card>

            <Card className="p-6 border-2 border-yellow-200 bg-yellow-50/50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 mb-1">Drafts</p>
                  <p className="text-gray-900">{draftAssignments.length} Assignments</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-yellow-200 flex items-center justify-center">
                  <Edit className="w-6 h-6 text-yellow-700" />
                </div>
              </div>
            </Card>

            <Card className="p-6 border-2 border-purple-200 bg-purple-50/50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 mb-1">Submissions</p>
                  <p className="text-gray-900">
                    {publishedAssignments.reduce((acc, a) => acc + (a.totalSubmissions || 0), 0)} Total
                  </p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-200 flex items-center justify-center">
                  <Users className="w-6 h-6 text-purple-700" />
                </div>
              </div>
            </Card>
          </div>
      
      {/* Assignments Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="h-12 bg-transparent border-0 p-0">
          <TabsTrigger value="published" className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-purple-600 data-[state=active]:bg-transparent px-4">
            <Eye className="w-4 h-4 mr-3" />
            Published ({publishedAssignments.length})
          </TabsTrigger>
          <TabsTrigger value="drafts" className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-purple-600 data-[state=active]:bg-transparent px-4">
            <Edit className="w-4 h-4 mr-3" />
            Drafts ({draftAssignments.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="published" className="mt-6">
          {publishedAssignments.length > 0 ? (
            <div className="space-y-4  bg-white">
              {publishedAssignments.map((assignment) => {
                const submissionRate = getSubmissionProgress(assignment);
                const isPastDue = new Date(assignment.dueDate) < new Date();

                return (
                  <Card 
                    key={assignment.id} 
                    className="p-6 hover:shadow-lg transition-shadow border-2 border-gray-200 hover:border-blue-300"
                  >
                    <div className="flex items-start justify-between gap-4 mb-4">
                      <div className="flex-1">
                        <div className="flex items-start gap-3 mb-2">
                          <h3 className="text-gray-900 flex-1">{assignment.title}</h3>
                          <div className="flex items-center gap-2">
                            <Badge className="bg-green-100 text-green-800 border-green-200">
                              Published
                            </Badge>
                            {selectedRole === UserRole.TEACHER && (
                              <>
                                <Button variant="ghost" size="sm" onClick={() => onEdit && onEdit(assignment)}>
                                  <Edit className="w-4 h-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700" onClick={async () => {
                                  if (onDelete) {
                                    await onDelete(assignment.id);
                                    onRefresh && onRefresh();
                                  }
                                }}>
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </div>
                        
                        <p className="text-gray-600 mb-4">{assignment.description?.replace(/<[^>]*>/g, '') || ''}</p>
                        
                        <div className="flex flex-wrap items-center gap-4 text-gray-600">
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            <span className={isPastDue ? 'text-red-600' : ''}>
                              Due {format(new Date(assignment.dueDate), 'MMM d, yyyy')}
                            </span>
                            {isPastDue && <Badge variant="outline" className="text-red-600 border-red-300">Past Due</Badge>}
                          </div>
                          <div className="flex items-center gap-2">
                            <Award className="w-4 h-4" />
                            <span>{assignment.totalMarks || 100} points</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span>{assignment.totalQuestions || 0} questions</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Submission Progress */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-gray-600">Submissions</span>
                          <span className="text-gray-900">
                            {(assignment.totalSubmissions)}/{currentEnrollment} ({submissionRate}%)
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all"
                            style={{ width: `${submissionRate}%` }}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button 
                        variant="outline"
                        className="flex-1"
                        onClick={() => {
                          console.log('View Details clicked for assignment:', assignment);
                          onViewDetails && onViewDetails(assignment);
                        }}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        Details
                      </Button>
                      {selectedRole === UserRole.TEACHER && (
                        <Button 
                          variant="outline"
                          className="flex-1"
                          onClick={() => onViewSubmissions && onViewSubmissions(assignment)}
                        >
                          <Users className="w-4 h-4 mr-2" />
                          Submissions ({assignment.totalSubmissions })
                        </Button>
                      )}
                    </div>
                  </Card>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No published assignments yet</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="drafts" className="mt-6">
          {draftAssignments.length > 0 ? (
            <div className="space-y-4  bg-white">
              {draftAssignments.map((assignment) => (
                <Card 
                  key={assignment.id} 
                  className="p-6 hover:shadow-lg transition-shadow border-2 border-gray-200 hover:border-yellow-300"
                >
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-start gap-3 mb-2">
                        <h3 className="text-gray-900 flex-1">{assignment.title}</h3>
                        <div className="flex items-center gap-2">
                          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                            Draft
                          </Badge>
                          <Button variant="ghost" size="sm" onClick={() => onEdit && onEdit(assignment)}>
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700" onClick={async () => {
                            if (onDelete) {
                              await onDelete(assignment.id);
                              onRefresh && onRefresh();
                            }
                          }}>
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      
                      <p className="text-gray-600 mb-4">{assignment.description?.replace(/<[^>]*>/g, '') || ''}</p>
                      
                      <div className="flex flex-wrap items-center gap-4 text-gray-600">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          <span>Due {format(new Date(assignment.dueDate), 'MMM d, yyyy')}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Award className="w-4 h-4" />
                          <span>{assignment.totalSubmissions || 100} points</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span>{assignment.totalQuestions|| 0} questions</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 mt-4">
                    <Button 
                      variant="outline"
                      className="flex-1"
                      onClick={() => {
                        console.log('View Details clicked for assignment:', assignment);
                        onViewDetails && onViewDetails(assignment);
                      }}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Details
                    </Button>
                    <Button 
                      className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                      onClick={() => onPublish && onPublish(assignment)}
                    >
                      Publish
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Edit className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No draft assignments yet</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

    </div>
  );
};
// ...existing code...