
import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";

import { Checkbox } from "@/components/ui/checkbox";
import { CalendarIcon, Plus, X, Paperclip, File, CheckCircle2, Sparkles,
  ArrowLeft, PencilLine } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { toast } from "sonner";
import { Assignment } from "@/types";
import QuestionGenerator from "./QuestionGenerator";
 import { useAuth } from "react-oidc-context";
import { createAssignment, updateAssignment } from "@/services/assignmentService";
import { ManualAssignmentForm } from '@/components/assignment/ManualAssignmentForm';
import { AIAssignmentForm } from '@/components/assignment/AIAssignmentForm';

interface QuestionWithFile {
  text: string;
  type: 'text' | 'mcq' | 'trueFalse';
  options?: string[];
 correctAnswers?: number[] | string[];
 file?: File;
  fileName?: string;
}
const formSchema = z.object({
  title: z.string().min(3, {
    message: "Title must be at least 3 characters.",
  }),
  description: z.string().min(10, {
    message: "Description must be at least 10 characters.",
  }),
  dueDate: z.date({
    required_error: "Due date is required.",
  }),
});

interface AssignmentFormProps {
  classId: string;
  onSubmit: () => void; // Called after successful creation/update
  assignment?: Assignment | null; // For edit mode
  mode?: 'create' | 'edit' | 'view'; // Mode prop
  onClose?: () => void;
  onBack: () => void;
}

export default function AssignmentForm({
  classId,
  onSubmit,
  assignment,
  mode = 'create',
  onClose,
  onBack
}: AssignmentFormProps) {
  const isEditMode = mode === 'edit';
  const isViewMode = mode === 'view';
  const [generatedQuestions, setGeneratedQuestions] = useState<QuestionWithFile[]>([]);
     const auth = useAuth();
      const [manualQuestions, setManualQuestions] = useState<QuestionWithFile[]>(() => {
        // Initialize with existing questions in edit mode
        if (assignment?.questions) {
          return assignment.questions.map(q => {
            if (typeof q === 'string') {
              return { text: q, type: 'text' as const };
            }
            // Handle question objects
            const qObj = q as any;
            return {
              text: qObj.questionText || qObj.text || String(q),
            type: qObj.type === 'MULTIPLE_CHOICE' ? 'mcq' as const : 'text' as const,
              options: qObj.options,
              correctAnswers: qObj.correctAnswer ? [qObj.correctAnswer] : undefined
            };
          });
        }
        return [];
      });

  const [currentQuestion, setCurrentQuestion] = useState("");
  const [questionType, setQuestionType] = useState<'text' | 'mcq'>('text');
  const [mcqOptions, setMcqOptions] = useState<string[]>(['', '', '', '']);
  const [correctAnswers, setCorrectAnswers] = useState<number[]>([]);
const [activeTab, setActiveTab] = useState<'ai' | 'manual'>('ai');
  
  const handleTabChange = (value: string) => {
    setActiveTab(value as 'ai' | 'manual');
  };
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: assignment?.title || "",
      description: assignment?.description ? assignment.description.split('\n\n**Generated Questions:**')[0] : "",
      dueDate: assignment?.dueDate ? new Date(assignment.dueDate as unknown as string) : new Date(),
    },
  });

  const handleSubmit = async (values: any) => {
    // Handle both form schema values and direct data from ManualAssignmentForm
    const formData = values.title ? values : {
      title: values.title || '',
      description: values.description || '',
      dueDate: values.dueDate || new Date()
    };
    
    // Get questions from the passed data or existing state
    const questionsFromForm = values.questions || [];
    const questionsToProcess = questionsFromForm.length > 0 ? questionsFromForm : [...manualQuestions, ...generatedQuestions];
    
    // Build payload matching required structure
    const questionsPayload = questionsToProcess.map((q: any) => {
      // Handle questions from AI form (already have proper type)
      if (q.questionText && (q.type === 'MULTIPLE_CHOICE' || q.type === 'TRUE_FALSE' || q.type === 'LONG_ANSWER' || q.type === 'SHORT_ANSWER')) {
        return {
          questionText: q.questionText,
          type: q.type, // Keep the original AI type
          marks: q.marks || 1,
          options: q.options || [],
          correctAnswer: q.correctAnswer || "",
          explanation: q.explanation || "",
        };
      }
      // Handle questions from ManualAssignmentForm
      if (q.text) {
        return {
          questionText: q.text,
          type: q.type === "mcq" ? "MULTIPLE_CHOICE" : "LONG_ANSWER",
          marks: 1,
          options: q.type === "mcq" ? (q.options || []) : [],
          correctAnswer: q.type === "mcq" && q.correctAnswers && q.correctAnswers.length > 0 
            ? (typeof q.correctAnswers[0] === 'number' ? q.options?.[q.correctAnswers[0]] || "" : String(q.correctAnswers[0]))
            : "",
          explanation: "",
        };
      }
      // Handle existing questions format (fallback)
      return {
        questionText: q.text || q.questionText,
        type: q.type === "mcq" ? "MULTIPLE_CHOICE" : q.type === "trueFalse" ? "TRUE_FALSE" : q.type || "LONG_ANSWER",
        marks: q.marks || 1,
        options: (q.type === "mcq" || q.type === "MULTIPLE_CHOICE") ? (q.options || []) : [],
        correctAnswer: q.correctAnswer || ((q.type === "mcq" || q.type === "MULTIPLE_CHOICE") && q.correctAnswers && q.correctAnswers.length > 0 
          ? (typeof q.correctAnswers[0] === 'number' ? q.options?.[q.correctAnswers[0]] || "" : String(q.correctAnswers[0]))
          : ""),
        explanation: q.explanation || "",
      };
    });

      const payload = {
         classroomId: classId,
         title: formData.title,
         description: formData.description,
         subject:  "General",
         topic:  "General",
         type: questionsFromForm.length > 0 || generatedQuestions.length > 0 ? "MANUAL" : "MANUAL",
         dueDate: format(formData.dueDate, "yyyy-MM-dd"),
        
        questions: questionsPayload,
       };

    try {
      if (isEditMode && assignment?.id) {
        await updateAssignment(auth.user?.access_token, payload, assignment.id);
        toast.success("Assignment updated successfully");
      } else {
        await createAssignment(auth.user?.access_token, payload);
        toast.success("Assignment created successfully");
      }

      onSubmit(); // Notify parent of successful creation/update
      onClose?.();

      if (!isEditMode) {
        form.reset();
        setGeneratedQuestions([]);
      }
    } catch (error) {
      console.error(isEditMode ? "updateAssignment error:" : "createAssignment error:", error);
      toast.error(isEditMode ? "Failed to update assignment" : "Failed to create assignment");
    }
  };

  const handleQuestionsGenerated = (questions: any[]) => {
    if (!questions || questions.length === 0) return;

    const mapped: QuestionWithFile[] = questions.map((item) => {
      // prefer known fields from service
      const text = item.questionText ?? item.question ?? String(item);
      const rawType: string | undefined = item.type ?? undefined;
      const isMcq = rawType === "MULTIPLE_CHOICE" || rawType === "TRUE_FALSE";
      const options = Array.isArray(item.options) ? item.options.map((o: any) => String(o).trim()) : 
        (rawType === "TRUE_FALSE" ? ["True", "False"] : undefined);

      // normalize correct answer(s)
      let correctAnswers: number[] | string[] = [];
      if (item.correctAnswer != null) {
        // if options exist try to map answer string to index
        if (options && typeof item.correctAnswer === "string") {
          const matchIndex = options.findIndex((opt) => opt.trim() === String(item.correctAnswer).trim());
          if (matchIndex >= 0) correctAnswers = [matchIndex];
          else correctAnswers = [String(item.correctAnswer).trim()];
        } else {
          // no options: keep as string
          correctAnswers = [String(item.correctAnswer)];
        }
      } else if (Array.isArray(item.correctAnswers)) {
        // some APIs may return correctAnswers array
        correctAnswers = item.correctAnswers;
      }

      return {
        text,
        type: rawType === "TRUE_FALSE" ? "trueFalse" : isMcq ? "mcq" : "text",
        options,
        correctAnswers,
      };
    });

    // switch to AI tab when questions are produced
    setActiveTab("ai");
    setGeneratedQuestions(mapped);
  };
   const handleAddManualQuestion = () => {
    if (currentQuestion.trim()) {
      if (questionType === 'mcq') {
        const validOptions = mcqOptions.filter(opt => opt.trim());
        if (validOptions.length < 2) {
          toast.error("Please add at least 2 options");
          return;
        }
        if (correctAnswers.length === 0) {
          toast.error("Please select at least one correct answer");
          return;
        }
        setManualQuestions([...manualQuestions, { 
          text: currentQuestion.trim(),
          type: 'mcq',
          options: validOptions,
          correctAnswers
        }]);
      } else {
        setManualQuestions([...manualQuestions, { text: currentQuestion.trim(), type: 'text' }]);
      }
      setCurrentQuestion("");
      setMcqOptions(['', '', '', '']);
      setCorrectAnswers([]);
      setQuestionType('text');
      toast.success("Question added");
    }
  };

  const handleRemoveManualQuestion = (index: number) => {
    setManualQuestions(manualQuestions.filter((_, i) => i !== index));
  };

  const handleRemoveGeneratedQuestion = (index: number) => {
    setGeneratedQuestions(generatedQuestions.filter((_, i) => i !== index));
  };

  const handleFileAttach = (index: number, file: File, isGenerated: boolean) => {
    if (isGenerated) {
      const updated = [...generatedQuestions];
      updated[index] = { ...updated[index], file, fileName: file.name };
      setGeneratedQuestions(updated);
    } else {
      const updated = [...manualQuestions];
      updated[index] = { ...updated[index], file, fileName: file.name };
      setManualQuestions(updated);
    }
    toast.success("File attached");
  };

  const handleFileRemove = (index: number, isGenerated: boolean) => {
    if (isGenerated) {
      const updated = [...generatedQuestions];
      const { file, fileName, ...rest } = updated[index];
      updated[index] = rest;
      setGeneratedQuestions(updated);
    } else {
      const updated = [...manualQuestions];
      const { file, fileName, ...rest } = updated[index];
      updated[index] = rest;
      setManualQuestions(updated);
    }
    toast.success("File removed");
  };


    return (
    <div className=" mx-auto">
      <Button 
        variant="ghost" 
        className="mb-6"
        onClick={onBack}
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Assignments
      </Button>

      <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        

        {/* Tabs for creation methods */}
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          {mode === 'create' && (
          <div className="border-b border-gray-200 bg-gray-50/50 px-8 pt-6">
            <TabsList className="grid w-full grid-cols-2 h-12 bg-transparent border-0 p-0">
              <TabsTrigger 
                value="ai" 
                className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-purple-600 data-[state=active]:bg-transparent px-4"
              >
                <Sparkles className="w-4 h-4" />
                AI-Assisted
              </TabsTrigger>
              <TabsTrigger 
                value="manual" 
               className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-purple-600 data-[state=active]:bg-transparent px-4"
              >
                <PencilLine className="w-4 h-4" />
                Manual Creation
              </TabsTrigger>
            </TabsList>
          </div>
          )}

          <div className="p-0 sm:p-8">
            <TabsContent value="ai" className="mt-0">
              <AIAssignmentForm 
                onSuccess={onSubmit} 
                onSubmit={handleSubmit} 
                onQuestionsGenerated={handleQuestionsGenerated}
                assignment={assignment}
                mode={mode}
                assignmentId={assignment?.id}
              />
            </TabsContent>

            <TabsContent value="manual" className="mt-0">
              <ManualAssignmentForm 
                onSuccess={onSubmit} 
                onSubmit={handleSubmit}
                assignment={assignment}
                mode={mode}
              />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}
