import { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { createLeaveRequest } from "@/services/leaveService";
import { toast } from "sonner";
import { useAuth } from "react-oidc-context"; // Updated import

interface StudentLeaveFormProps {
  classId: string;
  studentId: string;
  onLeaveSubmitted: () => void;
}

export function StudentLeaveForm({ classId, studentId, onLeaveSubmitted }: StudentLeaveFormProps) {
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({
    startDate: "",
    endDate: "",
    reason: ""
  });
  const auth = useAuth();

  const validateStartDate = (date: Date | undefined) => {
    if (!date) return "Start date is required";
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (date < today) return "Start date cannot be in the past";
    return "";
  };

  const validateEndDate = (date: Date | undefined) => {
    if (!date) return "End date is required";
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (date < today) return "End date cannot be in the past";
    if (startDate && date < startDate) return "End date cannot be before start date";
    return "";
  };

  const validateReason = (value: string) => {
    if (!value.trim()) return "Reason is required";
    if (value.trim().length < 10) return "Reason must be at least 10 characters";
    return "";
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const startDateError = validateStartDate(startDate);
    const endDateError = validateEndDate(endDate);
    const reasonError = validateReason(reason);
    
    setErrors({
      startDate: startDateError,
      endDate: endDateError,
      reason: reasonError
    });
    
    if (startDateError || endDateError || reasonError) {
      return;
    }

    setIsSubmitting(true);
    try {
      await createLeaveRequest(auth.user.access_token,{
        //studentId,
        classId,
        startDate: format(startDate, "yyyy-MM-dd") as any,
        endDate: format(endDate, "yyyy-MM-dd") as any,
        reason: reason.trim(),
        createdDate: new Date(),
        studentName: auth.user?.profile?.name || auth.user?.profile?.email || "Unknown Student"
      });
      
      toast.success("Leave request submitted successfully");
      setStartDate(undefined);
      setEndDate(undefined);
      setReason("");
      setErrors({ startDate: "", endDate: "", reason: "" });
      onLeaveSubmitted();
    } catch (error) {
      console.error("Error submitting leave request:", error);
      toast.error("Failed to submit leave request");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Apply for Leave</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => {
                      setStartDate(date);
                      setErrors(prev => ({ ...prev, startDate: validateStartDate(date) }));
                      if (endDate) {
                        setErrors(prev => ({ ...prev, endDate: validateEndDate(endDate) }));
                      }
                    }}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.startDate && <p className="text-red-500 text-sm">{errors.startDate}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => {
                      setEndDate(date);
                      setErrors(prev => ({ ...prev, endDate: validateEndDate(date) }));
                    }}
                    disabled={(date) => date < (startDate || new Date())}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.endDate && <p className="text-red-500 text-sm">{errors.endDate}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason for Leave</Label>
            <Textarea
              id="reason"
              placeholder="Please provide a reason for your leave request..."
              value={reason}
              onChange={(e) => {
                setReason(e.target.value);
                setErrors(prev => ({ ...prev, reason: validateReason(e.target.value) }));
              }}
              rows={3}
              className={errors.reason ? 'border-red-500' : 'edu-form-field'}
            />
            {errors.reason && <p className="text-red-500 text-sm">{errors.reason}</p>}
          </div>

          <Button type="submit" disabled={isSubmitting}   className="bg-purple-600 hover:bg-purple-700 ml-auto flex-1 items-center gap-2 rounded-full px-8"
>
            {isSubmitting ? "Submitting..." : "Submit"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}