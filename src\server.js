const io = require("socket.io")(8081, { cors: { origin: "*" } });

io.on("connection", (socket) => {
  const { classId } = socket.handshake.query;
  console.log(`New connection: ${socket.id}, classId: ${classId}`);
  
  if (classId) {
    socket.join(classId);
    console.log(`Socket ${socket.id} joined class ${classId}`);
    console.log(`Total clients in room ${classId}:`, io.sockets.adapter.rooms.get(classId)?.size || 0);
  }

  socket.on("draw-data", ({ classId, paths }) => {
    console.log(`Received draw-data from ${socket.id}:`, { classId, pathsCount: paths?.length });
    const room = io.sockets.adapter.rooms.get(classId);
    console.log(`Room ${classId} has ${room?.size || 0} clients:`, Array.from(room || []));
    
    // Broadcast to all clients in the room except sender
    socket.to(classId).emit("draw-data", { paths });
    console.log(`Broadcasted draw-data to class ${classId}`);
  });

  socket.on("disconnect", () => {
    console.log(`Socket ${socket.id} disconnected`);
  });
});