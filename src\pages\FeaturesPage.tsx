
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  BookOpen,
  Users,
  MessageSquare,
  Bell,
  CircleDollarSign,
  CalendarDays,
  Brain,
  ClipboardList,
  Video,
  FileText,
  BarChart,
  Shield,
  Sparkles
} from "lucide-react";
import { toast } from "sonner";
import Footer from "@/components/Footer"; // or your preferred toast library
import { useAuth } from "react-oidc-context";
import { useState } from "react";

export default function FeaturesPage() {
  const features = [
    {
      icon: BookOpen,
      title: "Smart Classroom Management",
      description: "Create and manage virtual classrooms with ease. Organize course materials, assignments, and resources efficiently.",
      color: "text-blue-600 bg-blue-100"
    },
    {
      icon: Users,
      title: "Student Management",
      description: "Track student progress, manage enrollments, and maintain detailed student profiles all in one place.",
      color: "text-green-600 bg-green-100"
    },
    {
      icon: MessageSquare,
      title: "Communication Tools",
      description: "Built-in messaging system for seamless communication between teachers, students, and parents.",
      color: "text-purple-600 bg-purple-100"
    },
    {
      icon: Bell,
      title: "Announcements",
      description: "Broadcast important updates and announcements to specific classes or the entire school community.",
      color: "text-red-600 bg-red-100"
    },
    {
      icon: CircleDollarSign,
      title: "Payment Management",
      description: "Handle tuition fees, process payments, and manage financial records securely.",
      color: "text-emerald-600 bg-emerald-100"
    },
    {
      icon: CalendarDays,
      title: "Scheduling System",
      description: "Organize classes, appointments, and events with our flexible scheduling tools.",
      color: "text-orange-600 bg-orange-100"
    },
    {
      icon: Brain,
      title: "AI-Powered Learning",
      description: "Leverage AI technology for personalized learning experiences and automated assistance.",
      color: "text-indigo-600 bg-indigo-100"
    },
    {
      icon: ClipboardList,
      title: "Attendance Tracking",
      description: "Monitor and record student attendance with our digital attendance system.",
      color: "text-pink-600 bg-pink-100"
    },
    {
      icon: Video,
      title: "Virtual Classrooms",
      description: "Conduct online classes with integrated video conferencing and collaborative tools.",
      color: "text-cyan-600 bg-cyan-100"
    },
    {
      icon: FileText,
      title: "Assignment Management",
      description: "Create, distribute, and grade assignments digitally with our comprehensive tools.",
      color: "text-violet-600 bg-violet-100"
    },
    {
      icon: BarChart,
      title: "Progress Analytics",
      description: "Track and analyze student performance with detailed analytics and reporting.",
      color: "text-amber-600 bg-amber-100"
    },
    {
      icon: Shield,
      title: "Security & Privacy",
      description: "Enterprise-grade security measures to protect sensitive educational data.",
      color: "text-teal-600 bg-teal-100"
    }
  ];
  const auth = useAuth();
    const [isSubmitting, setIsSubmitting] = useState(false);

const handleSignUp  = async () => {
   // window.location.href = "https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/signup?client_id=76u1v7el416ebllhpbhtqpmlh0&code_challenge=Cjh7j5XvSKwPZ5ahIhP5j2tuEvZiuoSm811Q62N0wFs&code_challenge_method=S256&redirect_uri=http%3A%2F%2Flocalhost%3A8081%2Flogin%2Foauth2%2Fcode%2Fcognito&response_type=code&scope=email+openid+phone&state=80e73e7091c04c30a0c4904373b2096f";
     setIsSubmitting(true);
    
    try {
      // Store form values in localStorage to access after redirect back from Cognito
    //  localStorage.setItem("registerFormData", JSON.stringify(form.getValues()));
      
      // Redirect to Cognito signup page
      await auth.signinRedirect({ prompt: "login" });
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
       {/* Hero Section with gradient background */}
        <section className="py-16 md:py-24 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-blue-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto relative z-10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
              <div className="max-w-2xl text-center md:text-left">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white leading-tight">
                  Transform <span className="text-yellow-300">Education</span> Through Innovation
                </h1>
                <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
                  Join thousands of educators and students in revolutionizing the learning experience
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <Button asChild size="lg" className="w-full sm:w-auto bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full shadow-lg">
                    <Link to="/register">Get Started Free</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="w-full sm:w-auto border-2 bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full">
                    <Link to="/subscription">View Pricing</Link>
                  </Button>
                </div>
                <div className="mt-12 flex flex-col sm:flex-row items-center justify-center md:justify-start gap-6">
                  <div className="flex -space-x-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="w-12 h-12 rounded-full border-4 border-purple-600 bg-white shadow-lg flex items-center justify-center text-purple-600 font-bold text-lg">
                        {String.fromCharCode(64 + i)}
                      </div>
                    ))}
                  </div>
                  <p className="text-lg text-white/90">
                    Joined by <span className="font-bold text-yellow-300">2000+</span> educators
                  </p>
                </div>
              </div>
              <div className="w-full md:w-2/5 relative">
                <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-transform duration-500">
                  <img 
                    src="https://images.unsplash.com/photo-1571260899304-425eee4c7efc?q=80&w=2070&auto=format&fit=crop" 
                    alt="EduConnect Platform" 
                    className="w-full h-64 md:h-96 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800">Modern Learning Experience</h3>
                    <p className="text-gray-600">Interactive tools for better engagement</p>
                  </div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-xl shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <p className="text-lg font-bold flex items-center gap-2">
                    <Sparkles className="h-5 w-5" />
                    AI-Powered Learning
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      <main className="flex-grow py-12 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">
              Powerful Features for Modern Education
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Discover all the tools and capabilities that make EduConnect the perfect platform for your educational needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature) => (
              <Card key={feature.title} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg ${feature.color}`}>
                      <feature.icon className="h-6 w-6" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button size="lg" asChild className="bg-purple-600 hover:bg-purple-700 text-white">
              <Link to="/register">Get Started Today</Link>
            </Button>
          </div>
        </div>
      </main>
       <footer className="bg-gray-900 text-white py-8 md:py-12">
              <Footer/>
            </footer>
    </div>
  );
}
