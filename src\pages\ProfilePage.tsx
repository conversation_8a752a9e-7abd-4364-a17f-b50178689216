 
 import React, { useState, useEffect, useRef } from "react";
 import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ci<PERSON>, Check, X, Plus, Trash2 } from "lucide-react";
 import { Link } from "react-router-dom";
 import { useAuth } from "react-oidc-context"; // Updated import
 import { But<PERSON> } from "@/components/ui/button";
 import { Card } from "@/components/ui/card";
 import { Badge } from "@/components/ui/badge";
 import { Input } from "@/components/ui/input";
 import { Textarea } from "@/components/ui/textarea";
 import { toast } from "sonner";
 import { useSelector } from "react-redux";
 import { generateAvatarUrl } from "@/lib/utils";

 import ProfileSidebar from "@/components/profile/ProfileSidebar";
 import { getAllAddresses, addAddress,updateProfile, updateAddress,getProfile,deleteAddress, createProfile } from "@/services/profileService";
 import { 
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue
 } from "@/components/ui/select";
 import {
   <PERSON><PERSON>,
   <PERSON>alog<PERSON>ontent,
   <PERSON><PERSON><PERSON>eader,
   <PERSON><PERSON><PERSON><PERSON><PERSON>,
   DialogFooter
 } from "@/components/ui/dialog";

 export default function ProfilePage() {
   const [editSection, setEditSection] = useState<string | null>(null);
   
   // Mock profile data (in a real app, these would come from backend)
   const [profileData, setProfileData] = useState({
     phone: "9875600320",
     gender: "Male",
     dob: "",
     socialMediaLink: "https://linkedin.com",
     country: "India",
     zipCode: "",
     state: "",
     city: "",
     address: "",
   });
     const auth = useAuth();
     
       
       const userData = useSelector((state: any) => state.user.userData);
       console.log(userData);
       const user = userData
             ? {
                 id: userData.id,
                 name: userData.name || "User",
                 email: userData.email || "",
                 role: userData.roles?.[0].role, // or adjust as needed
                 avatar: generateAvatarUrl(userData.name || "User", "3498db"),
               }
             : null;
   // Temp state for editing
   const [tempData, setTempData] = useState({ ...profileData });
   const [tempName, setTempName] = useState(user?.name || "");
   const [addresses, setAddresses] = useState([]);
     const [profile, setProfile] = useState([]);

   const [newAddress, setNewAddress] = useState({
     country: '',
     zipCode: '',
     state: '',
     city: '',
     address: ''
   });
   const [editAddress, setEditAddress] = useState({
     country: '',
     zipCode: '',
     state: '',
     city: '',
     address: ''
   });
   const [showAddForm, setShowAddForm] = useState(false);
   const [editingAddressId, setEditingAddressId] = useState(null);
   const [isAddressDialogOpen, setIsAddressDialogOpen] = useState(false);
   const [addressErrors, setAddressErrors] = useState({
     country: '',
     zipCode: '',
     state: '',
     city: '',
     address: ''
   });
   const [editAddressErrors, setEditAddressErrors] = useState({
     country: '',
     zipCode: '',
     state: '',
     city: '',
     address: ''
   });
   const hasInitialized = useRef(false);

   const fetchInitialData = async () => {
     if (!auth.user?.access_token || hasInitialized.current) return;
     
     hasInitialized.current = true;
     try {
       const [profileData, addressesData] = await Promise.all([
         getProfile(auth.user.access_token),
         getAllAddresses(auth.user.access_token)
       ]);
       
       setProfile(profileData);
       setAddresses(addressesData);
     } catch (error) {
       console.error('Error fetching data:', error);
     }
   };

   useEffect(() => {
     fetchInitialData();
   }, []);

   const handleEdit = (section: string, addressId?: string) => {
     if (section === 'address' && addressId) {
       const addressToEdit = addresses.find(addr => addr.id === addressId);
       if (addressToEdit) {
         setEditAddress({
           country: addressToEdit.country,
           zipCode: addressToEdit.postalCode || addressToEdit.zipCode,
           state: addressToEdit.state,
           city: addressToEdit.city,
           address: addressToEdit.streetAddress || addressToEdit.address
         });
         setEditingAddressId(addressId);
       }
     } else {
       setTempData({ ...profileData });
     }
     setEditSection(section);
   };

   const handleCancel = () => {
     setEditSection(null);
     setEditingAddressId(null);
     setEditAddressErrors({ country: '', zipCode: '', state: '', city: '', address: '' });
   };

   const validateEditAddress = () => {
     const errors = {
       country: '',
       zipCode: '',
       state: '',
       city: '',
       address: ''
     };
     
     if (!editAddress.country.trim()) errors.country = 'Country is required';
     if (!editAddress.zipCode.trim()) errors.zipCode = 'Zip code is required';
     if (!editAddress.state.trim()) errors.state = 'State is required';
     if (!editAddress.city.trim()) errors.city = 'City is required';
     if (!editAddress.address.trim()) errors.address = 'Street address is required';
     
     setEditAddressErrors(errors);
     return Object.values(errors).every(error => error === '');
   };

   const handleSave = async (section: string) => {
     if (section === 'address') {
       if (!validateEditAddress()) {
         return;
       }
       
       try {
         const addressToUpdate = {
           country: editAddress.country,
           zipCode: editAddress.zipCode,
           state: editAddress.state,
           city: editAddress.city,
           address: editAddress.address
         };
         await updateAddress(addressToUpdate, auth.user?.access_token, editingAddressId);
         const fetchedAddresses = await getAllAddresses(auth.user?.access_token);
         setAddresses(fetchedAddresses);
         setEditAddressErrors({ country: '', zipCode: '', state: '', city: '', address: '' });
         setEditSection(null);
         toast.success('Address updated successfully');
       } catch (error) {
         console.error('Error updating address:', error);
         toast.error('Failed to update address');
       }
     } else if (section === 'profile') {
       try {
         const profileToUpdate = {
           name: tempName,
           phone: tempData.phone,
           email : auth.user.profile.email
         };
         await updateProfile(profileToUpdate, auth.user?.access_token , user.id);
         setProfileData({ ...tempData });
         setEditSection(null);
         toast.success('Profile updated successfully');
         // Refresh profile data
         const refreshedProfile = await getProfile(auth.user?.access_token);
         setProfile(refreshedProfile);
       } catch (error) {
         console.error('Error updating profile:', error);
         toast.error('Failed to update profile');
       }
     } else {
       setProfileData({ ...tempData });
       setEditSection(null);
       toast.success(`${section} updated successfully`);
     }
   };

   const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
     const { name, value } = e.target;
     setTempData(prev => ({ ...prev, [name]: value }));
   };

   const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
     const { name, value } = e.target;
     setNewAddress(prev => ({ ...prev, [name]: value }));
   };

   const handleEditAddressChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
     const { name, value } = e.target;
     setEditAddress(prev => ({ ...prev, [name]: value }));
   };

   const handleAddAddress = () => {
     setNewAddress({ country: '', zipCode: '', state: '', city: '', address: '' });
     setIsAddressDialogOpen(true);
   };

   const validateAddress = () => {
     const errors = {
       country: '',
       zipCode: '',
       state: '',
       city: '',
       address: ''
     };
     
     if (!newAddress.country.trim()) errors.country = 'Country is required';
     if (!newAddress.zipCode.trim()) errors.zipCode = 'Zip code is required';
     if (!newAddress.state.trim()) errors.state = 'State is required';
     if (!newAddress.city.trim()) errors.city = 'City is required';
     if (!newAddress.address.trim()) errors.address = 'Street address is required';
     
     setAddressErrors(errors);
     return Object.values(errors).every(error => error === '');
   };

   const handleSaveAddress = async () => {
     if (!validateAddress()) {
       return;
     }
     
     try {
       await addAddress(newAddress, auth.user?.access_token);
       const fetchedAddresses = await getAllAddresses(auth.user?.access_token);
       setAddresses(fetchedAddresses);
       setNewAddress({ country: '', zipCode: '', state: '', city: '', address: '' });
       setAddressErrors({ country: '', zipCode: '', state: '', city: '', address: '' });
       setIsAddressDialogOpen(false);
       toast.success('Address added successfully');
     } catch (error) {
       console.error('Error adding address:', error);
       toast.error('Failed to add address');
     }
   };

   const handleCancelAdd = () => {
     setNewAddress({ country: '', zipCode: '', state: '', city: '', address: '' });
     setAddressErrors({ country: '', zipCode: '', state: '', city: '', address: '' });
     setIsAddressDialogOpen(false);
   };

   const handleDeleteAddress = async (addressId: string) => {
     try {
       await deleteAddress(auth.user?.access_token ,addressId);
       const fetchedAddresses = await getAllAddresses(auth.user?.access_token);
       setAddresses(fetchedAddresses);
       toast.success('Address deleted successfully');
     } catch (error) {
       console.error('Error deleting address:', error);
       toast.error('Failed to delete address');
     }
   };

   return (
     <div className="min-h-screen bg-gray-50">
       {/* Header */}
       <div className="p-4 border-b bg-white">
         <div className="flex items-center gap-2">
           <Link to="/dashboard" className="flex items-center">
             <ArrowLeft className="h-5 w-5 text-gray-700" />
           </Link>
           <h1 className="text-lg font-medium">Profile</h1>
         </div>
       </div>

       <div className="container mx-auto max-w-4xl py-6 px-4">
         <div className="grid grid-cols-12 gap-6">
           {/* Sidebar */}
           <div className="col-span-12 md:col-span-3">
             <ProfileSidebar activePage="profile" />
           </div>

           {/* Main Content */}
           <div className="col-span-12 md:col-span-9 space-y-6">
             <Card className="overflow-hidden">
               <div className="p-6">
                 <div className="flex justify-between items-center mb-6">
                   <h2 className="text-xl font-semibold">My Profile</h2>
                   {editSection === 'profile' ? (
                     <div className="flex items-center gap-2">
                       <Button
                         variant="outline"
                         size="sm"
                         className="flex items-center gap-1 text-red-600 border-red-600 hover:bg-red-50"
                         onClick={handleCancel}
                       >
                         <X className="h-4 w-4" />
                         Cancel
                       </Button>
                       <Button
                         size="sm"
                         className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                         onClick={() => handleSave('profile')}
                       >
                         <Check className="h-4 w-4" />
                         Save
                       </Button>
                     </div>
                   ) : (
                     <Button
                       variant="outline"
                       size="sm"
                       className="flex items-center gap-2 text-purple-600 border-purple-600 hover:bg-purple-50"
                       onClick={() => handleEdit('profile')}
                     >
                       <Pencil className="h-4 w-4" />
                       Edit
                     </Button>
                   )}
                 </div>

                 {editSection === 'profile' ? (
                   <div className="flex flex-col md:flex-row gap-4 md:gap-6 items-start md:items-center">
                     <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                       {user?.avatar ? (
                         <img
                           src={user.avatar}
                           alt={user?.name}
                           className="h-full w-full rounded-full object-cover"
                         />
                       ) : (
                         <span className="text-2xl font-semibold text-gray-600">
                           {tempName.charAt(0)}
                         </span>
                       )}
                     </div>
                     <div className="flex-1">
                       <div className="flex items-center gap-2 mb-4">
                         <Badge className="bg-green-100 text-green-800 hover:bg-green-200 font-normal">
                           Active
                         </Badge>
                       </div>
                       <div className="grid grid-cols-2 gap-4">
                         <div>
                           <p className="text-sm text-gray-500 mb-1">Full Name</p>
                           <Input 
                             value={tempName}
                             className="edu-form-field"
                
                             onChange={(e) => setTempName(e.target.value)}
                           />
                         </div>
                         <div>
                           <p className="text-sm text-gray-500 mb-1">Phone Number</p>
                           <Input 
                             name="phone"
                             className="edu-form-field"
                
                             value={tempData.phone}
                             onChange={handleChange}
                           />
                         </div>
                       </div>
                       <div className="mt-4">
                         <p className="text-sm text-gray-500 mb-1">Email ID</p>
                         <Input 
                           className="edu-form-field"
                
                           value={auth.user.profile.email }
                           disabled
                         />
                       </div>
                     </div>
                   </div>
                 ) : (
                   <div className="flex flex-col md:flex-row gap-4 md:gap-6 items-start md:items-center">
                     <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                       {user?.avatar ? (
                         <img
                           src={user.avatar}
                           alt={user?.name}
                           className="h-full w-full rounded-full object-cover"
                         />
                       ) : (
                         <span className="text-2xl font-semibold text-gray-600">
                           {user?.name.charAt(0)}
                         </span>
                       )}
                     </div>
                     <div>
                       <div className="flex items-center gap-2 mb-4">
                         <h3 className="text-lg font-medium">
                           Mr. {user?.name || "John Doe"}
                         </h3>
                         <Badge className="bg-green-100 text-green-800 hover:bg-green-200 font-normal ml-2">
                           Active
                         </Badge>
                       </div>
                       <div className="grid grid-cols-2 md:grid-cols-2 gap-y-6 gap-x-12">
                     <div>
                       <p className="text-sm text-gray-500 mb-1">Phone Number</p>
                       <p className="font-medium">{profileData.phone}</p>
                     </div>
                     <div>
                       <p className="text-sm text-gray-500 mb-1">Email ID</p>
                       <p className="font-medium">{auth.user.profile.email || '-'}</p>
                     </div>
                     
                   </div>
                     </div>
                   </div>
                 )}
               </div>
             </Card>

         
             <Card className="overflow-hidden">
               <div className="p-6">
                 <div className="flex justify-between items-center mb-6">
                   <h2 className="text-xl font-semibold">Address</h2>
                   {editSection === 'address' ? (
                     <div className="flex items-center gap-2">
                       <Button
                         variant="outline"
                         size="sm"
                         className="flex items-center gap-1 text-red-600 border-red-600 hover:bg-red-50"
                         onClick={handleCancel}
                       >
                         <X className="h-4 w-4" />
                         Cancel
                       </Button>
                       <Button
                         size="sm"
                         className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                         onClick={() => handleSave('address')}
                       >
                         <Check className="h-4 w-4" />
                         Save
                       </Button>
                     </div>
                   ) : (
                     <div className="flex items-center gap-2">
                       {addresses.length === 0 && (
                         <Button
                           variant="outline"
                           className="flex items-center gap-2 text-blue-600 border-blue-600 hover:bg-blue-50"
                           onClick={handleAddAddress}
                         >
                           <Plus className="h-4 w-4" />
                           Add
                         </Button>
                       )}
                     </div>
                   )}
                 </div>

                 {editSection === 'address' ? (
                   <div className="grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-12">
                     <div>
                       <p className="text-sm text-gray-500 mb-1">Country*</p>
                       <Input 
                         name="country"
                         value={editAddress.country}
                         onChange={handleEditAddressChange}
                         className={editAddressErrors.country ? 'border-red-500' : "edu-form-field"}
                       />
                       {editAddressErrors.country && <p className="text-red-500 text-xs mt-1">{editAddressErrors.country}</p>}
                     </div>
                     <div>
                       <p className="text-sm text-gray-500 mb-1">Zip Code*</p>
                       <Input 
                         name="zipCode"
                         value={editAddress.zipCode}
                         onChange={handleEditAddressChange}
                         className={editAddressErrors.zipCode ? 'border-red-500' : "edu-form-field"}
                       />
                       {editAddressErrors.zipCode && <p className="text-red-500 text-xs mt-1">{editAddressErrors.zipCode}</p>}
                     </div>
                     <div>
                       <p className="text-sm text-gray-500 mb-1">State*</p>
                       <Input 
                         name="state"
                         value={editAddress.state}
                         onChange={handleEditAddressChange}
                         className={editAddressErrors.state ? 'border-red-500' : "edu-form-field"}
                       />
                       {editAddressErrors.state && <p className="text-red-500 text-xs mt-1">{editAddressErrors.state}</p>}
                     </div>
                     <div>
                       <p className="text-sm text-gray-500 mb-1">City*</p>
                       <Input 
                         name="city"
                         value={editAddress.city}
                         onChange={handleEditAddressChange}
                         className={editAddressErrors.city ? 'border-red-500' : "edu-form-field"}
                       />
                       {editAddressErrors.city && <p className="text-red-500 text-xs mt-1">{editAddressErrors.city}</p>}
                     </div>
                     <div className="md:col-span-2">
                       <p className="text-sm text-gray-500 mb-1">Address*</p>
                       <Textarea 
                         name="address"
                         value={editAddress.address}
                         onChange={handleEditAddressChange}
                         className={editAddressErrors.address ? 'border-red-500' : "edu-form-field"}
                       />
                       {editAddressErrors.address && <p className="text-red-500 text-xs mt-1">{editAddressErrors.address}</p>}
                     </div>
                   </div>
                 ) : (
                   <div className="space-y-6">

                     {addresses.length > 0 ? (
                       addresses.map((addr, index) => (
                         <div key={index} className="relative grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-12 p-4  rounded-lg">
                           <div className="absolute top-2 right-2 flex gap-1">
                             <Button
                               size="sm"
                               variant="outline"
                                onClick={() => handleEdit('address', addr.id)}
                               className="h-8 w-8 p-0 text-purple-600 border-purple-600 hover:bg-purple-50"
                             >
                               <Pencil className="h-3 w-3" />
                             </Button>
                             <Button
                               size="sm"
                               variant="outline"
                               className="h-8 w-8 p-0 text-red-600 border-red-600 hover:bg-red-50"
                               onClick={() => handleDeleteAddress(addr.id)}
                             >
                               <Trash2 className="h-3 w-3" />
                             </Button>
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Country</p>
                             <p className="font-medium">{addr.country}</p>
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">Zip Code</p>
                             <p className="font-medium">{addr.zipCode}</p>
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">State</p>
                             <p className="font-medium">{addr.state}</p>
                           </div>
                           <div>
                             <p className="text-sm text-gray-500 mb-1">City</p>
                             <p className="font-medium">{addr.city}</p>
                           </div>
                           <div className="md:col-span-2">
                             <p className="text-sm text-gray-500 mb-1">Address</p>
                             <p className="font-medium">{addr.address}</p>
                           </div>
                         </div>
                       ))
                     ) : (
                       <p className="text-gray-500">No addresses found</p>
                     )}
                   </div>
                 )}
               </div>
             </Card>

           </div>
         </div>
       </div>
       
       <Dialog open={isAddressDialogOpen} onOpenChange={setIsAddressDialogOpen}>
         <DialogContent className="sm:max-w-[500px]">
           <DialogHeader>
             <DialogTitle>Add New Address</DialogTitle>
           </DialogHeader>
           <div className="grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-12 py-4">
             <div>
               <p className="text-sm text-gray-500 mb-1">Country*</p>
               <Input 
                 name="country"
                 value={newAddress.country}
                 onChange={handleAddressChange}
                 className={addressErrors.country ? 'border-red-500' : "edu-form-field"}
               />
               {addressErrors.country && <p className="text-red-500 text-xs mt-1">{addressErrors.country}</p>}
             </div>
             <div>
               <p className="text-sm text-gray-500 mb-1">Zip Code*</p>
               <Input 
                 name="zipCode"
                 value={newAddress.zipCode}
                 onChange={handleAddressChange}
                 className={addressErrors.zipCode ? 'border-red-500' : "edu-form-field"}
               />
               {addressErrors.zipCode && <p className="text-red-500 text-xs mt-1">{addressErrors.zipCode}</p>}
             </div>
             <div>
               <p className="text-sm text-gray-500 mb-1">State*</p>
               <Input 
                 name="state"
                 value={newAddress.state}
                 onChange={handleAddressChange}
                 className={addressErrors.state ? 'border-red-500' : "edu-form-field"}
               />
               {addressErrors.state && <p className="text-red-500 text-xs mt-1">{addressErrors.state}</p>}
             </div>
             <div>
               <p className="text-sm text-gray-500 mb-1">City*</p>
               <Input 
                 name="city"
                 value={newAddress.city}
                 onChange={handleAddressChange}
                 className={addressErrors.city ? 'border-red-500' : "edu-form-field"}
               />
               {addressErrors.city && <p className="text-red-500 text-xs mt-1">{addressErrors.city}</p>}
             </div>
             <div className="md:col-span-2">
               <p className="text-sm text-gray-500 mb-1">Street Address*</p>
               <Textarea 
               
                 name="address"
                 value={newAddress.address}
                 onChange={handleAddressChange}
                 className={addressErrors.address ? 'border-red-500' : 'edu-form-field'}
               />
               {addressErrors.address && <p className="text-red-500 text-xs mt-1">{addressErrors.address}</p>}
             </div>
           </div>
           <DialogFooter>
             <Button
               variant="outline"
               onClick={handleCancelAdd}
               className="text-red-600 border-red-600 hover:bg-red-50"
             >
               Cancel
             </Button>
             <Button
               onClick={handleSaveAddress}
               className="bg-green-600 hover:bg-green-700"
             >
               Save Address
             </Button>
           </DialogFooter>
         </DialogContent>
       </Dialog>
     </div>
   );
 }
