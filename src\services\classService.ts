
//import classesData from '../data/classes.json';

export interface ClassData {
  id: string;
  classroomId: string;
  className: string;
  subjectName: string;
  image: string;
  batchName: string;
  level: string;
  description: string;
  progress: number;
  currentEnrollment: number;
  teacherId: string;
  capacity: number;
  joinCode: string;
  createdDate: string;
  endDate: string;
  classType: string;
  totalSchedules:number;
  teacherName: string;
  pendingSchedules :number;
  posterFileId: string;
  imageKey: number;
  completed?: boolean;

}
export async function getClassbyJoinCode(accessToken: string,joincode: string){
  const response = await fetch(`/api/classroomManagement/v1/classrooms/code/${joincode}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (!response.ok) {
    throw new Error("Failed to fetch students");
  }
  console.log("Response from getClassbyJoinCode:", response);
  return response.json();
}


export async function getClassesForStudent(accessToken: string,
  page: number = 0,
  pageSize: number = 10
  ) {
  const response = await fetch('/api/classroomManagement/v1/student/enrolled-classes',
  { 
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch classes");
  }
  return response.json();
}



export async function getClassesFromBackend(accessToken: string,
  page: number = 0,
  pageSize: number = 10
  ) {
  const response = await fetch(`/api/classroomManagement/v1/classrooms/teacher?page=${page}&pageSize=${pageSize}`,
  { 
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch classes");
  }
  return response.json();
}
export async function getClassOverViewForTeacher(classId: string, accessToken: string) {
  const response = await fetch(`/api/classroom/v1/classes/${classId}/overview`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch class by id");
  }
  return response.json();
}


export async function getClassOverViewForStudent(classId: string, accessToken: string) {
  const response = await fetch(`/api/classroom/v1/classes/${classId}/student-overview`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch class by id");
  }
  return response.json();
}

export async function getClassById(id: string, accessToken: string): Promise<ClassData> {
  const response = await fetch(`/api/classroomManagement/v1/classrooms/${id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch class by id");
  }
  return response.json();
}


export async function createClassService(newClass: any, accessToken: string) {

  const response = await fetch("/api/classroomManagement/v1/classrooms", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(newClass)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create class");
  }
  return response.json();
}
export async function MarkClassAsComplete(accessToken: string,classId: string) {

  const response = await fetch(`/api/classroomManagement/v1/classrooms/${classId}/complete`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify({})
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create class");
  }
  return response.json();
}

export async function getCountryCodesMetadata(accessToken: string) {
  const response = await fetch("/api/common/v1/countries", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch country codes metadata");
  }
  return response.json();
}

export async function updateClassOnBackend(accessToken: string, classId: string, classData: any) {
  const response = await fetch(`/api/classroomManagement/v1/classrooms/${classId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(classData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    const errorData = await response.json();
    throw errorData;
  }
  return response.json();
}

export async function addStudentToClassByTeacher(accessToken: string, classroomId: string, studentId: string) {
  const response = await fetch(`/api/enrollmentManagement/v1/teacher/enroll/classroom/{classroomId}/student/{studentId}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to add student to class by teacher");
  }
  return response.json();
}

export async function createClassNotes( accessToken: string,classId:string,notes: any) {
 const req ={
  "classId": classId,
  "title": "Important Announcement",
  "content": notes
};
  const response = await fetch("/api/classroom/v1/notes", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(req)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create notes");
  }
  return response.json();
}
export async function getClassNotes(accessToken: string,classId) {
  const response = await fetch(`/api/classroom/v1/classes/${classId}/notes`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch country codes metadata");
  }
  return response.json();
}

export async function updateClassNotes(accessToken: string, classId: string, notes: any,notesId:string) {
 const req={
  "classId": classId,
  "title": "Important Announcement",
  "content": notes
};
 
  const response = await fetch(`/api/classroom/v1/notes/${notesId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(req)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    const errorData = await response.json();
    throw errorData;
  }
  return response.json();
}
