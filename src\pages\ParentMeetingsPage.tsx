
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { Calendar, Clock, User, MessageSquare, ChevronLeft } from "lucide-react";
import { useAuth } from "react-oidc-context";
import { useApp } from "@/context/AppContext";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { MeetingSlot, UserRole } from "@/types";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useSelector } from "react-redux";
import { useUserRole } from "@/hooks/useUserRole";

const ParentMeetingsPage = () => {
  const navigate = useNavigate();
  const  user = useAuth().user;
  const { classes, meetings, bookMeetingSlot, cancelMeetingSlot } = useApp();
  
  const [selectedMeetingId, setSelectedMeetingId] = useState<string | null>(null);
  const [selectedSlotId, setSelectedSlotId] = useState<string | null>(null);
  const [selectedStudentId, setSelectedStudentId] = useState<string>("");
  const { selectedRole } = useUserRole();

  const getDashboardRoute = () => {
    
    switch (selectedRole) {
      case UserRole.TEACHER:
        return "/teacher-dashboard";
      case UserRole.PARENT:
        return "/parent-dashboard";
      case UserRole.STUDENT:
        return "/student-dashboard";
      default:
        return "/";
    }
  };
  
  const activeMeetings = meetings.filter(meeting => meeting.isActive);
  
  const parentMeetings = activeMeetings.filter(meeting => {
    const parentClass = classes.find(c => c.id === meeting.classId);
    return parentClass && parentClass.parents.includes(user?.profile.sub || "");
  });
  
  const bookedMeetings = activeMeetings.map(meeting => {
    const bookedSlots = meeting.slots.filter(slot => slot.bookedBy === user?.profile.sub);
    return { meeting, bookedSlots };
  }).filter(item => item.bookedSlots.length > 0);
  
  const getStudentsForParent = () => {
    const studentsMap = new Map();
    
    classes.forEach(classItem => {
      if (classItem.parents.includes(user?.profile.sub || "")) {
        classItem.students.forEach(studentId => {
          if (!studentsMap.has(studentId)) {
            studentsMap.set(studentId, {
              id: studentId,
              name: studentId === "2" ? "John Doe" : `Student ${studentId}`
            });
          }
        });
      }
    });
    
    return Array.from(studentsMap.values());
  };
  
  const students = getStudentsForParent();
  
  const handleBookMeeting = () => {
    if (selectedMeetingId && selectedSlotId && selectedStudentId) {
      bookMeetingSlot(selectedMeetingId, selectedSlotId, user?.profile.sub || "", selectedStudentId);
      
      setSelectedMeetingId(null);
      setSelectedSlotId(null);
      setSelectedStudentId("");
    }
  };
  
  const handleCancelBooking = (meetingId: string, slotId: string) => {
    cancelMeetingSlot(meetingId, slotId);
  };
  
  const getMeetingClass = (classId: string) => {
    return classes.find(c => c.id === classId);
  };
  
  const upcomingMeetings = [
    {
      id: "m1",
      teacherName: "Ms. Sarah Johnson",
      subject: "Mathematics",
      date: "2025-04-25",
      time: "14:00",
      status: "confirmed"
    },
    {
      id: "m2",
      teacherName: "Mr. David Wilson",
      subject: "Science",
      date: "2025-04-26",
      time: "15:30",
      status: "pending"
    }
  ];

  return (
    <div className="min-h-screen bg-slate-50">
      <DashboardHeader variant="parent" showBackButton={true} />
      <div className="container mx-auto px-4 py-6">
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-slate-800">Parent-Teacher Meetings</h1>
              <p className="text-slate-600 mt-1">Schedule and manage your parent-teacher conferences</p>
            </div>
            <Button 
              onClick={() => navigate(getDashboardRoute())}
              variant="outline"
              className="w-full sm:w-auto"
            >
              Back to Dashboard
            </Button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <Calendar className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-slate-600">Upcoming Meetings</p>
                    <p className="text-2xl font-semibold text-slate-800">3</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <Clock className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-slate-600">Total Hours</p>
                    <p className="text-2xl font-semibold text-slate-800">2.5</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <User className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-slate-600">Teachers Met</p>
                    <p className="text-2xl font-semibold text-slate-800">4</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-amber-100 rounded-lg">
                    <MessageSquare className="h-6 w-6 text-amber-600" />
                  </div>
                  <div>
                    <p className="text-sm text-slate-600">Follow-ups</p>
                    <p className="text-2xl font-semibold text-slate-800">2</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Alert className="mt-6 bg-blue-50 border-blue-200">
            <AlertTitle className="text-blue-800">Next Meeting Reminder</AlertTitle>
            <AlertDescription className="text-blue-700">
              Your next meeting with Ms. Sarah Johnson is scheduled for Friday, April 25th at 2:00 PM.
            </AlertDescription>
          </Alert>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="text-xl">Upcoming Meetings</CardTitle>
              <CardDescription>Your scheduled conferences</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingMeetings.map(meeting => (
                  <div key={meeting.id} className="p-4 rounded-lg border bg-white hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-medium text-slate-800">{meeting.teacherName}</h3>
                        <p className="text-sm text-slate-600">{meeting.subject}</p>
                      </div>
                      <Badge variant={meeting.status === 'confirmed' ? 'default' : 'secondary'}>
                        {meeting.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-slate-600 mt-2">
                      <Calendar className="h-4 w-4" />
                      <span>{format(new Date(meeting.date), "MMMM d, yyyy")}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-slate-600 mt-1">
                      <Clock className="h-4 w-4" />
                      <span>{meeting.time}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="lg:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Available Meeting Slots</h2>
            
            {parentMeetings.length === 0 ? (
              <Card>
                <CardContent className="p-6">
                  <p className="text-center text-gray-500">There are no available meetings at this time.</p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                {parentMeetings.map(meeting => {
                  const classInfo = getMeetingClass(meeting.classId);
                  const availableSlots = meeting.slots.filter(slot => !slot.isBooked);
                  
                  return (
                    <Card key={meeting.id} className="hover:shadow-lg transition-shadow duration-300">
                      <CardHeader>
                        <CardTitle>{meeting.title}</CardTitle>
                        <CardDescription>
                          Class: {classInfo?.className} • {format(new Date(meeting.date), "MMMM d, yyyy")}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        {meeting.description && (
                          <p className="text-gray-700 mb-4">{meeting.description}</p>
                        )}
                        
                        {availableSlots.length === 0 ? (
                          <div className="text-center py-2">
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                              No available slots
                            </Badge>
                          </div>
                        ) : (
                          <Collapsible>
                            <CollapsibleTrigger asChild>
                              <Button variant="outline" className="w-full flex justify-between">
                                <span>{availableSlots.length} Available Time Slots</span>
                                <Calendar className="h-4 w-4" />
                              </Button>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4">
                                {availableSlots.map(slot => (
                                  <Dialog key={slot.id}>
                                    <DialogTrigger asChild>
                                      <Button
                                        variant="outline"
                                        className="flex flex-col items-start h-auto py-2"
                                        onClick={() => {
                                          setSelectedMeetingId(meeting.id);
                                          setSelectedSlotId(slot.id);
                                        }}
                                      >
                                        <span className="text-sm font-medium">
                                          {format(new Date(slot.startTime), "h:mm a")}
                                        </span>
                                        <span className="text-xs text-gray-500">
                                          to {format(new Date(slot.endTime), "h:mm a")}
                                        </span>
                                      </Button>
                                    </DialogTrigger>
                                    <DialogContent>
                                      <DialogHeader>
                                        <DialogTitle>Book Meeting Slot</DialogTitle>
                                        <DialogDescription>
                                          Select which student this meeting is for:
                                        </DialogDescription>
                                      </DialogHeader>
                                      
                                      <div className="py-4">
                                        <p className="mb-2 text-sm font-medium">Meeting Details:</p>
                                        <div className="bg-gray-50 p-3 rounded mb-4">
                                          <p><span className="font-medium">Class:</span> {classInfo?.className}</p>
                                          <p><span className="font-medium">Date:</span> {format(new Date(meeting.date), "MMMM d, yyyy")}</p>
                                          <p><span className="font-medium">Time:</span> {format(new Date(slot.startTime), "h:mm a")} - {format(new Date(slot.endTime), "h:mm a")}</p>
                                        </div>
                                        
                                        <div className="space-y-3">
                                          <label className="text-sm font-medium">Select Student:</label>
                                          <Select value={selectedStudentId} onValueChange={setSelectedStudentId}>
                                            <SelectTrigger>
                                              <SelectValue placeholder="Choose a student" />
                                            </SelectTrigger>
                                            <SelectContent>
                                              {students.map(student => (
                                                <SelectItem key={student.id} value={student.id}>
                                                  {student.name}
                                                </SelectItem>
                                              ))}
                                            </SelectContent>
                                          </Select>
                                        </div>
                                      </div>
                                      
                                      <DialogFooter>
                                        <Button 
                                          onClick={handleBookMeeting} 
                                          disabled={!selectedStudentId}
                                          className="w-full sm:w-auto"
                                        >
                                          Book Meeting
                                        </Button>
                                      </DialogFooter>
                                    </DialogContent>
                                  </Dialog>
                                ))}
                              </div>
                            </CollapsibleContent>
                          </Collapsible>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParentMeetingsPage;
