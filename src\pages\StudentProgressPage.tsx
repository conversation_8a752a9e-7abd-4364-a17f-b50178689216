import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from  "react-oidc-context";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { Card, CardContent } from "@/components/ui/card";
import { GraduationCap, BookOpen, ListCheck, BarChart, ChevronLeft } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { UserRole } from "@/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getParentStudents } from "@/services/parentService";
import { Student } from "@/services/studentService";
import { useSelector } from "react-redux";
import { useUserRole } from "@/hooks/useUserRole";

export default function StudentProgressPage() {
  const  user  = useAuth().user;
  const navigate = useNavigate();
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedStudentId, setSelectedStudentId] = useState<string>();
  const { selectedRole } = useUserRole();

  useEffect(() => {
    const loadStudents = async () => {
      if (selectedRole === UserRole.PARENT) {
        const loadedStudents = await getParentStudents(user.profile.sub);
        setStudents(loadedStudents);
        if (loadedStudents.length > 0) {
          setSelectedStudentId(loadedStudents[0].id);
        }
      }
    };
    
    loadStudents();
  }, [user]);

  const handleBackToDashboard = () => {
    switch (selectedRole) {
      case UserRole.PARENT:
        navigate('/parent-dashboard');
        break;
      case UserRole.STUDENT:
        navigate('/student-dashboard');
        break;
      default:
        navigate('/dashboard');
    }
  };

  const progressData = {
    overall: 86.5,
    attendance: 98,
    assignments: 92,
    quizzes: 85,
    participation: 89
  };

  const subjectProgress = [
    { subject: "Mathematics", progress: 88, grade: "A" },
    { subject: "Science", progress: 92, grade: "A+" },
    { subject: "English", progress: 85, grade: "B+" },
    { subject: "History", progress: 90, grade: "A" }
  ];

  const selectedStudent = students.find(s => s.id === selectedStudentId);

  return (
    <div className="min-h-screen bg-slate-50">
      <DashboardHeader variant={selectedRole === UserRole.PARENT ? "parent" : "student"} />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center">
            <Button 
              variant="ghost" 
              className="mr-4" 
              onClick={handleBackToDashboard}
            >
              <ChevronLeft className="h-5 w-5 mr-2" /> 
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-slate-800 mb-2">Student Progress Overview</h1>
              <p className="text-slate-600">Track your child's academic performance and progress</p>
            </div>
          </div>
          
          {selectedRole=== UserRole.PARENT && students.length > 0 && (
            <div className="w-[200px]">
              <Select
                value={selectedStudentId}
                onValueChange={setSelectedStudentId}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select student" />
                </SelectTrigger>
                <SelectContent>
                  {students.map((student) => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.studentName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {selectedStudent ? (
          <>
            {/* Overall Progress */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <Card className="bg-white">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
                      <GraduationCap className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-slate-800">{selectedStudent.studentName}'s Performance</h2>
                      <p className="text-slate-500">Academic Year 2024-2025</p>
                    </div>
                  </div>
                  <div className="mb-4">
                    <div className="text-3xl font-bold text-slate-800">{selectedStudent.progress}%</div>
                    <div className="h-2 bg-gray-200 rounded-full mt-2">
                      <div 
                        className="h-2 bg-purple-600 rounded-full" 
                        style={{ width: `${selectedStudent.progress}%` }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-6">
                  <h2 className="text-lg font-semibold text-slate-800 mb-4">Key Metrics</h2>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <ListCheck className="h-5 w-5 text-blue-600" />
                        <span className="font-medium">Attendance</span>
                      </div>
                      <div className="text-2xl font-bold text-blue-600">{progressData.attendance}%</div>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <BookOpen className="h-5 w-5 text-green-600" />
                        <span className="font-medium">Assignments</span>
                      </div>
                      <div className="text-2xl font-bold text-green-600">{progressData.assignments}%</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Subject-wise Progress */}
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-slate-800 mb-4">Subject Performance</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {subjectProgress.map((subject) => (
                  <Card key={subject.subject} className="bg-white">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <BarChart className="h-5 w-5 text-slate-600" />
                          <h3 className="font-medium">{subject.subject}</h3>
                        </div>
                        <span className="text-sm font-semibold bg-purple-100 text-purple-600 px-2 py-1 rounded">
                          {subject.grade}
                        </span>
                      </div>
                      <div className="h-2 bg-gray-200 rounded-full">
                        <div 
                          className="h-2 bg-purple-600 rounded-full" 
                          style={{ width: `${subject.progress}%` }}
                        />
                      </div>
                      <div className="mt-1 text-sm text-slate-600">{subject.progress}% Complete</div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </>
        ) : (
          <Card className="p-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Student Data Available</h3>
            <p className="text-gray-500">Please select a student to view their progress.</p>
          </Card>
        )}
      </div>
    </div>
  );
}
