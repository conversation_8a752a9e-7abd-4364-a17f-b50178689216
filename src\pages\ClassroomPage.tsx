
import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { 
  ArrowLeft, 
  Video, 
  MicOff, 
  Mic, 
  ScreenShare, 
  Users, 
  MessageSquare, 
  PenTool,
  Monitor,
  Settings,
  X,
  Check,
  Share2,
  Calendar,
  Clock
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card"; 
import { useAuth } from "react-oidc-context";
import { useApp } from "@/context/AppContext";
import { toast } from "sonner";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { <PERSON><PERSON>, <PERSON>, Smartphone } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { SyllabusTopicList, type SyllabusTopic } from "@/components/syllabus/SyllabusTopicList";
import { SyllabusTopicForm } from "@/components/syllabus/SyllabusTopicForm";
import { getSchedulesByClassId } from "@/services/scheduleService";
import { getClassById, ClassData,getClassesFromBackend } from "@/services/classService";
import { ScheduleEvent, UserRole } from "@/types";
import { format } from "date-fns";
import { getAllSyllabus } from "@/services/syllabusService";
import { generateAvatarUrl } from "@/lib/utils";
import { useSelector } from "react-redux";
import { useUserRole } from "@/hooks/useUserRole";
import { Student, getEnrolledStudentsForClass} from "@/services/studentService";
import { JaaSMeeting, JitsiMeeting } from '@jitsi/react-sdk';

const ClassroomPage = () => {
  const { classId } = useParams<{ classId: string }>();
  
  const { classes, shareClass } = useApp();
  const auth = useAuth();
  const navigate = useNavigate();
 
  const [activeTab, setActiveTab] = useState("video");
  const [bottomPanelOpen, setBottomPanelOpen] = useState(false);
  const [bottomActiveTab, setBottomActiveTab] = useState("participants");
  const [videoEnabled, setVideoEnabled] = useState(true);
  const [micEnabled, setMicEnabled] = useState(true);
  const [screenShareEnabled, setScreenShareEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [syllabusTopics, setSyllabusTopics] = useState<SyllabusTopic[]>([]);
      const [availableClasses, setAvailableClasses] = useState<ClassData[]>([]);
  
  useEffect(() => {
    fetchSyllabus();
  }, []);
    
  const fetchSyllabus = async () => {
    try {
      setIsLoading(true);
      const data = await getAllSyllabus(auth.user.access_token,classId);
      setSyllabusTopics(data);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching announcements:", error);
      toast.error("Failed to load announcements");
      setIsLoading(false);
    }
  };
  const apiRef = useRef<any>();
  
  const { selectedRole: userRole } = useUserRole();
  const isTeacher = userRole === UserRole.TEACHER;
  const [isAddTopicOpen, setIsAddTopicOpen] = useState(false);
  const [schedules, setSchedules] = useState<ScheduleEvent[]>([]);
  const [isLoadingSchedules, setIsLoadingSchedules] = useState(true);

  useEffect(() => {
    const fetchSchedules = async () => {
      if (classId) {
        try {
          setIsLoadingSchedules(true);
          console.log(isTeacher);
          const data = await getSchedulesByClassId(auth.user.access_token,classId);
          setSchedules(data);
        } catch (error) {
          console.error("Error fetching schedules:", error);
          toast.error("Failed to load schedules");
        } finally {
          setIsLoadingSchedules(false);
        }
      }
    };

    fetchSchedules();
  }, [classId]);
useEffect(() => {
    const fetchClasses = async () => {
      try {
        const classes = await getClassesFromBackend(auth.user.access_token);
        setAvailableClasses(classes.content || []);
      } catch (error) {
        console.error("Error fetching classes:", error);
        setAvailableClasses([]);
      }
    };

    fetchClasses();
    }, []);
  const currentClass = availableClasses.find(c => c.id === classId);
  
  const [students, setStudents] = useState<Student[]>([]);
     // Fetch students for dropdown
     useEffect(() => {
       const fetchStudents = async () => {
         try {
           const data = await getEnrolledStudentsForClass(auth.user.access_token ,classId);
            setStudents(data);
         } catch (error) {
           toast.error("Failed to load students");
         }
       };
       fetchStudents();
     }, [auth.user.access_token]);
   
  const handleGoBack = () => {
    if(isTeacher) {
    navigate("/classes");
    } else {
      navigate("/student-classes");
    } 
   };

  const toggleVideo = () => {
    setVideoEnabled(!videoEnabled);
    toast(videoEnabled ? "Video turned off" : "Video turned on");
  };

  const toggleMic = () => {
    setMicEnabled(!micEnabled);
    toast(micEnabled ? "Microphone muted" : "Microphone unmuted");
  };

  const toggleScreenShare = () => {
    setScreenShareEnabled(!screenShareEnabled);
    toast(screenShareEnabled ? "Screen sharing stopped" : "Screen sharing started");
  };
  const handleChangeSchedule =()=>{
    
    setActiveTab("schedule");
    toast.success("Student admitted to the class");
  };
  const handleAdmitStudent = (studentId: string) => {
    toast.success("Student admitted to the class");
    // Logic to admit student would go here
  };

  const handleDenyStudent = (studentId: string) => {
    toast.error("Student denied access");
    // Logic to deny student would go here
  };

  const handleShare = (method: "email" | "whatsapp" | "copy" | "other") => {
    if (currentClass) {
      shareClass(currentClass.id, method);
    }
  };

  const handleCopyJoinCode = () => {
    if (currentClass) {
      navigator.clipboard.writeText(currentClass.joinCode);
      toast("Join code copied to clipboard!");
    }
  };

  const handleAddTopic = (data: { title: string; description: string }) => {
    const newTopic: SyllabusTopic = {
      id: Date.now().toString(),
      title: data.title,
     content: data.description,
     attachedFiles: [],
     links: [],
      status: 'OPEN'
    };
    
    setSyllabusTopics(prev => [...prev, newTopic]);
    setIsAddTopicOpen(false);
    toast.success("Topic added successfully");
  };

  const handleUpdateTopicStatus = (topicId: string, newStatus: SyllabusTopic['status']) => {
    setSyllabusTopics(prev =>
      prev.map(topic =>
        topic.id === topicId ? { ...topic, status: newStatus } : topic
      )
    );
    toast.success(`Topic status updated to ${newStatus}`);
  };

  if (!currentClass) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Class not found</h1>
          <Button onClick={() => navigate('/teacher-dashboard')}>Return to Dashboard</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col">
      {/* Top Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              className="text-white hover:bg-gray-700" 
              onClick={handleGoBack}
            >
              <ArrowLeft className="mr-2 h-5 w-5" />
              Back
            </Button>
            <div>
              <h1 className="text-lg font-semibold text-white">{currentClass.subjectName}</h1>
              <p className="text-sm text-gray-300">{currentClass.className}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <code className="bg-gray-700 text-white px-3 py-1 rounded text-sm">
              {currentClass.joinCode}
            </code>
            <Button 
              variant="ghost" 
              size="icon" 
              className="text-white hover:bg-gray-700" 
              onClick={handleCopyJoinCode}
            >
              <Copy className="h-4 w-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="text-white hover:bg-gray-700">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleShare("email")}>
                  <Mail className="h-4 w-4 mr-2" />
                  <span>Email</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleShare("whatsapp")}>
                  <Smartphone className="h-4 w-4 mr-2" />
                  <span>WhatsApp</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleShare("copy")}>
                  <Copy className="h-4 w-4 mr-2" />
                  <span>Copy link</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        <Tabs 
          value={activeTab}
          onValueChange={(value) => setActiveTab(value)}
          className="flex-1 flex flex-col"
        >
          <TabsList className="grid w-full max-w-md grid-cols-2 mx-4 mt-4">
            <TabsTrigger value="video">Live Session</TabsTrigger>
            <TabsTrigger value="management">Class Management</TabsTrigger>
          </TabsList>

          <TabsContent value="video" className="flex-1 m-4 mt-2">
            {/* Video Call Area */}
            <div className="rounded-lg flex-1 relative min-h-[60vh]" style={{ height: '60vh' }}>
              <JitsiMeeting
                roomName={currentClass.joinCode}
                configOverwrite={{
                  startWithAudioMuted: !micEnabled,
                  startWithVideoMuted: !videoEnabled,
                  prejoinPageEnabled: false,
                  requireDisplayName: false,
                  enableWelcomePage: false,
                  enableClosePage: false,
                  disableDeepLinking: true,
                  enableUserRolesBasedOnToken: false,
                  enableNoAudioDetection: false,
                  enableNoisyMicDetection: false,
                  startAudioOnly: false,
                  enableLobbyChat: false,
                  enableInsecureRoomNameWarning: false
                }}
                interfaceConfigOverwrite={{
                  TOOLBAR_BUTTONS: ['microphone', 'camera', 'hangup', 'chat', 'participants-pane'],
                  SHOW_JITSI_WATERMARK: false,
                  SHOW_WATERMARK_FOR_GUESTS: false,
                  DISABLE_JOIN_LEAVE_NOTIFICATIONS: true
                }}
                userInfo={{
                  displayName: auth.user?.profile?.name || "Guest",
                  email: ""
                }}
                onApiReady={(api) => {
                  apiRef.current = api;
                }}
                getIFrameRef={(iframeRef) => {
                  if (iframeRef) {
                    iframeRef.style.height = '60vh';
                    iframeRef.style.width = '100%';
                    iframeRef.style.border = 'none';
                    iframeRef.style.borderRadius = '8px';
                  }
                }}
              />

              
              {/* Video Grid for participants would go here */}
              <div className="absolute bottom-4 right-4 grid grid-cols-2 gap-2">
                {students.slice(0, 4).map((student) => (
                  <div key={student.id} className="w-24 h-16 bg-gray-800 rounded border border-gray-600 flex items-center justify-center">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="" alt={student.userName} />
                        <AvatarFallback className="text-xs">
                        {(student.userName && student.userName.length > 0) ? student.userName.charAt(0) : "?"}
                        </AvatarFallback>                    
                        </Avatar>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="management" className="flex-1 m-4 mt-2">
            <div className="bg-white rounded-lg p-6 h-full">
              <Tabs 
                value={bottomActiveTab}
                onValueChange={(value) => setBottomActiveTab(value)}
                className="h-full flex flex-col"
              >
                <TabsList className="grid w-full max-w-lg grid-cols-4">
                  <TabsTrigger value="participants">Participants</TabsTrigger>
                  <TabsTrigger value="syllabus">Syllabus</TabsTrigger>
                  <TabsTrigger value="schedule">Schedule</TabsTrigger>
                  <TabsTrigger value="assessment">Assessment</TabsTrigger>
                </TabsList>

                <TabsContent value="participants" className="flex-1 mt-4">
                  <h2 className="text-lg font-medium mb-4">Participants Management</h2>
                  <div className="space-y-2">
                    {students.map((student) => (
                      <div key={student.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src="" alt={student.userName} />
                            <AvatarFallback>
                              {(student.userName && student.userName.length > 0) ? student.userName.charAt(0) : "?"}
                            </AvatarFallback>                          </Avatar>
                          <span>{student.userName}</span>
                          <Badge variant={student.status === 'ACTIVE' ? 'secondary' : 'default'}>
                            {student.status}
                          </Badge>
                        </div>
                        {student.status === 'INACTIVE' && (
                          <div className="flex items-center gap-2">
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => handleDenyStudent(student.id)}
                              className="border-red-500 text-red-500 hover:bg-red-50"
                            >
                              Deny
                            </Button>
                            <Button 
                              size="sm" 
                              onClick={() => handleAdmitStudent(student.id)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              Admit
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="syllabus" className="flex-1 mt-4">
                  <div className="mb-4">
                    <h2 className="text-xl font-semibold mb-1">Course Syllabus</h2>
                    <p className="text-gray-500">Manage topics and track progress</p>
                  </div>
                  <Card>
                    <CardContent className="p-4">
                      <SyllabusTopicList
                        topics={syllabusTopics}
                        onUpdateStatus={handleUpdateTopicStatus}
                        onAddTopic={() => setIsAddTopicOpen(true)}
                        onRefresh={fetchSyllabus}

                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="schedule" className="flex-1 mt-4">
                  <div className="mb-4">
                    <h2 className="text-xl font-semibold mb-1">Class Schedule</h2>
                    <p className="text-gray-500">View and manage upcoming sessions</p>
                  </div>
                  {isLoadingSchedules ? (
                    <div className="text-center py-12">
                      <p className="text-gray-500">Loading schedules...</p>
                    </div>
                  ) : schedules.length === 0 ? (
                    <div className="text-center py-12">
                      <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                      <h3 className="text-lg font-medium text-gray-600 mb-2">No Scheduled Sessions</h3>
                      <p className="text-gray-500">There are no upcoming sessions for this class.</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {schedules.map((schedule) => (
                        <Card key={schedule.id} className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-medium mb-1">{schedule.classroomName}</h3>
                              <p className="text-sm text-gray-500">{schedule.description}</p>
                              <div className="flex items-center gap-2 mt-2 text-sm text-gray-600">
                                <Calendar className="h-4 w-4" />
                                <span>{format(new Date(schedule.startTime), "EEEE, MMMM d")}</span>
                                <Clock className="h-4 w-4 ml-2" />
                                <span>
                                  {format(new Date(schedule.startTime), "h:mm a")} - {format(new Date(schedule.endTime), "h:mm a")}
                                </span>
                              </div>
                            </div>
                            {schedule.recurring && (
                              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                {schedule.recurrenceType})
                              </Badge>
                            )}
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="assessment" className="flex-1 mt-4">
                  <h2 className="text-lg font-medium mb-4">Student Assessment</h2>
                  <p className="text-gray-500">Assessment tools and student progress tracking would appear here.</p>
                </TabsContent>
              </Tabs>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Bottom Control Bar */}
      <div className="bg-gray-800 border-t border-gray-700 p-4">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          {/* Left Controls - Video/Audio */}
          <div className="flex items-center gap-3">
            <Button 
              variant={videoEnabled ? "default" : "secondary"} 
              size="icon"
              onClick={toggleVideo}
              className={videoEnabled ? "bg-green-600 hover:bg-green-700" : "bg-gray-600 hover:bg-gray-700"}
            >
              <Video className="h-5 w-5" />
            </Button>
            
            <Button 
              variant={micEnabled ? "default" : "secondary"} 
              size="icon"
              onClick={toggleMic}
              className={micEnabled ? "bg-green-600 hover:bg-green-700" : "bg-red-600 hover:bg-red-700"}
            >
              {micEnabled ? <Mic className="h-5 w-5" /> : <MicOff className="h-5 w-5" />}
            </Button>
            
            <Button 
              variant={screenShareEnabled ? "default" : "secondary"} 
              size="icon"
              onClick={toggleScreenShare}
              className={screenShareEnabled ? "bg-blue-600 hover:bg-blue-700" : "bg-gray-600 hover:bg-gray-700"}
            >
              <ScreenShare className="h-5 w-5" />
            </Button>
          </div>
          
          
          {/* Right Controls */}
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              size="sm"
              className="text-white border-gray-600 hover:bg-gray-700"
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="destructive" size="sm">
              End Class
            </Button>
          </div>
        </div>
      </div>

      {/* Dialogs */}
      <Dialog open={isAddTopicOpen} onOpenChange={setIsAddTopicOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Topic</DialogTitle>
          </DialogHeader>
          <SyllabusTopicForm
            onSubmit={handleAddTopic}
            onCancel={() => setIsAddTopicOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ClassroomPage;