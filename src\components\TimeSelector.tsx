
import React, { useState } from 'react';
import { Clock, Edit3 } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface TimeSelectorProps {
  label: string;
  value: string;
  onChange: (time: string) => void;
  placeholder?: string;
  disabledTimes?: string[];
  className?: string;
  style?: React.CSSProperties; 
}

const TimeSelector = ({ label, value, onChange, placeholder, disabledTimes = [], className, style }: TimeSelectorProps) => {
    const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState('');

  // Generate time slots from 6 AM to 11 PM in 15-minute intervals
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 6; hour <= 23; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const time24 = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
       
        // Fix the 12-hour conversion logic
        let hour12 = hour;
        let ampm = 'AM';
       
        if (hour === 0) {
          hour12 = 12;
          ampm = 'AM';
        } else if (hour === 12) {
          hour12 = 12;
          ampm = 'PM';
        } else if (hour > 12) {
          hour12 = hour - 12;
          ampm = 'PM';
        } else {
          ampm = 'AM';
        }
       
        const time12 = `${hour12}:${minute.toString().padStart(2, '0')} ${ampm}`;
       
        slots.push({
          value: time24,
          label: time12
        });
      }
    }
    return slots;
  };

  const timeSlots = generateTimeSlots();

  const formatTimeForDisplay = (time24: string) => {
    if (!time24) return '';
    const [hour, minute] = time24.split(':').map(Number);
    let hour12 = hour;
    let ampm = 'AM';
   
    if (hour === 0) {
      hour12 = 12;
      ampm = 'AM';
    } else if (hour === 12) {
      hour12 = 12;
      ampm = 'PM';
    } else if (hour > 12) {
      hour12 = hour - 12;
      ampm = 'PM';
    } else {
      ampm = 'AM';
    }
   
    return `${hour12}:${minute.toString().padStart(2, '0')} ${ampm}`;
  };

  const handleEditClick = () => {
    setIsEditing(true);
    setInputValue(value);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputBlur = () => {
    // Validate and format the input
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;
    if (timeRegex.test(inputValue)) {
      onChange(inputValue);
    }
    setIsEditing(false);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleInputBlur();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setInputValue('');
    }
  };

  return (
    <div>
      <Label className="text-sm font-medium text-gray-700 mb-2 block">
        {label}*
      </Label>
     
      {isEditing ? (
        <div className="relative">
          <Input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            onKeyDown={handleInputKeyDown}
            placeholder="HH:MM (24-hour format)"
           className={cn("edu-form-field pl-10", className)}
            style={style}
              autoFocus
          />
          <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        </div>
      ) : (
        <div className="relative">
          <Select value={value} onValueChange={onChange}>
             <SelectTrigger className={cn("edu-form-field", className)} style={style}>
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4 text-gray-400" />
                <SelectValue placeholder={placeholder} />
              </div>
            </SelectTrigger>
            <SelectContent className="max-h-60">
              {timeSlots.map((slot) => (
                <SelectItem 
                  key={slot.value} 
                  value={slot.value}
                  disabled={disabledTimes.includes(slot.value)}
                >
                  {slot.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
         
          {value && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-8 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
              onClick={handleEditClick}
            >
              <Edit3 className="h-3 w-3" />
            </Button>
          )}
        </div>
      )}
     
      {value && !isEditing && (
        <p className="text-xs text-gray-500 mt-1">
          24-hour format: {value} | Click edit to input custom time
        </p>
      )}
    </div>
  );
};

export default TimeSelector;