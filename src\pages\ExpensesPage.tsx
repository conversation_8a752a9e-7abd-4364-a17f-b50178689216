import { toast } from "sonner";

import React, { useState, useMemo, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useApp } from "@/context/AppContext";
import {
  ChevronLeft,
  PlusCircle,
  Search,
  Calendar,
  Trash2,
  Edit,
  ArrowLeft,
  DollarSign
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardFooter,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON><PERSON>r, <PERSON><PERSON>ooltip, ChartTooltipContent } from "@/components/ui/chart";
import { format, parseISO, subMonths } from "date-fns";
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts";
import { useToast } from "@/components/ui/use-toast";
import { createNewExpense, getAllExpenses, updateExistingExpense, getExpensesByCategory,
  getMonthlyExpenses,deleteExistingExpense } from "@/services/expenseService";
import { useAuth } from "react-oidc-context";
import {getClassIdsAndNames } from "@/services/announcementService";

import { useSelector } from "react-redux";
// Define expense categories
const EXPENSE_CATEGORIES = [
  "TEACHING_MATERIALS",
  "TRANSPORTATION",
  "OFFICE_SUPPLIES",
  "TECHNOLOGY",
  "PROFESSIONAL_DEVELOPMENT",
  "MEALS",
  "ACCOMMODATION",
  "OTHER"
];

export default function ExpensesPage() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const { expenses = [], createExpense, updateExpense, deleteExpense } = useApp();
    const userData = useSelector((state: any) => state.user.userData);
  const [allExpenses, setAllExpenses] = useState([]);
  const [monthlyExpenseData, setMonthlyExpenseData] = useState([]);
  const [expenseByCategory, setExpenseByCategory] = useState([]);
const [searchQuery, setSearchQuery] = useState<string>("");
  const [dateFilter, setDateFilter] = useState<string>("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [classOptions, setClassOptions] = useState<{ id: string; className: string }[]>([]);
  const auth = useAuth();
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentExpense, setCurrentExpense] = useState<any>(null);
  const [newExpense, setNewExpense] = useState({
    title: "",
    description: "",
    amount: "",
    expenseDate: new Date().toISOString().split('T')[0],
    category: "OFFICE_SUPPLIES",
    classId:""
  });
  
  const [createErrors, setCreateErrors] = useState({
    title: "",
    amount: "",
    expenseDate: "",
    classId: ""
  });
  
  const [editErrors, setEditErrors] = useState({
    title: "",
    amount: "",
    expenseDate: "",
    classId: ""
  });

  const loadAllData = async () => {
    if (!user?.access_token) return;
    
    try {
      const [expensesData, monthlyData, categoryData, classData] = await Promise.all([
        getAllExpenses(user.access_token),
        getMonthlyExpenses(user.access_token),
        getExpensesByCategory(user.access_token),
        getClassIdsAndNames(user.access_token)
      ]);
      
      setAllExpenses(expensesData);
      setMonthlyExpenseData(monthlyData);
      setExpenseByCategory(categoryData);
      setClassOptions(classData);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };
  
  useEffect(() => {
    loadAllData();
  }, [user?.access_token]);
  // Get today's date
  const today = new Date();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();
  
  // Filter expenses based on selections
  const filteredExpenses = useMemo(() => {
    return (allExpenses || []).filter(expense => {
      // Filter by search
      if (searchQuery && 
          !expense.title?.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !expense.description?.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      
      // Filter by category
      if (categoryFilter !== "all" && expense.category !== categoryFilter) {
        return false;
      }
      
      // Filter by date
      if (dateFilter !== "all" && expense.expenseDate) {
        const expenseDate = new Date(expense.expenseDate);
        
        if (dateFilter === "thisMonth") {
          const expenseMonth = expenseDate.getMonth();
          const expenseYear = expenseDate.getFullYear();
          return expenseMonth === currentMonth && expenseYear === currentYear;
        } else if (dateFilter === "lastMonth") {
          const lastMonth = new Date(currentYear, currentMonth - 1);
          const expenseMonth = expenseDate.getMonth();
          const expenseYear = expenseDate.getFullYear();
          return expenseMonth === lastMonth.getMonth() && expenseYear === lastMonth.getFullYear();
        } else if (dateFilter === "thisYear") {
          return expenseDate.getFullYear() === currentYear;
        }
      }
      
      return true;
    });
  }, [allExpenses, searchQuery, categoryFilter, dateFilter, currentMonth, currentYear]);
  
  // Calculate total expenses
  const totalExpenses = useMemo(() => {
    return filteredExpenses.reduce((sum, expense) => sum + (expense.amount || 0), 0).toFixed(2);
  }, [filteredExpenses]);


  
  
  
  const handleCreateExpense = async () => {
    const errors = {
      title: !newExpense.title ? "Title is required" : "",
      amount: !newExpense.amount ? "Amount is required" : "",
      expenseDate: !newExpense.expenseDate ? "Date is required" : "",
      classId: !newExpense.classId ? "Class is required" : ""
    };
    
    setCreateErrors(errors);
    
    if (errors.title || errors.amount || errors.expenseDate || errors.classId) {
      return;
    }
    
    try {
      await createNewExpense(user.access_token, {
        title: newExpense.title,
        description: newExpense.description,
        classId: newExpense.classId,
        amount: parseFloat(newExpense.amount),
        expenseDate: newExpense.expenseDate,
        category: newExpense.category
      });
      loadAllData();
      // Reset the form and close dialog
      setNewExpense({
        title: "",
        description: "",
        amount: "",
        expenseDate: new Date().toISOString().split('T')[0],
        category: "OFFICE_SUPPLIES",
        classId:""
      });
      
      setCreateErrors({ title: "", amount: "", expenseDate: "", classId: "" });
      setIsCreateDialogOpen(false);
      
      toast({
        title: "Expense created",
        description: "Your expense has been recorded"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create expense",
        variant: "destructive"
      });
    }
  };
  
  const handleOpenEditDialog = (expense: any) => {
    setCurrentExpense(expense);
    setIsEditDialogOpen(true);
  };
  
  const handleUpdateExpense = async () => {
    if (!currentExpense || !currentExpense.id) return;
    
    const errors = {
      title: !currentExpense.title ? "Title is required" : "",
      amount: !currentExpense.amount ? "Amount is required" : "",
      expenseDate: !currentExpense.expenseDate ? "Date is required" : "",
      classId: !currentExpense.classId ? "Class is required" : ""
    };
    
    setEditErrors(errors);
    
    if (errors.title || errors.amount || errors.expenseDate || errors.classId) {
      return;
    }
    
    try {
      const updateData = {
        amount: parseFloat(currentExpense.amount),
        category: currentExpense.category,
        title: currentExpense.title,
        description: currentExpense.description,
        expenseDate: currentExpense.expenseDate
      };
      
      await updateExistingExpense(user.access_token, currentExpense.id, updateData);
      
      setEditErrors({ title: "", amount: "", expenseDate: "", classId: "" });
      setIsEditDialogOpen(false);
      loadAllData();
      toast({
        title: "Expense updated",
        description: "The expense has been updated successfully"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update expense",
        variant: "destructive"
      });
    }
  };
  
  const handleDeleteExpense = async (id: string) => {
    try {
      await deleteExistingExpense(user.access_token, id);
      loadAllData();
      toast({
        title: "Expense deleted",
        description: "The expense has been deleted successfully",
        variant: "default"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete expense",
        variant: "destructive"
      });
    }
  };
  
  const chartConfig = {
    expense: { color: "#ef4444" },
    categoryExpense: { color: "#8b5cf6" }
  };
  
  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-grow">
        <header className="bg-white p-6 flex justify-between items-center border-b border-gray-200">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              className="p-2" 
              onClick={() => navigate('/teacher-dashboard')}
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-semibold">Expense Manager</h1>
          </div>
          <div className="flex gap-3">
            <Button 
              className="bg-purple-600 hover:bg-purple-700 flex gap-2"
              onClick={() => setIsCreateDialogOpen(true)}
            >
              <PlusCircle className="h-4 w-4" />
              Add Expense
            </Button>
          </div>
        </header>
        
        <div className="p-6">
          <div className="mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Expenses
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 text-red-500 mr-2" />
                  <span className="text-2xl font-bold">${totalExpenses}</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Expenses</CardTitle>
                <CardDescription>
                  Expense trends over the last 12 months
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={monthlyExpenseData}
                      margin={{ top: 10, right: 10, bottom: 40, left: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="month" 
                        tick={{ fontSize: 12 }}
                        angle={-45}
                        textAnchor="end"
                        height={70}
                      />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar 
                        dataKey="totalAmount" 
                        fill={chartConfig.expense.color} 
                        name="Expense" 
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Expenses by Category</CardTitle>
                <CardDescription>
                  Distribution of expenses across categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={expenseByCategory}
                      margin={{ top: 10, right: 10, bottom: 70, left: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="category" 
                        tick={{ fontSize: 12 }}
                        angle={-45}
                        textAnchor="end"
                        height={70}
                      />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar 
                        dataKey="totalAmount" 
                        fill={chartConfig.categoryExpense.color} 
                        name="Amount" 
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
               <CardTitle>Expense History</CardTitle>
                  <CardDescription>View and manage all expenses</CardDescription>
              <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search expenses..."
                      className="edu-form-field pl-8 md:w-[250px]"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="edu-form-field md:w-[200px]">
                      <SelectValue placeholder="Filter by category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {EXPENSE_CATEGORIES.map((category) => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Select value={dateFilter} onValueChange={setDateFilter}>
                    <SelectTrigger className="edu-form-field md:w-[180px]">
                      <SelectValue placeholder="Filter by date" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="thisMonth">This Month</SelectItem>
                      <SelectItem value="lastMonth">Last Month</SelectItem>
                      <SelectItem value="thisYear">This Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredExpenses.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        No expenses found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredExpenses.map((expense) => (
                      <TableRow key={expense.id}>
                        <TableCell className="font-medium">{expense.title}</TableCell>
                        <TableCell>{expense.category}</TableCell>
                        <TableCell>{expense.expenseDate}</TableCell>
                        <TableCell>{expense.amount.toFixed(2)}</TableCell>
                        <TableCell className="max-w-[200px] truncate">{expense.description}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="icon" onClick={() => handleOpenEditDialog(expense)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" onClick={() => handleDeleteExpense(expense.id)}>
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Create Expense Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Expense</DialogTitle>
            <DialogDescription>
              Create a new expense record for your accounting
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="title">Title</label>
              <Input 
                id="title" 
                value={newExpense.title}
                onChange={(e) => {
                  setNewExpense({...newExpense, title: e.target.value});
                  if (createErrors.title) setCreateErrors({...createErrors, title: ""});
                }} 
                placeholder="Expense title"
                className={createErrors.title ? "border-red-500" : "edu-form-field"}
              />
              {createErrors.title && <p className="text-sm text-red-500">{createErrors.title}</p>}
            </div>
            <div className="space-y-2">
              <label htmlFor="category">Class Name</label>
              <Select 
                value={newExpense.classId} 
                onValueChange={(value) => {
                  setNewExpense({...newExpense, classId: value});
                  if (createErrors.classId) setCreateErrors({...createErrors, classId: ""});
                }}
              >
                <SelectTrigger className={createErrors.classId ? "border-red-500" : "edu-form-field"}>
                  <SelectValue placeholder="Select a class" />
                </SelectTrigger>
                <SelectContent>
                  {classOptions.map(classItem => <SelectItem key={classItem.id} value={classItem.id}>
                      {classItem.className}
                    </SelectItem>)}
                </SelectContent>
              </Select>
              {createErrors.classId && <p className="text-sm text-red-500">{createErrors.classId}</p>}
            </div>
            
            <div className="space-y-2">
              <label htmlFor="category">Category</label>
              <Select 
                value={newExpense.category} 
                onValueChange={(value) => setNewExpense({...newExpense, category: value})}
              >
                <SelectTrigger className="edu-form-field">
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {EXPENSE_CATEGORIES.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="amount">Amount</label>
                <Input 
                  id="amount" 
                  type="number"
                  value={newExpense.amount}
                  onChange={(e) => {
                    setNewExpense({...newExpense, amount: e.target.value});
                    if (createErrors.amount) setCreateErrors({...createErrors, amount: ""});
                  }} 
                  placeholder="0.00"
                  className={createErrors.amount ? "border-red-500" : "edu-form-field"}
                />
                {createErrors.amount && <p className="text-sm text-red-500">{createErrors.amount}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="date">Date</label>
                <Input 
                  id="date" 
                  type="date"
                  value={newExpense.expenseDate}
                  onChange={(e) => {
                    setNewExpense({...newExpense, expenseDate: e.target.value});
                    if (createErrors.expenseDate) setCreateErrors({...createErrors, expenseDate: ""});
                  }}
                  className={createErrors.expenseDate ? "border-red-500" : "edu-form-field"}
                />
                {createErrors.expenseDate && <p className="text-sm text-red-500">{createErrors.expenseDate}</p>}
              </div>
            </div>
            <div className="space-y-2">
              <label htmlFor="description">Description (optional)</label>
              <Input 
                id="description" 
                value={newExpense.description}
                className="edu-form-field"
                onChange={(e) => setNewExpense({...newExpense, description: e.target.value})} 
                placeholder="Describe the expense" 
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>Cancel</Button>
            <Button 
              className="bg-purple-600 hover:bg-purple-700" 
              onClick={handleCreateExpense}
            >
              Add Expense
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Expense Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Expense</DialogTitle>
            <DialogDescription>
              Modify the details of this expense
            </DialogDescription>
          </DialogHeader>
          {currentExpense && (
            <div className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="edit-title">Title</label>
                <Input 
                  id="edit-title" 
                  value={currentExpense.title}
                  onChange={(e) => {
                    setCurrentExpense({...currentExpense, title: e.target.value});
                    if (editErrors.title) setEditErrors({...editErrors, title: ""});
                  }} 
                  placeholder="Expense title"
                  className={editErrors.title ? "border-red-500" : "edu-form-field"}
                />
                {editErrors.title && <p className="text-sm text-red-500">{editErrors.title}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="edit-class">Class Name</label>
                <Select 
                  value={currentExpense.classId} 
                  onValueChange={(value) => {
                    setCurrentExpense({...currentExpense, classId: value});
                    if (editErrors.classId) setEditErrors({...editErrors, classId: ""});
                  }}
                >
                  <SelectTrigger className={editErrors.classId ? "border-red-500" : "edu-form-field"}>
                    <SelectValue placeholder="Select a class" />
                  </SelectTrigger>
                  <SelectContent>
                    {classOptions.map(classItem => <SelectItem key={classItem.id} value={classItem.id}>
                        {classItem.className}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                {editErrors.classId && <p className="text-sm text-red-500">{editErrors.classId}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="edit-category">Category</label>
                <Select 
                  value={currentExpense.category} 
                  onValueChange={(value) => setCurrentExpense({...currentExpense, category: value})}
                >
                  <SelectTrigger className="edu-form-field">
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {EXPENSE_CATEGORIES.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="edit-amount">Amount </label>
                  <Input 
                    id="edit-amount" 
                    type="number"
                    value={currentExpense.amount}
                    onChange={(e) => {
                      setCurrentExpense({...currentExpense, amount: parseFloat(e.target.value)});
                      if (editErrors.amount) setEditErrors({...editErrors, amount: ""});
                    }} 
                    placeholder="0.00"
                    className={editErrors.amount ? "border-red-500" : "edu-form-field"}
                  />
                  {editErrors.amount && <p className="text-sm text-red-500">{editErrors.amount}</p>}
                </div>
                <div className="space-y-2">
                  <label htmlFor="edit-date">Date</label>
                  <Input 
                    id="edit-date" 
                    type="date"
                    value={currentExpense.expenseDate}
                    onChange={(e) => {
                      setCurrentExpense({...currentExpense, expenseDate: new Date(e.target.value)});
                      if (editErrors.expenseDate) setEditErrors({...editErrors, expenseDate: ""});
                    }}
                    className={editErrors.expenseDate ? "border-red-500" : "edu-form-field"}
                  />
                  {editErrors.expenseDate && <p className="text-sm text-red-500">{editErrors.expenseDate}</p>}
                </div>
              </div>
              <div className="space-y-2">
                <label htmlFor="edit-description">Description (optional)</label>
                <Input 
                  id="edit-description" 
                  value={currentExpense.description}
                  onChange={(e) => setCurrentExpense({...currentExpense, description: e.target.value})} 
                  placeholder="Describe the expense" 
                  className="edu-form-field"
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancel</Button>
            <Button 
              className="bg-purple-600 hover:bg-purple-700" 
              onClick={handleUpdateExpense}
            >
              Update Expense
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
