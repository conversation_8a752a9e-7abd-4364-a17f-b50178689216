
import React, { useState,use<PERSON><PERSON>back ,useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useApp } from "@/context/AppContext";
import { useAuth } from "react-oidc-context";
import { useIsMobile } from "@/hooks/use-mobile";
import { ArrowLeft, FileCheck, FileText, UserCheck, Wand2,Trash2,Upload,Edit,Check,X,CheckCircle2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import { User<PERSON><PERSON> } from "@/types";
import { toast } from "sonner";
import { useSelector } from "react-redux";
import { useUserRole } from "@/hooks/useUserRole";
import { Assignment } from "@/types";

import { getTeacherAssignmentsForClass ,deleteExistingQuestion,updateAssignmentStatus,
  updateAssignment,
  getAssignmentsForStudent,getAllSubmisionsForAssignment} from "@/services/assignmentService";

export default function AssignmentDetailPage() {
  const { classId, assignmentId } = useParams<{ classId: string; assignmentId: string }>();
  const navigate = useNavigate();
  const { assignments, classes, submitAssignment } = useApp();
  const { user } = useAuth();
  const{ selectedRole} =  useUserRole(); 

  const isTeacher = selectedRole === UserRole.TEACHER;
  const isStudent = selectedRole === UserRole.STUDENT;
  const isMobile = useIsMobile();
  const [submitting, setSubmitting] = useState(false);
// pick assignment from nav state first, then fetchedAssignments, then app context
  const currentClass = classes.find(c => c.id === classId);
  const [fetchedAssignments, setFetchedAssignments] = useState<Assignment[]>([]);
  
  const [fetchedSubmissions, setFetchedSubmissions] = useState<any[]>([]);
 const [submissionsLoading, setSubmissionsLoading] = useState(false);
  const [assignmentsLoading, setAssignmentsLoading] = useState(false);
 const auth = useAuth();
const [generatedQuestionsList, setGeneratedQuestionsList] = useState<{ id?: string; number: number; questionText: string; type?: string; options?: string[]; correctAnswer?: string }[]>([]);
const [editingQuestionId, setEditingQuestionId] = useState<string | null>(null);
const [editingQuestionText, setEditingQuestionText] = useState<string>("");
const fetchAssignments = useCallback(async () => {
  console.log("fetchAssignments triggered", { token: !!auth?.user?.access_token, classId });
  if (!auth?.user?.access_token || !classId) return;
  setAssignmentsLoading(true);
  try {
    let data;
     data = await getTeacherAssignmentsForClass(auth.user.access_token, classId);
   
   // ensure we store an array result directly on fetchedAssignments
    const list = Array.isArray(data) ? data : (data?.content || []);
    setFetchedAssignments(list);

    console.log("fetched assignments:", list);
  } catch (err) {
    console.error("Failed to load assignments:", err);
    toast.error("Failed to load assignments");
  } finally {
    setAssignmentsLoading(false);
  }
}, [auth?.user?.access_token, classId]);

useEffect(() => {
  fetchAssignments();
}, [fetchAssignments]);
const fetchSubmissions = useCallback(async () => {
  console.log("fetchSubmissions triggered", { token: !!auth?.user?.access_token, assignmentId });
  if (!auth?.user?.access_token || !classId) return;
  setSubmissionsLoading(true);
  try {
    let data;
     data = await getAllSubmisionsForAssignment(auth.user.access_token,assignmentId);
   console.log(data)
   // ensure we store an array result directly on fetchedAssignments
    const list = Array.isArray(data) ? data : (data?.content || []);
    setFetchedSubmissions(list);


    console.log("fetched assignments:", list);
  } catch (err) {
    console.error("Failed to load assignments:", err);
    toast.error("Failed to load assignments");
  } finally {
    setSubmissionsLoading(false);
  }
}, [auth?.user?.access_token, classId]);

useEffect(() => {
  fetchSubmissions();
}, [fetchSubmissions]);

console.log(fetchSubmissions)
  
const allAssignments = fetchedAssignments.length ? fetchedAssignments : (assignments || []);
const assignment = allAssignments.find((a: any) =>  String(a.id) === String(assignmentId) && (a.classroomId ?? a.classId ?? a.classroom?.id) === classId
);
const [publishing, setPublishing] = useState(false);
  const [published, setPublished] = useState<boolean>(false);
console.log(assignment)

   const submittedBy: any = assignment?.submittedBy ?? [];

  // safe submitted check: handle array of ids or objects, or single id
  const hasSubmitted = Boolean(user) && (Array.isArray(submittedBy)
    ? submittedBy.some((s: any) =>
        String(s) === String(user.id_token) ||
        String(s?.id) === String(user.id_token) ||
        String(s?.userId) === String(user.id_token)
      )
    : String(submittedBy) === String(user.id_token));
  const isOverdue = assignment?.dueDate ? new Date(assignment.dueDate) < new Date() : false;
 const handleSubmit = () => {
    if (user && !hasSubmitted) {
      setSubmitting(true);
      submitAssignment(assignment.id, user.id_token);
      setTimeout(() => {
        setSubmitting(false);
      }, 1000);
    }
  };
 const assignmentDescriptionRaw = assignment?.description ?? "";

  const hasGeneratedQuestions = assignmentDescriptionRaw.includes("**Generated Questions:**") || (Array.isArray(assignment?.questions) && assignment?.questions.length > 0);
  let generatedQuestions: string[] = [];

const assignmentDescription = assignment?.description ?? "";

 useEffect(() => {
   if (!assignment || !Array.isArray(assignment.questions) || assignment.questions.length === 0) {
     setGeneratedQuestionsList([]);
     return;
   }

   const list = assignment.questions.map((q: any, idx: number) => ({
     id: q?.id,
     number: q?.questionNumber ?? idx + 1,
     questionText: q?.questionText ?? "",
     type: q?.type,
     options: q?.options || [],
     correctAnswer: q?.correctAnswer
   }));
   setGeneratedQuestionsList(list);
 }, [assignment?.id, assignment?.questions]);
 const handleRemoveGeneratedQuestion = async (questionId: string | undefined, assignmentIdParam: string, index: number) => {
  if (!questionId) {
    toast.error("Missing question id");
    return;
  }
  if (!window.confirm("Remove this question?")) return;

  try {
    // call API to delete the question (adjust args if your service signature differs)
    await deleteExistingQuestion(auth.user.access_token, assignmentIdParam, questionId);
    setGeneratedQuestionsList(prev => {
      const updated = prev.filter((_, i) => i !== index)
        .map((q, idx) => ({ ...q, number: idx + 1 }));
      return updated;
   });
    toast.success("Question removed");
  } catch (err) {
   console.error("Failed to remove question", err);
    toast.error("Failed to remove question");
  }
};
  useEffect(() => {
  // initialize published state from assignment if available
  if (assignment) {
    setPublished(String(assignment.status || "").toUpperCase() === "PUBLISHED");
  }
}, [assignment?.status]);

const handleEditQuestion = (questionId: string | undefined) => {
  if (!questionId) return;
  const question = generatedQuestionsList.find(q => q.id === questionId);
  if (!question) return;
  
  setEditingQuestionId(questionId);
  setEditingQuestionText(question.questionText);
};

const handleSaveQuestion = async (questionId: string) => {
  if (!editingQuestionText.trim()) {
    toast.error("Question text cannot be empty");
    return;
  }
  
  try {
    const updatedQuestions = generatedQuestionsList.map(q => 
      q.id === questionId ? { ...q, questionText: editingQuestionText } : q
    );
    const payload = {
      classroomId: assignment.classroomId || classId,
      title: assignment.title,
      description: assignment.description,
      subject: "General",
      topic:  "General",
      type:  "MANUAL",
      dueDate: assignment.dueDate,
      questions: updatedQuestions.map(q => ({
        questionText: q.questionText,
        type: "MULTIPLE_CHOICE",
        marks: 1,
        correctAnswer: "",
        explanation: "",
        options: []
      }))
    };
    
    await updateAssignment(auth.user.access_token, payload, assignment.id);
    
    setGeneratedQuestionsList(updatedQuestions);
    setEditingQuestionId(null);
    setEditingQuestionText("");
    
    toast.success("Question updated");
  } catch (err) {
    console.error("Failed to update question", err);
    toast.error("Failed to update question");
  }
};

const handleCancelEdit = () => {
  setEditingQuestionId(null);
  setEditingQuestionText("");
};

const handlePublish = async () => {
  if (!assignment) return;
  if (!window.confirm("Publish this assignment? It will become visible to students.")) return;
  setPublishing(true);
  try {
    // If you have a publish API, call it here. Fallback: update local state and show toast.
     await updateAssignmentStatus(auth.user.access_token, assignment.id,"PUBLISHED")
    setPublished(true);
   toast.success("Assignment published");
  } catch (err) {
    console.error("Publish failed", err);
    toast.error("Failed to publish assignment");
  } finally {
    setPublishing(false);
  }
};

if (assignmentsLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading assignment...</h1>
        </div>
      </div>
    );
  }

  if (!assignment) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Assignment not found</h1>
          <Button onClick={() => {
              navigate(`/class/${classId}?tab=assignments`);
           }}>Return to Class</Button>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8">
      <div className="max-w-4xl mx-auto">
        <Button 
          variant="ghost" 
          className="mb-4 md:mb-6" 
          onClick={() => {
              navigate(`/class/${classId}?tab=assignments`);
          }}
        >
          <ArrowLeft className="mr-2 h-4 w-4 md:h-5 md:w-5" />
          Back to Class
        </Button>
        
        <div className="grid gap-4 md:gap-6">
          <Card>
            <CardHeader className="p-4 md:p-6">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                <div>
                  <CardTitle className="text-xl md:text-2xl">{assignment.title}</CardTitle>
                  <CardDescription className="mt-1">
                   {currentClass?.className ?? "Class"} • Due: {assignment?.dueDate ? format(new Date(assignment.dueDate), "PPP") : "No due date"}
                  </CardDescription>
                </div>
             <div className="mt-2 sm:mt-0 flex items-center gap-3">
                  {selectedRole === UserRole.TEACHER && (
                    <div className="flex items-center gap-2">
                      
                      <Button
                        size="sm"
                        variant={published ? "secondary" : "default"}
                        className="flex items-center gap-2 h-8"
                        onClick={handlePublish}
                        disabled={publishing || published}
                      >
                        <Upload className="h-4 w-4" />
                        {publishing ? "Publishing..." : published ? "Published" : "Publish"}
                      </Button>
                    </div>
                  )}

                  {isOverdue ? (
                    <Badge variant="destructive" className="whitespace-nowrap">
                      Overdue
                    </Badge>
                  ) : hasSubmitted ? (
                    <Badge variant="outline" className="bg-green-100 text-green-800 whitespace-nowrap">
                      Submitted
                   </Badge>
                  ) : (
                    <Badge variant="outline" className="whitespace-nowrap">
                      Pending
                    </Badge>
                 )}
                </div>   </div>
            </CardHeader>
            <CardContent className="p-4 md:p-6 space-y-4 md:space-y-6">
              <div>
                <h3 className="font-medium mb-2">Assignment Description</h3>
                 <span> {assignment.description}</span>
              </div>
              
               {generatedQuestionsList.length > 0 && (
                   <div>
                  <h3 className="font-medium mb-2 flex items-center">
                    <Wand2 className="h-4 w-4 mr-2 text-purple-500" />
                    Generated Questions
                  </h3>
                  <div className="p-3 md:p-4 bg-purple-50 rounded-md border border-purple-100">
                    <div className="space-y-4">
                    {generatedQuestionsList.map((q, index) => (
                        <div key={q.id ?? index} className="border rounded-lg p-4 bg-white">
                          <div className="flex items-start justify-between gap-2 mb-3">
                            <div className="flex-1">
                              <div className="flex items-start gap-2">
                                <span className="font-medium text-sm text-gray-600 mt-1">
                                  {q.number}.
                                </span>
                                <div className="flex-1">
                                  {editingQuestionId === q.id ? (
                                    <Input
                                      value={editingQuestionText}
                                      onChange={(e) => setEditingQuestionText(e.target.value)}
                                      className="edu-form-field w-full"
                                      onKeyDown={(e) => {
                                        if (e.key === 'Enter') handleSaveQuestion(q.id!);
                                        if (e.key === 'Escape') handleCancelEdit();
                                      }}
                                      autoFocus
                                    />
                                  ) : (
                                    <div>
                                      <p className="font-medium text-gray-900 mb-2">{q.questionText}</p>
                                      {q.type && (
                                        <Badge variant="outline" className="text-xs mb-2">
                                          {q.type.replace('_', ' ')}
                                        </Badge>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                            {selectedRole === UserRole.TEACHER && (
                              <div className="flex gap-1">
                                {editingQuestionId === q.id ? (
                                  <>
                                    <button
                                      type="button"
                                      aria-label="Save question"
                                      onClick={() => handleSaveQuestion(q.id!)}
                                      className="text-green-500 hover:text-green-700 pr-1"
                                    >
                                      <Check className="h-4 w-4" />
                                    </button>
                                    <button
                                      type="button"
                                      aria-label="Cancel edit"
                                      onClick={handleCancelEdit}
                                      className="text-gray-500 hover:text-gray-700 pr-2"
                                    >
                                      <X className="h-4 w-4" />
                                    </button>
                                  </>
                                ) : (
                                  <button
                                    type="button"
                                    aria-label="Edit question"
                                    onClick={() => handleEditQuestion(q.id)}
                                    className="text-blue-500 hover:text-blue-700 pr-2"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </button>
                                )}
                                <button
                                  type="button"
                                  aria-label="Remove question"
                                  onClick={() => handleRemoveGeneratedQuestion(q.id, assignment.id, index)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                 <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            )}
                          </div>
                          
                          {/* Display options for multiple choice questions */}
                          {q.options && q.options.length > 0 && (
                            <div className="ml-6 space-y-2">
                              <p className="text-sm font-medium text-gray-700 mb-2">Options:</p>
                              <div className="space-y-1">
                                {q.options.map((option, optIdx) => (
                                  <div key={optIdx} className="flex items-center gap-2 text-sm">
                                    <span className="font-medium text-gray-600 min-w-[20px]">
                                      {String.fromCharCode(65 + optIdx)}.
                                    </span>
                                    <span className={`${q.correctAnswer === option ? 'text-green-700 font-medium bg-green-50 px-2 py-1 rounded' : 'text-gray-700'}`}>
                                      {option}
                                    </span>
                                    {q.correctAnswer === option && (
                                      <Badge variant="outline" className="text-xs bg-green-100 text-green-800">
                                        Correct
                                      </Badge>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          {/* Display correct answer for non-multiple choice questions */}
                          {q.correctAnswer && (!q.options || q.options.length === 0) && (
                            <div className="ml-6 mt-2">
                              <p className="text-sm font-medium text-gray-700 mb-1">Correct Answer:</p>
                              <div className="text-sm text-green-700 font-medium bg-green-50 px-3 py-2 rounded border border-green-200">
                                {q.correctAnswer}
                              </div>
                            </div>
                          )}
                        </div>
                     ))}
                    </div>
                 </div>
                </div>
              )}
              
              
              {isTeacher && (
                <div>
                  <h3 className="font-medium mb-2">Submission Status ({fetchedSubmissions.length} students)</h3>
                  <Card>
                    <CardContent className="p-3 md:p-4">
                      {submissionsLoading ? (
                        <div className="text-center py-4">Loading submissions...</div>
                      ) : fetchedSubmissions.length > 0 ? (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Student</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Score</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {fetchedSubmissions.map((submission: any) => (
                              <TableRow key={submission.id}>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <Avatar className="h-8 w-8">
                                      <AvatarFallback>{submission.studentName?.[0] || 'S'}</AvatarFallback>
                                    </Avatar>
                                    <span>{submission.studentName || `Student ${submission.studentId}`}</span>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge variant={submission.status === 'SUBMITTED' ? 'default' : 'secondary'}>
                                    {submission.status}
                                  </Badge>
                                </TableCell>
                                <TableCell>{submission.obtainedMarks}/{submission.totalMarks}</TableCell>
                                <TableCell>
                                  <div className="flex gap-2">
                                    <Button variant="outline" size="sm">
                                      View
                                    </Button>
                                    <Button variant="outline" size="sm">
                                      Grade
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      ) : (
                        <div className="text-center py-4 md:py-6">
                          <UserCheck className="h-8 w-8 md:h-12 md:w-12 text-gray-300 mx-auto mb-2" />
                          <p className="text-sm md:text-base">No submissions yet</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
