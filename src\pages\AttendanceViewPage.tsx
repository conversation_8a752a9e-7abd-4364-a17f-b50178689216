import React, { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { useApp } from "@/context/AppContext";
import { Link } from "react-router-dom";
import { 
  BookOpen, 
  Calendar,
  Bell,
  User,
  MessageSquare,
  Users,
  Home,
  DollarSign,
  FileText,
  ClipboardCheck,
  Check,
  X,
  Clock
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserRole } from "@/types";

export default function AttendanceViewPage() {
  const { user } = useAuth();
  const { classes, getAttendanceByStudent } = useApp();
  
  const [selectedClass, setSelectedClass] = useState<string>("all");
  const [attendanceRecords, setAttendanceRecords] = useState<any[]>([]);
  
  // Filter classes based on student enrollment
  const userClasses = classes.filter(c => user && c.students.includes(user.id));

  // Update attendance records when selected class changes
  useEffect(() => {
    if (!user) return;
    
    const records = getAttendanceByStudent(user.id, selectedClass === "all" ? "" : selectedClass);
    setAttendanceRecords(records);
  }, [selectedClass, user, getAttendanceByStudent]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <Check className="h-5 w-5 text-green-500" />;
      case 'absent':
        return <X className="h-5 w-5 text-red-500" />;
      case 'late':
        return <Clock className="h-5 w-5 text-amber-500" />;
      case 'excused':
        return <Calendar className="h-5 w-5 text-blue-500" />;
      default:
        return null;
    }
  };

  const getAttendanceStats = () => {
    if (attendanceRecords.length === 0) return { present: 0, absent: 0, late: 0, excused: 0, total: 0, presentPercentage: 0 };
    
    const present = attendanceRecords.filter(r => r.status === 'present').length;
    const absent = attendanceRecords.filter(r => r.status === 'absent').length;
    const late = attendanceRecords.filter(r => r.status === 'late').length;
    const excused = attendanceRecords.filter(r => r.status === 'excused').length;
    
    const total = attendanceRecords.length;
    
    return {
      present,
      absent,
      late,
      excused,
      total,
      presentPercentage: total > 0 ? Math.round((present / total) * 100) : 0
    };
  };

  const stats = getAttendanceStats();

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="w-64 bg-white flex flex-col">
        <div className="p-5 flex items-center gap-2 h-14">
          <div className="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
            <BookOpen className="text-white h-5 w-5" />
          </div>
          <span className="text-lg font-medium">Student Portal</span>
        </div>

        <nav className="flex-grow">
          <ul className="space-y-0.5 py-2">
            <li className="px-3">
              <Link to="/student-dashboard" className="flex items-center gap-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100">
                <Home className="h-5 w-5" />
                <span>Dashboard</span>
              </Link>
            </li>
            <li className="px-3">
              <Link to="/my-classes" className="flex items-center gap-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100">
                <BookOpen className="h-5 w-5" />
                <span>My Classes</span>
              </Link>
            </li>
            <li className="px-3">
              <Link to="/attendance-view" className="flex items-center gap-3 px-3 py-2 rounded-md bg-purple-100 text-purple-700">
                <ClipboardCheck className="h-5 w-5" />
                <span>Attendance</span>
              </Link>
            </li>
            <li className="px-3">
              <Link to="/announcements" className="flex items-center gap-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100">
                <Bell className="h-5 w-5" />
                <span>Announcements</span>
              </Link>
            </li>
            <li className="px-3">
              <Link to="/chat" className="flex items-center gap-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100">
                <MessageSquare className="h-5 w-5" />
                <span>Chat</span>
              </Link>
            </li>
            <li className="px-3">
              <Link to="/student-payments" className="flex items-center gap-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100">
                <DollarSign className="h-5 w-5" />
                <span>Payments</span>
              </Link>
            </li>
          </ul>
        </nav>

        <div className="mt-auto p-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center border border-gray-200">
                <img src="/lovable-uploads/be4a4452-329d-4a54-b616-07af5c82205b.png" alt="Support" className="w-10 h-10" />
              </div>
            </div>
            <h3 className="text-center font-semibold">Support 24/7</h3>
            <p className="text-center text-sm text-gray-500">Need help with your classes?</p>
            <Button className="w-full mt-3 bg-purple-600 hover:bg-purple-700">Chat</Button>
          </div>
        </div>
      </div>

      <div className="flex-grow">
        <header className="bg-white p-6 flex justify-between items-center h-14">
          <h1 className="text-2xl font-semibold">My Attendance</h1>
          <div className="flex items-center gap-4">
            <Link to="/profile">
              <button>
                <User className="h-6 w-6 text-gray-500" />
              </button>
            </Link>
          </div>
        </header>

        <div className="p-6">
          <div className="mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-gray-500 mb-2">Present</h3>
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="text-3xl font-bold text-green-600">{stats.present}</div>
                      <div className="text-sm text-gray-500">Days</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-gray-500 mb-2">Absent</h3>
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="text-3xl font-bold text-red-600">{stats.absent}</div>
                      <div className="text-sm text-gray-500">Days</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-gray-500 mb-2">Late</h3>
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="text-3xl font-bold text-amber-600">{stats.late}</div>
                      <div className="text-sm text-gray-500">Days</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-gray-500 mb-2">Attendance Rate</h3>
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="text-3xl font-bold">{stats.presentPercentage}%</div>
                      <div className="text-sm text-gray-500">Overall</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Attendance Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4">
                <div className="w-full md:w-64">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Class</label>
                  <Select 
                    value={selectedClass} 
                    onValueChange={setSelectedClass}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Classes</SelectItem>
                      {userClasses.map(cls => (
                        <SelectItem key={cls.id} value={cls.id}>
                          {cls.className}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {attendanceRecords.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Class</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {attendanceRecords.map(record => (
                        <TableRow key={record.id}>
                          <TableCell>{record.date}</TableCell>
                          <TableCell>
                            {classes.find(c => c.id === record.classId)?.className || 'Unknown Class'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              {getStatusIcon(record.status)}
                              <span className="ml-2 capitalize">{record.status}</span>
                            </div>
                          </TableCell>
                          <TableCell>{record.notes || 'No notes'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8">
                    <ClipboardCheck className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">No Attendance Records</h3>
                    <p className="text-gray-500">
                      There are no attendance records available for {selectedClass !== "all" ? "this class" : "your classes"}.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
