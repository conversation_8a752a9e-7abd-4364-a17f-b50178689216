// utils/convertFromUTC.js
import { DateTime } from 'luxon';

/**
 * Converts a UTC date and time to a target timezone.
 * @param {string} date - Date in 'YYYY-MM-DD' format.
 * @param {string} time - Time in 'HH:mm' format.
 * @param {string} toZone - Target timezone (e.g., 'America/New_York').
 * @returns {string} - Converted time in 'HH:mm' format.
 */
export function convertUTCToLocalTime(date, time, targetZone) {
  const utcDateTime = DateTime.fromISO(`${date}T${time}`, { zone: 'utc' });
  const localDateTime = utcDateTime.setZone(targetZone);

  return {
    date: localDateTime.toFormat('yyyy-MM-dd'),
    time: localDateTime.toFormat('h:mm a')
  };
}

