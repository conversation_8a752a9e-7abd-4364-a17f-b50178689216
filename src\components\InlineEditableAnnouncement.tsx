
import React, { useState } from "react";
import { Announcement } from "@/types";
import { useApp } from "@/context/AppContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Edit, Save, X, Trash } from "lucide-react";
import { format } from "date-fns";

interface InlineEditableAnnouncementProps {
  announcement: Announcement;
}

export default function InlineEditableAnnouncement({ announcement }: InlineEditableAnnouncementProps) {
  const { updateAnnouncement, deleteAnnouncement } = useApp();
  const [isEditing, setIsEditing] = useState(false);
  const [editedAnnouncement, setEditedAnnouncement] = useState({
    title: announcement.title,
    content: announcement.content
  });

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedAnnouncement({
      title: announcement.title,
      content: announcement.content
    });
  };

  const handleSave = () => {
    updateAnnouncement(announcement.id, {
      title: editedAnnouncement.title,
      content: editedAnnouncement.content
    });
    setIsEditing(false);
  };

  const handleDelete = () => {
    deleteAnnouncement(announcement.id);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedAnnouncement(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        {isEditing ? (
          <Input
            name="title"
            className="edu-form-field"
                   
            value={editedAnnouncement.title}
            onChange={handleChange}
          />
        ) : (
          <CardTitle>{announcement.title}</CardTitle>
        )}
        <div className="text-sm text-muted-foreground">
          {format(new Date(announcement.createdAt), "MMM d, yyyy")}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <Textarea
            name="content"
            value={editedAnnouncement.content}
            onChange={handleChange}
            rows={4}
          />
        ) : (
          <p className="whitespace-pre-line">{announcement.content}</p>
        )}
      </CardContent>
      <CardFooter className="flex justify-end gap-2 pt-0">
        {isEditing ? (
          <>
            <Button 
              size="sm" 
              variant="outline" 
              className="flex items-center gap-1 " 
              onClick={handleCancel}
            >
              <X className="h-4 w-4" />
              Cancel
            </Button>
            <Button 
              size="sm" 
              className="bg-purple-600 hover:bg-purple-700 flex items-center gap-1" 
              onClick={handleSave}
            >
              <Save className="h-4 w-4" />
              Save
            </Button>
          </>
        ) : (
          <>
            <Button 
              size="sm" 
              variant="outline" 
              className="flex items-center gap-1 bg-purple-600 hover:bg-purple-700 " 
              onClick={handleEdit}
            >
              <Edit className="h-4 w-4" />
              Edit
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              className="flex items-center gap-1 text-red-600 hover:text-red-700" 
              onClick={handleDelete}
            >
              <Trash className="h-4 w-4" />
              Delete
            </Button>
          </>
        )}
      </CardFooter>
    </Card>
  );
}
