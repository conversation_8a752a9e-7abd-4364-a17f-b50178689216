
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface AttendanceSummaryProps {
  averageAttendance: string | number;
  classesThisMonth: number;
  studentsBelow75: number;
  onRecordAttendance: () => void;
}

export const AttendanceSummary = ({
  averageAttendance,
  classesThisMonth,
  studentsBelow75,
  onRecordAttendance
}: AttendanceSummaryProps) => {
  return (
    <div className="space-y-6">
      

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-green-500 mb-2">{averageAttendance}%</div>
              <p className="text-sm text-gray-600">Average Attendance</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-500 mb-2">{classesThisMonth}</div>
              <p className="text-sm text-gray-600">Classes This Month</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-500 mb-2">{studentsBelow75}</div>
              <p className="text-sm text-gray-600">Students Below 75%</p>
            </div>
          </CardContent>
        </Card>
      </div>

     
    </div>
  );
};
