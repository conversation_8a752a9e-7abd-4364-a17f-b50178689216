
import { Student } from './studentService';
import { UserRole } from '@/types';

interface ParentStudent extends Student {
  relation?: string;
}

export async function getParentStudents(parentId: string): Promise<ParentStudent[]> {
  // This would be replaced with a real API call in production
  const students = [
    {
      id: "1",
      studentName: "<PERSON>",
      email: "<EMAIL>",
      joinedDate: "2023-04-15",
      progress: 86,
      classes: ["Mathematics", "Physics"],
      lastActivity: "2023-09-01T15:30:00",
      relation: "Son",
      payments: {
        status: "paid" as const,
        amount: 250
      },
      phoneNumber: "232224",
  dateOfBirth: "",
  parentName: "",
    country : "",
     parentPhone: ""
    },
    {
      id: "2",
      studentName: "<PERSON>",
      email: "<EMAIL>",
      joinedDate: "2023-03-22",
      progress: 92,
      classes: ["Chemistry", "Biology"],
      lastActivity: "2023-08-30T10:15:00",
      relation: "Daughter",
      payments: {
        status: "paid" as const,
        amount: 300
      },
      phoneNumber: "232224",
  dateOfBirth: "",
  parentName: "",
    country : "",
     parentPhone: ""
    }
  ];
  
  return students;
}
