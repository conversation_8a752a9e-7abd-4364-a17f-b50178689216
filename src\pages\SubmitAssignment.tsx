import React, { useState,useCallback ,useEffect} from "react";
import { use<PERSON>ara<PERSON>, useNavigate, useLocation } from "react-router-dom";
import { useApp } from "@/context/AppContext";
import { useAuth } from "react-oidc-context";
import { useIsMobile } from "@/hooks/use-mobile";
import { ArrowLeft, FileCheck, FileText, UserCheck, Wand2, Eye, GraduationCap, FileSearch, Clock, Calendar, AlertCircle, CheckCircle2, Sparkles, TrendingUp, ChevronLeft, ChevronRight, ChevronDown, ChevronUp, PenSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { UserR<PERSON>, AssignmentSubmission } from "@/types";
import { toast } from "sonner";
import studentsData from "@/data/students.json";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { SubmissionGradingDialog } from "@/components/assignment/SubmissionGradingDialog";
import { SubmissionViewDialog } from "@/components/assignment/SubmissionViewDialog";
import {getAssignmentsForStudent, submitAssignmentByStudent, getStudentSubmittedAssignment} from "@/services/assignmentService";
import { Assignment } from "@/types";
export default function SubmitAssignment() {
  const { classId, assignmentId } = useParams<{ classId: string; assignmentId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const isSubmitted = location.state?.isSubmitted || false;
  const { assignments, classes, submitAssignment, gradeSubmission } = useApp();
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const [submitting, setSubmitting] = useState(false);
  const [submissionSuccess, setSubmissionSuccess] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [studentAnswers, setStudentAnswers] = useState<Record<number, string>>({});
  const [showResults, setShowResults] = useState(false);
  const [showBreakdown, setShowBreakdown] = useState(false);
  const [showPerformanceSummary, setShowPerformanceSummary] = useState(true);
  const [gradingDialog, setGradingDialog] = useState<{
    open: boolean;
    studentId: string;
    studentName: string;
    submission: AssignmentSubmission;
  } | null>(null);
  const [viewDialog, setViewDialog] = useState<{
    open: boolean;
    studentId: string;
    studentName: string;
    submission: AssignmentSubmission;
  } | null>(null);

const [assignmentsLoading, setAssignmentsLoading] = useState(false);
 const auth = useAuth();
   const [fetchedAssignments, setFetchedAssignments] = useState<Assignment[]>([]);
   const [submittedAssignment, setSubmittedAssignment] = useState<Assignment | null>(null);
 
  const fetchAssignments = useCallback(async () => {
    console.log("fetchAssignments triggered", { token: !!auth?.user?.access_token, classId, isSubmitted });
    if (!auth?.user?.access_token || !classId) return;
    setAssignmentsLoading(true);
    try {
      const data = await getAssignmentsForStudent(auth.user.access_token, classId);
        const list = Array.isArray(data) ? data : (data?.content || []);
        setFetchedAssignments(list);

      if (isSubmitted) {
        const sdata = await getStudentSubmittedAssignment(auth.user.access_token, assignmentId);
        setSubmittedAssignment(sdata);
      } console.log("fetched assignments successfully");
    } catch (err) {
      console.error("Failed to load assignments:", err);
      toast.error("Failed to load assignments");
    } finally {
      setAssignmentsLoading(false);
    }
  }, [auth?.user?.access_token, classId, isSubmitted, assignmentId]);
  
  useEffect(() => {
    fetchAssignments();
  }, [fetchAssignments]);
  
  const assignment = fetchedAssignments.find(a => a.id === assignmentId) || assignments.find(a => a.id === assignmentId);
  const currentClass = classes.find(c => c.id === classId);

  if (assignmentsLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading assignment...</h1>
        </div>
      </div>
    );
  }

  if (!assignment ) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Assignment not found</h1>
          <Button onClick={() => navigate(`/class/${classId}`)}>Return to Class</Button>
        </div>
      </div>
    );
  }

  const hasSubmitted = user && assignment.submittedBy?.includes(user.profile?.sub || '') || isSubmitted || false;
  const isOverdue = new Date(assignment.dueDate) < new Date();
  
  const completedCount = assignment.submissions?.filter(s => s.status === "complete" || s.status === "graded").length || 0;
  const totalStudents = currentClass?.students?.length || 0;
  
  const handleSubmit = async () => {
    if ( !hasSubmitted && auth?.user?.access_token) {
      setSubmitting(true);
      try {
        const payload = {
          assignmentId: assignment.id,
          answers: generatedQuestions.map((question, index) => ({
            questionId: question.id ,
            answer: studentAnswers[index] || ""
          }))
        };
        await submitAssignmentByStudent(auth.user.access_token, payload);
        setSubmissionSuccess(true);
        setShowResults(true);
        toast.success("Assignment submitted successfully!");
      } catch (error) {
        console.error("Failed to submit assignment:", error);
        toast.error("Failed to submit assignment");
      } finally {
        setSubmitting(false);
      }
    }
  };

  const handleAnswerChange = (questionIndex: number, answer: string) => {
    setStudentAnswers(prev => ({
      ...prev,
      [questionIndex]: answer
    }));
  };

  const getQuestionStatus = (index: number) => {
    // For demo purposes, marking odd-indexed questions as correct
    return index % 2 === 0 ? "correct" : "wrong";
  };

  const handleGradeSubmission = (updatedSubmission: AssignmentSubmission) => {
    if (gradingDialog) {
      gradeSubmission(assignment.id, gradingDialog.studentId, updatedSubmission);
      setGradingDialog(null);
    }
  };

  const getStudentName = (studentId: string) => {
    const student = studentsData.find(s => s.id === studentId);
    return student?.name || `Student ${studentId}`;
  };

  const getStatusColor = () => {
    if (isOverdue) return "from-red-500/10 to-red-600/5 border-red-200";
    if (hasSubmitted) return "from-green-500/10 to-green-600/5 border-green-200";
    return "from-gray-500/10 to-gray-600/5 border-gray-200";
  };

  const getSubmissionForStudent = (studentId: string): AssignmentSubmission => {
    const existing = assignment.submissions?.find(s => s.studentId === studentId);
    return existing || {
      studentId,
      submittedAt: new Date(),
      status: "pending"
    };
  };

  // Function to strip HTML tags
  const stripHtmlTags = (html: string): string => {
    return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();
  };

  // Check if the description has generated questions
  const hasGeneratedQuestions = assignment.description.includes("**Generated Questions:**") || (assignment.questions && assignment.questions.length > 0);
  
  let assignmentDescription = stripHtmlTags(assignment.description);
  let generatedQuestions: any[] = [];
  
  // First check if questions are in the questions array
  if (assignment.questions && assignment.questions.length > 0) {
    generatedQuestions = assignment.questions.map((q: any) => {
      if (typeof q === 'string') {
        return { text: q, type: 'text' };
      }
      return {
        id: q.id,
        text: q.questionText || q.text || q,
        type: q.type === 'TRUE_FALSE' ? 'TRUE_FALSE' : (q.type === 'MULTIPLE_CHOICE' ? 'mcq' : 'text'),
        options: q.options || (q.type === 'TRUE_FALSE' ? ['True', 'False'] : [])
      };
    });
  }
  // Fallback to parsing from description
  else if (assignment.description?.includes("**Generated Questions:**")) {
    const parts = assignment.description.split("**Generated Questions:**");
    assignmentDescription = parts[0].trim();
    
    if (parts[1]) {
      generatedQuestions = parts[1]
        .split("\n\n")
        .filter(line => line.trim())
        .map(line => ({ text: line.replace(/^\d+\.\s/, '').trim(), type: 'text' }));
    }
  }

  // Calculate performance metrics from submitted assignment data
  const correctCount = isSubmitted && submittedAssignment ? Math.round((submittedAssignment.obtainedMarks / submittedAssignment.totalMarks) * generatedQuestions.length) : 0;
  const wrongCount = isSubmitted && submittedAssignment ? generatedQuestions.length - correctCount : 0;
  const scorePercentage = isSubmitted && submittedAssignment ? submittedAssignment.percentage : 0;

  // Detect question type and options from question text
  const detectQuestionType = (question: string): { type: 'mcq' | 'text' | 'TRUE_FALSE', options?: string[] } => {
    // Check if question contains multiple choice options (a), b), c), etc. or A), B), C), etc.
    const mcqPattern = /\n?\s*[a-dA-D][\)\.]\s*.+/g;
    const matches = question.match(mcqPattern);
    
    if (matches && matches.length >= 2) {
      const options = matches.map(opt => opt.trim().replace(/^[a-dA-D][\)\.]\s*/, ''));
      return { type: 'mcq', options };
    }
    
    return { type: 'text' };
  };

  const currentQuestionData = generatedQuestions[currentQuestionIndex] 
    ? (generatedQuestions[currentQuestionIndex].type === 'TRUE_FALSE'
        ? { type: 'TRUE_FALSE' as const, options: ['True', 'False'] }
        : generatedQuestions[currentQuestionIndex].type === 'mcq' 
        ? { type: 'mcq' as const, options: generatedQuestions[currentQuestionIndex].options }
        : detectQuestionType(generatedQuestions[currentQuestionIndex].text))
    : { type: 'text' as const };
  
  // Extract question text without options for MCQ
  const getQuestionText = (question: string, questionData: { type: 'mcq' | 'text' | 'TRUE_FALSE', options?: string[] }) => {
    if (questionData.type === 'mcq') {
      return question.split(/\n?\s*[a-dA-D][\)\.]/)[0].trim();
    }
    return question;
  };

  return (
    <div className="min-h-screen bg-gray-50 p-2 md:p-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-3 md:mb-4">
          <Button 
            variant="ghost" 
            onClick={() => navigate(`/class/${classId}/student-manageclass?tab=assignments`)}
            className="hover:bg-purple-100 hover:text-purple-700"
          >
            <ArrowLeft className="mr-2 h-4 w-4 md:h-5 md:w-5" />
            Back to Class
          </Button>
          <Button 
              className="gap-2 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white"
              onClick={() => navigate(`/class/${classId}/student-manageclass?tab=assignments`)}
            >
              Close Assignment
            </Button>
          
        </div>
        
        <div className="grid gap-3 md:gap-4">
          <Card className={`bg-gradient-to-br ${getStatusColor()} border-2 shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="p-3 md:p-5">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-primary" />
                    <CardTitle className="text-xl md:text-3xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                      {assignment.title}
                    </CardTitle>
                  </div>
                  <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1.5">
                      <GraduationCap className="h-4 w-4" />
                      <span className="font-medium">{currentClass?.className || "Class"}</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <Calendar className="h-4 w-4" />
                      <span>Due: {format(new Date(assignment.dueDate), "PPP")}</span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  {isOverdue ? (
                    <Badge className="bg-red-600 hover:bg-red-700 text-white border-0 shadow-md">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Overdue
                    </Badge>
                  ) : (
                    hasSubmitted ? (
                      <Badge className="bg-green-600 hover:bg-green-700 text-white border-0 shadow-md">
                        <CheckCircle2 className="h-3 w-3 mr-1" />
                        Submitted
                      </Badge>
                    ) : (
                      <Badge className="bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-md">
                        <Clock className="h-3 w-3 mr-1" />
                        Pending
                      </Badge>
                    )
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-3 md:p-5 space-y-4">
              
                <div className="p-4 md:p-5 bg-gradient-to-br from-muted/50 to-muted/30 rounded-lg border-2 border-muted shadow-sm">
                  <p className="whitespace-pre-line text-sm md:text-base leading-relaxed">{assignmentDescription}</p>
                </div>
              
              
              {generatedQuestions.length > 0 && (
                <div>
                    {assignment.isSubmitted && (
                    <div className="mb-4">
                      <Button
                        variant="ghost"
                        onClick={() => setShowPerformanceSummary(!showPerformanceSummary)}
                        className="w-full justify-between hover:bg-purple-50 mb-2 px-3 py-2 h-auto"
                      >
                        <span className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                          <TrendingUp className="h-4 w-4 text-purple-600" />
                          Performance Summary
                        </span>
                        {showPerformanceSummary ? (
                          <ChevronUp className="h-4 w-4 text-gray-500" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-gray-500" />
                        )}
                      </Button>

                      {showPerformanceSummary && (
                        <Card className="overflow-hidden bg-gradient-to-br from-purple-50 via-indigo-50/50 to-blue-50/50 border border-purple-200 shadow-sm">
                          <CardContent className="space-y-2 p-3">
                            {/* Stats Grid */}
                            <div className="grid grid-cols-3 gap-2">
                              <div className="bg-gradient-to-br from-green-50 to-green-100/80 rounded-md p-2 border border-green-200">
                                <div className="flex flex-col items-center gap-1">
                                  <div className="p-1 bg-green-500 rounded shadow-sm">
                                    <CheckCircle2 className="h-3 w-3 text-white" />
                                  </div>
                                  <div className="text-center">
                                    <div className="text-xl font-bold text-green-700">{correctCount}</div>
                                    <div className="text-[10px] font-medium text-green-600">Correct</div>
                                  </div>
                                </div>
                              </div>
                              
                              <div className="bg-gradient-to-br from-red-50 to-red-100/80 rounded-md p-2 border border-red-200">
                                <div className="flex flex-col items-center gap-1">
                                  <div className="p-1 bg-red-500 rounded shadow-sm">
                                    <AlertCircle className="h-3 w-3 text-white" />
                                  </div>
                                  <div className="text-center">
                                    <div className="text-xl font-bold text-red-700">{wrongCount}</div>
                                    <div className="text-[10px] font-medium text-red-600">Wrong</div>
                                  </div>
                                </div>
                              </div>
                              
                              <div className="bg-gradient-to-br from-purple-50 to-indigo-100/80 rounded-md p-2 border border-purple-200">
                                <div className="flex flex-col items-center gap-1">
                                  <div className="p-1 bg-gradient-to-r from-purple-500 to-indigo-600 rounded shadow-sm">
                                    <Sparkles className="h-3 w-3 text-white" />
                                  </div>
                                  <div className="text-center">
                                    <div className="text-xl font-bold bg-gradient-to-r from-purple-700 to-indigo-600 bg-clip-text text-transparent">
                                      {Math.round(scorePercentage)}%
                                    </div>
                                    <div className="text-[10px] font-medium text-purple-600">Score</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            {/* Progress Bar */}
                            <div className="bg-white/60 backdrop-blur-sm rounded-md p-2 border border-purple-200/50">
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-[10px] font-semibold text-gray-700">Progress</span>
                                <span className="text-[10px] font-medium text-gray-600">
                                  {correctCount}/{generatedQuestions.length}
                                </span>
                              </div>
                              <Progress 
                                value={scorePercentage} 
                                className="h-1.5 bg-gray-200"
                              />
                            </div>
                            
                            {/* Breakdown Toggle */}
                            <div className="border-t border-purple-200/50 pt-2">
                              <Button
                                variant="ghost"
                                onClick={() => setShowBreakdown(!showBreakdown)}
                                className="w-full justify-between hover:bg-purple-50 transition-colors group py-1.5 h-auto"
                              >
                                <span className="text-xs font-semibold text-gray-700 flex items-center gap-1.5">
                                  <FileSearch className="h-3 w-3" />
                                  Question Breakdown
                                </span>
                                {showBreakdown ? (
                                  <ChevronUp className="h-3.5 w-3.5 text-gray-500 group-hover:text-purple-700 transition-colors" />
                                ) : (
                                  <ChevronDown className="h-3.5 w-3.5 text-gray-500 group-hover:text-purple-700 transition-colors" />
                                )}
                              </Button>
                              
                              {showBreakdown && (
                                <div className="mt-2 space-y-1.5 animate-fade-in">
                                  {generatedQuestions.map((question, index) => {
                                    const status = getQuestionStatus(index);
                                    return (
                                      <div 
                                        key={index}
                                        className={`p-2 rounded-md flex items-start gap-2 transition-all duration-200 hover:scale-[1.01] ${
                                          status === "correct" 
                                            ? "bg-gradient-to-r from-green-50 to-green-100/70 border border-green-300" 
                                            : "bg-gradient-to-r from-red-50 to-red-100/70 border border-red-300"
                                        }`}
                                      >
                                        <div className={`p-0.5 rounded ${
                                          status === "correct" ? "bg-green-500" : "bg-red-500"
                                        }`}>
                                          {status === "correct" ? (
                                            <CheckCircle2 className="h-3 w-3 text-white flex-shrink-0" />
                                          ) : (
                                            <AlertCircle className="h-3 w-3 text-white flex-shrink-0" />
                                          )}
                                        </div>
                                        <div className="flex-1">
                                          <div className="flex items-center gap-1.5 mb-0.5">
                                            <span className="text-[10px] font-bold text-gray-500">
                                              Q{index + 1}
                                            </span>
                                            <Badge variant="outline" className={
                                              status === "correct" 
                                                ? "bg-green-100 text-green-700 border-green-300 text-[10px] py-0 px-1 h-4" 
                                                : "bg-red-100 text-red-700 border-red-300 text-[10px] py-0 px-1 h-4"
                                            }>
                                              {status === "correct" ? "✓" : "✗"}
                                            </Badge>
                                          </div>
                                          <div className="text-[11px] font-medium text-gray-800 leading-relaxed">
                                            {question.text}
                                          </div>
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                    )}

                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-lg flex items-center gap-2">
                      <Wand2 className="h-5 w-5 text-purple-600" />
                       "Question"
                        </h3>
                      <Badge className="bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-md">
                        Question {currentQuestionIndex + 1} of {generatedQuestions.length}
                      </Badge>
                    
                  </div>
                  
                    <div className="space-y-4">
                      <div className="mb-4">
                        <Progress value={((currentQuestionIndex + 1) / generatedQuestions.length) * 100} className="h-2" />
                      </div>
                      <div className="p-6 md:p-8 bg-gradient-to-br from-white to-purple-50/30 rounded-lg border-2 border-purple-200 shadow-md">
                        <div className="w-full space-y-6">
                          <div className="flex items-center justify-between mb-2">
                            <div className="text-sm text-muted-foreground font-medium">Question {currentQuestionIndex + 1}</div>
                            {studentAnswers[currentQuestionIndex] && (
                              <Badge className="bg-purple-600 text-white gap-1">
                                <CheckCircle2 className="h-3 w-3" />
                                Answered
                              </Badge>
                            )}
                          </div>
                          <p className="text-base md:text-lg leading-relaxed font-semibold text-gray-800">
                            {getQuestionText(generatedQuestions[currentQuestionIndex].text, currentQuestionData)}
                          </p>

                          {/* Answer Input Section */}
                          <div className="pt-4 border-t border-purple-200">
                            <div className="flex items-center gap-2 mb-4">
                              <PenSquare className="h-4 w-4 text-purple-600" />
                              <h4 className="font-semibold text-gray-700">Your Answer</h4>
                            </div>
                            
                            {currentQuestionData.type === 'TRUE_FALSE' ? (
                              <RadioGroup 
                                value={studentAnswers[currentQuestionIndex] || ""} 
                                onValueChange={(value) => handleAnswerChange(currentQuestionIndex, value)}
                                className="space-y-3"
                              >
                                {['True', 'False'].map((option, index) => (
                                  <div 
                                    key={index} 
                                    className="flex items-start space-x-3 p-4 rounded-lg border-2 border-purple-100 hover:border-purple-300 hover:bg-purple-50/50 transition-all cursor-pointer group"
                                  >
                                    <RadioGroupItem 
                                      value={option} 
                                      id={`option-${currentQuestionIndex}-${index}`} 
                                      className="mt-0.5"
                                    />
                                    <Label 
                                      htmlFor={`option-${currentQuestionIndex}-${index}`}
                                      className="flex-1 cursor-pointer text-sm md:text-base font-medium leading-relaxed group-hover:text-purple-700"
                                    >
                                      {option}
                                    </Label>
                                  </div>
                                ))}
                              </RadioGroup>
                            ) : currentQuestionData.type === 'mcq' && currentQuestionData.options ? (
                              <RadioGroup 
                                value={studentAnswers[currentQuestionIndex] || ""} 
                                onValueChange={(value) => handleAnswerChange(currentQuestionIndex, value)}
                                className="space-y-3"
                              >
                                {currentQuestionData.options.map((option, index) => (
                                  <div 
                                    key={index} 
                                    className="flex items-start space-x-3 p-4 rounded-lg border-2 border-purple-100 hover:border-purple-300 hover:bg-purple-50/50 transition-all cursor-pointer group"
                                  >
                                    <RadioGroupItem 
                                      value={option} 
                                      id={`option-${currentQuestionIndex}-${index}`} 
                                      className="mt-0.5"
                                    />
                                    <Label 
                                      htmlFor={`option-${currentQuestionIndex}-${index}`}
                                      className="flex-1 cursor-pointer text-sm md:text-base font-medium leading-relaxed group-hover:text-purple-700"
                                    >
                                      <span className="font-semibold text-purple-600 mr-2">
                                        {String.fromCharCode(65 + index)}.
                                      </span>
                                      {option}
                                    </Label>
                                  </div>
                                ))}
                              </RadioGroup>
                            ) : (
                              <RichTextEditor
                                value={studentAnswers[currentQuestionIndex] || ""}
                                onChange={(value) => handleAnswerChange(currentQuestionIndex, value)}
                                placeholder="Type your answer here..."
                                className="border-2 border-purple-200 focus:border-purple-400 focus:ring-purple-400"
                                readOnly={hasSubmitted}
                              />
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-between gap-3 pt-2">
                        <Button
                          variant="outline"
                          onClick={() => setCurrentQuestionIndex(prev => Math.max(0, prev - 1))}
                          disabled={currentQuestionIndex === 0}
                          className="gap-2 hover:bg-purple-50 hover:border-purple-300"
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Previous
                        </Button>
                        {currentQuestionIndex === generatedQuestions.length - 1 && !assignment.isSubmitted ? (
                          <Button 
                            onClick={handleSubmit}
                            disabled={hasSubmitted || submitting || submissionSuccess}
                            className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 gap-2"
                          >
                            <CheckCircle2 className="h-5 w-5" />
                            {submitting ? "Submitting..." : submissionSuccess ? "Submitted" : hasSubmitted ? "Already Submitted" : "Mark as Submitted"}
                          </Button>
                        ) : currentQuestionIndex !== generatedQuestions.length - 1 && (
                          <Button
                            variant="outline"
                            onClick={() => setCurrentQuestionIndex(prev => Math.min(generatedQuestions.length - 1, prev + 1))}
                            className="gap-2 hover:bg-purple-50 hover:border-purple-300"
                          >
                            Next
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  
                </div>
              )}

              
             
             
            </CardContent>
          </Card>
        </div>
      </div>

      {gradingDialog && (
        <SubmissionGradingDialog
          open={gradingDialog.open}
          onOpenChange={(open) => !open && setGradingDialog(null)}
          studentName={gradingDialog.studentName}
          submission={gradingDialog.submission}
          onSave={handleGradeSubmission}
        />
      )}

      {viewDialog && (
        <SubmissionViewDialog
          open={viewDialog.open}
          onOpenChange={(open) => !open && setViewDialog(null)}
          studentName={viewDialog.studentName}
          submission={viewDialog.submission}
          assignment={assignment}
        />
      )}
    </div>
  );
}