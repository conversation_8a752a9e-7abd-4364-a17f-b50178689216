
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ChevronLeft, Search, Filter, ArrowUpDown, Download, Receipt } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useApp } from "@/context/AppContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import SidebarNavigation from "@/components/SidebarNavigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function StudentPaymentsPage() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { payments, classes } = useApp();
  
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [sortBy, setSortBy] = useState<"date" | "amount">("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  
  // Filter payments for the logged-in student
  let studentPayments = payments.filter(payment => payment.studentId === user?.id);
  
  // Apply filters
  studentPayments = studentPayments.filter(payment => {
    const matchesSearch = searchTerm === "" || 
      payment.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      classes.find(c => c.id === payment.classId)?.className.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === "all" || payment.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });
  
  // Apply sorting
  studentPayments.sort((a, b) => {
    if (sortBy === "date") {
      return sortOrder === "asc" 
        ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    } else {
      return sortOrder === "asc" ? a.amount - b.amount : b.amount - a.amount;
    }
  });
  
  // Format amount to currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };
  
  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  const handleToggleSort = (field: "date" | "amount") => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <SidebarNavigation activeItem="payment" />
      
      {/* Main content */}
      <div className="flex-grow">
        {/* Header */}
        <header className="bg-white p-6 flex justify-between items-center border-b">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              className="p-2" 
              onClick={() => navigate('/student-dashboard')}
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-semibold">My Payments</h1>
          </div>
        </header>

        {/* Content */}
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input 
                placeholder="Search payments..." 
                className="edu-form-field pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-5 bg-slate-50 p-3 text-xs font-medium text-slate-600">
                  <div className="flex items-center cursor-pointer" onClick={() => handleToggleSort("date")}>
                    Date 
                    <ArrowUpDown className="h-3 w-3 ml-1" />
                  </div>
                  <div>Class</div>
                  <div>Description</div>
                  <div className="flex items-center cursor-pointer" onClick={() => handleToggleSort("amount")}>
                    Amount
                    <ArrowUpDown className="h-3 w-3 ml-1" />
                  </div>
                  <div>Status</div>
                </div>
                <div className="divide-y">
                  {studentPayments.length === 0 ? (
                    <div className="p-4 text-center text-sm text-gray-500">
                      No payments found
                    </div>
                  ) : (
                    studentPayments.map((payment) => {
                      const classItem = classes.find(c => c.id === payment.classId);
                      return (
                        <div key={payment.id} className="grid grid-cols-5 p-3 text-sm">
                          <div>{formatDate(payment.createdAt)}</div>
                          <div>{classItem?.className || 'N/A'}</div>
                          <div>{payment.description}</div>
                          <div>{formatCurrency(payment.amount)}</div>
                          <div>
                            <Badge className={`px-2 py-1 rounded-full text-xs ${
                              payment.status === 'completed' 
                                ? 'bg-green-100 text-green-800' 
                                : payment.status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-red-100 text-red-800'
                            }`}>
                              {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                            </Badge>
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
