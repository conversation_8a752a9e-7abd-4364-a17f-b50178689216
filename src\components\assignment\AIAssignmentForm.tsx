import { format } from 'date-fns';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Sparkles,Wand2, Check, Trash2,Lightbulb ,Award,CalendarIcon} from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "react-oidc-context";
import {  QuestionGenParams } from "@/utils/llmService";
import { generateAIQuestions, deleteExistingQuestion } from "@/services/assignmentService";
import { toast } from "sonner";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
interface AIAssignmentFormProps {
  onSuccess?: () => void;
  onQuestionsGenerated?: (questions: any[]) => void;
  onSubmit?: (values: any) => void;
  assignment?: any;
  mode?: 'create' | 'edit' | 'view';
  assignmentId?: string;
}

export function AIAssignmentForm({ onSuccess, onQuestionsGenerated, onSubmit, assignment, mode = 'create', assignmentId }: AIAssignmentFormProps) {
  
  // Clear generated content when component unmounts or when explicitly needed
  const clearGeneratedContent = () => {
    setGeneratedContent(null);
  };
  const [date, setDate] = useState<Date>(assignment?.dueDate ? new Date(assignment.dueDate) : undefined);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<any>(() => {
    if (assignment && mode !== 'create') {
      return {
        title: assignment.title || 'AI Generated Assignment',
        description: assignment.description || '',
        questions: assignment.questions || []
      };
    }
    return null;
  });
const [title, setTitle] = useState(assignment?.title || "");
const [prompt, setPrompt] = useState(assignment?.description || "");
const [gradeLevel,setGradeLevel] = useState("");
const [count, setCount] = useState(assignment?.questions?.length || 5);
  const [difficulty, setDifficulty] = useState<"easy" | "medium" | "hard">("medium");
  const [type, setType] = useState<"multiple-choice" | "essay" | "true-false" | "all">("all");
  const auth = useAuth();

  const handleRemoveGeneratedQuestion = async (questionId: string | undefined, assignmentIdParam: string, index: number) => {
    if (!window.confirm("Remove this question?")) return;

    // If question has an ID and assignment exists, it's a saved question - delete from API
    if (questionId && assignmentIdParam) {
      try {
        await deleteExistingQuestion(auth.user.access_token, assignmentIdParam, questionId);
        toast.success("Question removed");
        onSuccess?.();
      } catch (err) {
        console.error("Failed to remove question", err);
        toast.error("Failed to remove question");
        return;
      }
    }
    
    // Remove from local state (works for both new and saved questions)
    setGeneratedContent(prev => ({
      ...prev,
      questions: prev.questions.filter((_: any, i: number) => i !== index)
    }));
    
    if (!questionId) {
      toast.success("Question removed");
    }
  };

  // Clear generated content when switching to create mode or when assignment changes
  useEffect(() => {
    if (mode === 'create' && !assignment) {
      setGeneratedContent(null);
      return;
    }
  }, [mode, assignment?.id]);

  // Update state when assignment prop changes
  useEffect(() => {
    if (assignment) {
      setTitle(assignment.title || '');
      setDate(assignment.dueDate ? new Date(assignment.dueDate) : undefined);
      setPrompt(assignment.description || '');
      setCount(assignment.questions?.length || 5);
      setGradeLevel(assignment.gradeLevel || '');
      
      if (assignment.questions && assignment.questions.length > 0) {
        // Ensure MCQ questions have options and correct type
        const questionsWithOptions = assignment.questions.map((q: any) => {
          // If question has options or should be MCQ, ensure it has proper type and options
          if ((q.type === 'MULTIPLE_CHOICE' || q.type === 'mcq') || 
              (q.options && q.options.length > 0) ||
              (q.questionText && (q.questionText.includes('Which of') || q.questionText.includes('Select') || q.questionText.includes('Choose')))) {
            return {
              ...q,
              type: 'MULTIPLE_CHOICE',
              options: q.options && q.options.length > 0 ? q.options : ['Option A', 'Option B', 'Option C', 'Option D']
            };
          }
          return q;
        });
        
        setGeneratedContent({
          title: assignment.title || 'AI Generated Assignment',
          description: assignment.description || '',
          questions: questionsWithOptions
        });
      } else {
        setGeneratedContent({
          title: assignment.title || 'AI Generated Assignment',
          description: assignment.description || '',
          questions: []
        });
      }
    } else if (mode === 'create') {
      // Reset form for create mode
      setTitle('');
      setPrompt('');
      setGradeLevel('');
      setCount(5);
      setDate(undefined);
      setGeneratedContent(null);
    }
  }, [assignment, mode]);
  const validateForm = () => {
    if (!title.trim()) {
      toast.error("Please enter a title first");
      return false;
    }
    if (!prompt.trim()) {
      toast.error("Please enter a prompt first");
      return false;
    }
    if (!gradeLevel.trim()) {
      toast.error("Please enter a grade level");
      return false;
    }
    if (count < 1 || count > 20) {
      toast.error("Number of questions must be between 1 and 20");
      return false;
    }
    return true;
  };

  const handleGenerate = async () => {
    if (!validateForm()) {
      return;
    }

    setIsGenerating(true);
    const token = auth.user?.access_token;
    if (!token) {
      toast.error("You must be signed in to generate questions");
      return;
    }

    setIsGenerating(true);

    try {
      // map UI values to service payload shape
      const difficultyMap: Record<string, string> = {
        easy: "EASY",
        medium: "MEDIUM",
        hard: "HARD",
      };

      const typeMap: Record<string, string[]> = {
        "multiple-choice": ["MULTIPLE_CHOICE"],
        essay: ["SHORT_ANSWER"],
        "true-false": ["TRUE_FALSE"],
        all: ["MULTIPLE_CHOICE", "SHORT_ANSWER", "TRUE_FALSE"],
      };

      const payload = {
        topic: "General", // adjust or expose as an input if you want user-controlled topic
        difficultyLevel: difficultyMap[difficulty] || "MEDIUM",
        numberOfQuestions: count,
        questionTypes: typeMap[type] || ["SHORT_ANSWER"],
        additionalInstructions: prompt.trim(),
        gradeLevel: gradeLevel, // change or expose if needed
        marksPerQuestion: 2, // change or expose if needed
      };

      console.log("generateAIQuestions payload:", payload);
       const response = await generateAIQuestions(token, payload as any);
      // service returns an array of question objects (or may return { questions: [...] })
      const data: any[] = Array.isArray(response) ? response : (response?.questions ?? []);
      console.log("generateAIQuestions response length:", data.length);

      if (data.length > 0) {
        console.log("Generated questions data:", data);
        
        // Ensure questions have proper types and options
        const processedQuestions = data.map((q: any) => {
          // If the question type is not set or is incorrect, try to infer it
          let questionType = q.type || 'LONG_ANSWER';
          
          // If question has options, it should be MULTIPLE_CHOICE
          if (q.options && q.options.length > 0) {
            questionType = 'MULTIPLE_CHOICE';
          }
          
          // If question text suggests MCQ but no options, add default options
          if ((q.questionText || q.question || '').match(/which of|select|choose/i) && questionType !== 'MULTIPLE_CHOICE') {
            questionType = 'MULTIPLE_CHOICE';
          }
          
          const processedQuestion = {
            ...q,
            type: questionType,
            options: questionType === 'MULTIPLE_CHOICE' ? 
              (q.options && q.options.length > 0 ? q.options : ['Option A', 'Option B', 'Option C', 'Option D']) : 
              []
          };
          
          // Ensure correct answer is set for MCQ questions
          if (questionType === 'MULTIPLE_CHOICE' && processedQuestion.options.length > 0) {
            // If correctAnswer is not set or not in options, set it to the first option
            if (!processedQuestion.correctAnswer || !processedQuestion.options.includes(processedQuestion.correctAnswer)) {
              processedQuestion.correctAnswer = processedQuestion.options[0];
            }
          }
          
          return processedQuestion;
        });
        
        // pass the object array to AssignmentForm which maps it into UI shape
        onQuestionsGenerated?.(processedQuestions);
        setGeneratedContent({
          title: title || 'AI Generated Assignment',
          description: prompt,
          questions: processedQuestions
        });
        toast.success(`Generated ${data.length} questions`);
        setPrompt("");
      } else {
        toast.error("Failed to generate questions");
      }
   } catch (error) {
      console.error("Error generating questions:", error);
      toast.error("An error occurred while generating questions");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Title */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {mode === 'create' ? 'Create Assignment' : mode === 'edit' ? 'Edit Assignment' : 'View Assignment'}
        </h1>
      </div>
      {/* AI Prompt Section */}
      {mode === 'create' && (
      <Card className="p-0 sm:p-6 border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-blue-50">
        <div className="flex items-start gap-3 mb-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="title" className="text-gray-900 mb-2 block">
                  Assignment Title *
                </Label>
                <Input
                  id="title"
                  placeholder="e.g., Chapter 5 Review: Photosynthesis"
                  className="edu-form-field"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="prompt" className="text-gray-900 mb-2 block">
                  Describe Your Assignment
                </Label>
            <RichTextEditor
              value={prompt}
              onChange={setPrompt}
              placeholder="Example: Create a 10-question assignment about photosynthesis for 7th grade biology students. Include multiple choice and short answer questions about the process, importance, and factors affecting photosynthesis."
            />
                <p className="text-gray-600 mt-2">
                  Be specific about the topic, grade level, question types, and learning objectives
                </p>
              </div>
            </div>
          
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
          <div className="space-y-2">
            <Label htmlFor="grade-level">Grade Level</Label>
            <Input 
              id="grade-level" 
              placeholder="e.g., 7th Grade"
           className="edu-form-field"
              value={gradeLevel}
              onChange={(e) => setGradeLevel(e.target.value)}
            />
          </div>

          
          <div className="space-y-2">
            <Label htmlFor="difficulty">Difficulty</Label>
            <Select  value={difficulty} onValueChange={(value: any) => setDifficulty(value)}>
              <SelectTrigger id="difficulty" className="edu-form-field">
                <SelectValue placeholder="Select difficulty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="easy">Easy</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="hard">Hard</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
             <Label htmlFor="count">Number of Questions</Label>
            <Input
              id="count"
              type="number"
              min={1}
              className="edu-form-field"
              max={20}
              value={count}
              onChange={(e) => setCount(Number(e.target.value))}
            />
          </div>
        </div>
 <div className="space-y-2">
          <Label>Question Type</Label>
          <RadioGroup 
            value={type} 
            onValueChange={(value: any) => setType(value)}
            className="flex flex-col space-y-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="all" />
              <Label htmlFor="all">Mixed Types</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="multiple-choice" id="multiple-choice" />
              <Label htmlFor="multiple-choice">Multiple Choice</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="essay" id="essay" />
              <Label htmlFor="essay">Essay/Short Answer</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="true-false" id="true-false" />
              <Label htmlFor="true-false">True/False</Label>
            </div>
          </RadioGroup>
        </div>
        <Button 
          onClick={handleGenerate}
          disabled={isGenerating}
          className="w-full  mt-3 bg-purple-600 hover:bg-purple-700"
        >
          {isGenerating ? (
            <>
              <Wand2 className="w-4 h-4 animate-spin" />
              Generating Assignment...
            </>
          ) : (
            <>
              <Sparkles className="w-4 h-4" />
              Generate Assignment with AI
            </>
          )}
        </Button>
      </Card>
      )}

      {/* Generated Content Preview */}
      {generatedContent && (
        <Card className="p-0 sm:p-6 border-2 border-gray-200">
          
          <div className="space-y-4 mb-6">
            <div>
              <Label className="text-gray-700 mb-1 block">Title</Label>
              <Input 
                value={generatedContent.title} 
                onChange={(e) => setGeneratedContent({...generatedContent, title: e.target.value})}
              className="edu-form-field"
                readOnly={mode === 'view'}
              />
            </div>
            
            <div>
              <Label className="text-gray-700 mb-1 block">Description</Label>
              <RichTextEditor
                value={generatedContent.description}
                onChange={(value) => setGeneratedContent({...generatedContent, description: value})}
                readOnly={mode === 'view'}
              />
            </div>

            <div>
              <Label className="text-gray-700 mb-2 block">Questions ({generatedContent.questions.length})</Label>
              <div className="space-y-3">
                {generatedContent.questions.map((q: any, idx: number) => (
                  <div key={idx} className="p-4 bg-white rounded-lg border border-gray-200 space-y-3">
                    <div className="flex flex-col sm:flex-row items-start gap-3">
                      <div className="px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded border self-start">Q{idx + 1}</div>
                      <div className="flex-1 space-y-3 w-full">
                        <Textarea
                          value={q.questionText || q.question || ''}
                          onChange={(e) => {
                            const newQuestions = [...generatedContent.questions];
                            newQuestions[idx] = { ...newQuestions[idx], questionText: e.target.value, question: e.target.value };
                            setGeneratedContent({...generatedContent, questions: newQuestions});
                          }}
                          className="edu-form-field"rows={2}
                          readOnly={mode === 'view'}
                        />
                        
                        {mode !== 'view' && (
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                            <Label className="text-gray-600">Question Type:</Label>
                            <Select 
                              value={q.type || (q.options && q.options.length > 0 ? 'MULTIPLE_CHOICE' : 'LONG_ANSWER')} 
                              onValueChange={(value) => {
                                console.log(`Changing question ${idx + 1} type from ${q.type} to ${value}`);
                                const newQuestions = [...generatedContent.questions];
                                newQuestions[idx] = { 
                                  ...newQuestions[idx], 
                                  type: value,
                                  options: value === 'MULTIPLE_CHOICE' ? 
                                    (newQuestions[idx].options && newQuestions[idx].options.length > 0 ? newQuestions[idx].options : ['Option A', 'Option B', 'Option C', 'Option D']) : 
                                    []
                                };
                                setGeneratedContent({...generatedContent, questions: newQuestions});
                              }}
                            >
                              <SelectTrigger className="edu-form-field w-full sm:w-40">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="MULTIPLE_CHOICE">Multiple Choice</SelectItem>
                                <SelectItem value="TRUE_FALSE">True/False</SelectItem>
                                <SelectItem value="LONG_ANSWER">Long Answer</SelectItem>
                                <SelectItem value="SHORT_ANSWER">Short Answer</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                        
                        {q.type === 'MULTIPLE_CHOICE' && (
                          <div className="space-y-2 pl-4">
                            <Label className="text-gray-600">{mode === 'view' ? 'Answer Options' : 'Answer Options (Click to mark correct answer)'}</Label>
                            {(q.options && q.options.length > 0 ? q.options : ['Option A', 'Option B', 'Option C', 'Option D']).map((option: string, optionIdx: number) => (
                              <div key={optionIdx} className={`flex flex-col sm:flex-row sm:items-center gap-2 p-2 rounded ${
                                mode === 'view' && q.correctAnswer === option ? 'bg-green-50 border border-green-200' : ''
                              }`}>
                                <div className="flex items-center gap-2 w-full">
                                  <button
                                    type="button"
                                    onClick={() => {
                                      if (mode !== 'view') {
                                        const newQuestions = [...generatedContent.questions];
                                        newQuestions[idx] = { ...newQuestions[idx], correctAnswer: option };
                                        setGeneratedContent({...generatedContent, questions: newQuestions});
                                      }
                                    }}
                                    disabled={mode === 'view'}
                                    className={`w-6 h-6 rounded-full border-2 flex items-center justify-center flex-shrink-0 transition-colors ${
                                      q.correctAnswer === option 
                                        ? 'border-green-600 bg-green-600' 
                                        : 'border-gray-300 bg-white hover:border-green-400'
                                    }`}
                                  >
                                    {q.correctAnswer === option && (
                                      <Check className="w-4 h-4 text-white" />
                                    )}
                                  </button>
                                  <span className="text-gray-500 w-6 flex-shrink-0">{String.fromCharCode(65 + optionIdx)}.</span>
                                  <Input
                                    value={option}
                                    readOnly={mode === 'view'}
                                    onChange={(e) => {
                                      if (mode !== 'view') {
                                        const newQuestions = [...generatedContent.questions];
                                        const currentOptions = newQuestions[idx].options && newQuestions[idx].options.length > 0 
                                          ? [...newQuestions[idx].options] 
                                          : ['Option A', 'Option B', 'Option C', 'Option D'];
                                        currentOptions[optionIdx] = e.target.value;
                                        newQuestions[idx] = { 
                                          ...newQuestions[idx], 
                                          type: 'MULTIPLE_CHOICE',
                                          options: currentOptions 
                                        };
                                        setGeneratedContent({...generatedContent, questions: newQuestions});
                                      }
                                    }}
                                    placeholder={`Option ${String.fromCharCode(65 + optionIdx)}`}
                                    className={`edu-form-field bg-gray-50 flex-1 min-w-0 ${
                                      mode === 'view' && q.correctAnswer === option ? 'font-semibold text-green-800' : ''
                                    }`}
                                  />
                                </div>
                                {mode === 'view' && q.correctAnswer === option && (
                                  <Badge className="bg-green-100 text-green-800 border-green-300 self-start sm:self-center">Correct Answer</Badge>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex sm:flex-col gap-2 self-start">
                        <Badge variant={(q.type === 'MULTIPLE_CHOICE' || q.type === 'mcq') ? 'default' : 'secondary'} className="whitespace-nowrap">
                          {q.type === 'MULTIPLE_CHOICE' ? 'MCQ' : q.type === 'TRUE_FALSE' ? 'T/F' : 'Text'}
                        </Badge>
                       {mode !== 'view' && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleRemoveGeneratedQuestion(q.id, assignmentId || assignment?.id || '', idx)}
                          disabled={generatedContent.questions.length === 1}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                       )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            </div>
            </Card>
      )}

      {/* Assignment Details */}
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="points" className="flex items-center gap-2">
              <Award className="w-4 h-4" />
              Total Points
            </Label>
            <Input 
              id="points" 
              type="number" 
              placeholder="100"
              className="edu-form-field"
              defaultValue="100"
            />
          </div>

          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <CalendarIcon className="w-4 h-4" />
              Due Date
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, 'PPP') : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        
      </div>

      {/* Action Buttons */}
      {mode !== 'view' && (
      <div className="flex gap-3 pt-4">
        
        <Button 
          className="bg-purple-600 hover:bg-purple-700 ml-auto"
          onClick={() => {
            if (!generatedContent?.title?.trim()) {
              toast.error("Please enter a title for the assignment");
              return;
            }
            if (!generatedContent?.description?.trim()) {
              toast.error("Please enter a description for the assignment");
              return;
            }
            if (!generatedContent?.questions?.length) {
              toast.error("Please generate at least one question");
              return;
            }
            if (!date) {
              toast.error("Please select a due date");
              return;
            }
            
            if (generatedContent && onSubmit) {
              // Debug: Log questions before submitting
              console.log('=== SUBMITTING ASSIGNMENT DATA ===');
              console.log('Title:', generatedContent.title);
              console.log('Description:', generatedContent.description);
              console.log('Questions count:', generatedContent.questions.length);
              
              generatedContent.questions.forEach((q: any, idx: number) => {
                console.log(`Question ${idx + 1}:`);
                console.log('  - Type:', q.type);
                console.log('  - Text:', q.questionText || q.question);
                console.log('  - Options:', q.options);
                console.log('  - Correct Answer:', q.correctAnswer);
                console.log('  - Full object:', JSON.stringify(q, null, 2));
              });
              
              const submissionData = {
                title: generatedContent.title,
                description: generatedContent.description,
                dueDate: date,
                questions: generatedContent.questions
              };
              
              console.log('Final submission data:', JSON.stringify(submissionData, null, 2));
              
              // Validate data integrity
              const hasInvalidQuestions = submissionData.questions.some((q: any) => {
                if (q.type === 'MULTIPLE_CHOICE') {
                  return !q.options || q.options.length === 0 || !q.correctAnswer;
                }
                return false;
              });
              
              if (hasInvalidQuestions) {
                console.error('❌ Invalid questions detected before submission!');
                toast.error('Some multiple choice questions are missing options or correct answers');
                return;
              }
              
              console.log('✅ All questions validated successfully');
              
              onSubmit(submissionData);
            }
          }}
        >
          {mode === 'edit' ? 'Update Assignment' : 'Create Assignment'}
        </Button>
      </div>
      )}
    </div>
  );
}