import { Link, useNavigate } from "react-router-dom";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useAuth } from "react-oidc-context"; // Updated import
import { useApp } from "@/context/AppContext";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { UserRole } from "@/types";
import { generateAvatarUrl } from "@/lib/utils";
import { joinStudentToClassroom } from "@/services/enrollmentService";

const joinSchema = z.object({
  joinCode: z.string().min(4, "Join code must be at least 4 characters"),
});

type JoinFormValues = z.infer<typeof joinSchema>;

export function JoinClassForm() {
  const auth = useAuth();
  const { joinClass } = useApp();
  const [isSubmitting, setIsSubmitting] = useState(false);
    const navigate = useNavigate();
  
  // Get user from OIDC
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;
  
  const form = useForm<JoinFormValues>({
    resolver: zodResolver(joinSchema),
    defaultValues: {
      joinCode: "",
    },
  });

  async function onSubmit(values: JoinFormValues) {
    if (!user) {
      toast.error("You must be logged in to join a class");
      return;
    }
    
    setIsSubmitting(true);
    
    try {
     const response =   await joinStudentToClassroom( auth.user?.access_token,values.joinCode);
      toast.success("Joined class successfully!");
     navigate("/student-classes");
      form.reset();
    } catch (error) {
      console.error('Error joining class:', error);
      toast.error("Failed to join class. Please check the join code.");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="joinCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Class Join Code</FormLabel>
              <FormControl>
                <Input placeholder="Enter join code (e.g. MATH101)" {...field} className="edu-form-field"
                    />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
                   

        <Button type="submit" className="bg-purple-600 hover:bg-purple-700 w-full" disabled={isSubmitting}>
          {isSubmitting ? "Joining..." : "Join Class"}
        </Button>
      </form>
    </Form>
  );
}

export default JoinClassForm;
