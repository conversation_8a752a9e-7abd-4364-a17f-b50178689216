
import { ParentTeacherMeeting, MeetingSlot } from "@/types";
import meetingsData from "@/data/meetings.json";

export const getMeetingsByTeacher = async (teacherId: string): Promise<ParentTeacherMeeting[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const meetings = meetingsData.map(meeting => ({
        ...meeting,
        date: new Date(meeting.date),
        createdAt: new Date(meeting.createdAt),
        slots: meeting.slots.map(slot => ({
          ...slot,
          startTime: new Date(slot.startTime),
          endTime: new Date(slot.endTime)
        }))
      }));
      const teacherMeetings = meetings.filter(m => m.teacherId === teacherId);
      resolve(teacherMeetings);
    }, 300);
  });
};

export const createMeeting = async (
  meeting: Omit<ParentTeacherMeeting, "id" | "createdAt" | "slots">,
  slots: Omit<MeetingSlot, "id" | "isBooked" | "bookedBy">[]
): Promise<ParentTeacherMeeting> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newMeeting: ParentTeacherMeeting = {
        ...meeting,
        id: `m${Date.now()}`,
        createdAt: new Date(),
        slots: slots.map((slot, index) => ({
          ...slot,
          id: `s${Date.now()}-${index}`,
          isBooked: false
        }))
      };
      console.log("Created new meeting:", newMeeting);
      resolve(newMeeting);
    }, 300);
  });
};

export const deleteMeeting = async (id: string): Promise<boolean> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log("Deleted meeting with ID:", id);
      resolve(true);
    }, 300);
  });
};
