import { useAuth } from "react-oidc-context";
import { createFeeStructure as createFee<PERSON>tructureAP<PERSON> } from "@/services/feeServices";
import React, { useState ,useEffect, useRef} from "react";
import { getFeeStructuresForClassRoom, optInToFeeStructure, getStudentOptedFee }from "@/services/feeServices";
import { FeeStructure, UserRole } from "@/types";
import { useApp } from "@/context/AppContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Plus,
  Edit,
  Trash,
  Check,
  X,
  ChevronsUpDown
} from "lucide-react";
import { toast } from "sonner";
import { getCountryCodesMetadata } from "@/services/classService";
import { deleteFeeStructure as deleteFeeStructureAP<PERSON> } from "@/services/feeServices";
import { updateFeeStructure as updateFeeStructure<PERSON><PERSON> } from "@/services/feeServices";
import { useUserRole } from "@/hooks/useUserRole";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface FeeStructureTableProps {
  classId: string;
  refreshKey?: number;

}

export default function FeeStructureTable({ classId , refreshKey }: FeeStructureTableProps) {
  const { classes, createFeeStructure, updateFeeStructure, deleteFeeStructure } = useApp();
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const auth = useAuth();
  const { selectedRole } = useUserRole();  

  const isTeacher = selectedRole === UserRole.TEACHER;

  const currentClass = classes.find(c => c.id === classId);
  const [feeStructures, setFeeStructures] = useState<FeeStructure[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const hasFetchedFees = useRef(false);
  const hasFetchedCountries = useRef(false);
  const hasFetchedOptedStatus = useRef(false);

  const [newFee, setNewFee] = useState<Omit<FeeStructure, "id"|"status" >>({
    country: "",
    paymentType: "",
    feeAmount: 0,
    discountPercentage: 0,
    description: "" 
  });
  const paymentTypeOptions = [
  { value: "MONTHLY", label: "MONTHLY" },
  { value: "QUARTERLY", label: "QUARTERLY" },
  { value: "YEARLY", label: "YEARLY" },
  { value: "ONE_TIME", label: "ONE_TIME" }
  ];
 const [countryOptions, setCountries] = useState<{ 
        id: string;
        name: string;
        isoCode: string;
        currency: string;
        currencyCode: string;
        flag: string;
        region: string;
        subregion: string;
     }[]>([]);
 
  const [editingFee, setEditingFee] = useState<FeeStructure | null>(null);
  const [optedInFeeId, setOptedInFeeId] = useState<string | null>(null);
  const [countryOpen, setCountryOpen] = useState(false);
  useEffect(() => {
      const fetchFeeStructures = async () => {
        try {
          setIsLoading(true);
          const data = await getFeeStructuresForClassRoom(auth.user.access_token, classId);
          setFeeStructures(data);
        } catch (error) {
          toast.error("Failed to load fee structures");
        } finally {
          setIsLoading(false);
        }
      };
      if (auth.user?.access_token && classId) {
        if (refreshKey !== undefined) {
          // If refreshKey is provided, always fetch
          fetchFeeStructures();
        } else if (!hasFetchedFees.current) {
          // Otherwise, only fetch once
          hasFetchedFees.current = true;
          fetchFeeStructures();
        }
      }
    }, [auth.user?.access_token, classId, refreshKey]);

      useEffect(() => {
          async function fetchCountries() {
            try {
              const data = await getCountryCodesMetadata(auth.user.access_token);
               setCountries(data);
            } catch (error) {
              toast.error("Failed to load countries");
            }
          }
          if (auth.user?.access_token && !hasFetchedCountries.current) {
            hasFetchedCountries.current = true;
            fetchCountries();
          }
        }, [auth.user?.access_token]);
      
  useEffect(() => {
    const checkOptedInStatus = async () => {
      try {
        const optedFee = await getStudentOptedFee(auth.user.access_token, classId);
        if (optedFee && optedFee.feeStructureId) {
          setOptedInFeeId(optedFee.feeStructureId);
        }
      } catch (error) {
        console.log("No opted fee found or error:", error);
      }
    };
    
    if (!isTeacher && auth.user?.access_token && classId && !hasFetchedOptedStatus.current) {
      hasFetchedOptedStatus.current = true;
      checkOptedInStatus();
    }
  }, [auth.user?.access_token, classId, isTeacher]);

  const handleAddNew = async () => {
  try {
    // Optionally, collect fee data from a form or state
    const feeData = {
      country: "",
      paymentType: "",
      feeAmount: 0,
      discountPercentage: 0,
      description :"",
      status: "active"  
    };
    await createFeeStructure(classId, feeData);
    toast.success("Fee structure created!");
    setIsAddingNew(true); // or refresh your data here
    setNewFee(feeData);
  } catch (error) {
    toast.error("Failed to create fee structure");
  }
};

  const handleEditFee = (fee: FeeStructure) => {
    setEditingId(fee.id);
    const selectedCountry = countryOptions.find(opt => opt.isoCode === fee.country || opt.name === fee.country);
    setEditingFee({ ...fee, country: selectedCountry?.isoCode || fee.country });
  };

  const handleDeleteFee = async (feeId: string) => {
  try {
    await deleteFeeStructureAPI(auth.user.access_token, classId, feeId);
    console.log("Fee structure deleted:", feeId);
    toast.success("Fee structure deleted!");
    // Refresh the table by refetching data or updating state
    setFeeStructures(prev => prev.filter(fee => fee.id !== feeId));
  } catch (error) {
    toast.error("Failed to delete fee structure");
  }
};

  const handleSaveNew = async () => {
  if (!newFee.country || !newFee.paymentType || newFee.feeAmount <= 0) {
    toast.error("Please fill in all required fields");
    return;
  }
};


  const handleSaveEdit = async () => {
  if (!editingFee) return;

  if (!editingFee.country || !editingFee.paymentType || editingFee.feeAmount <= 0) {
    toast.error("Please fill in all required fields");
    return;
  }

  try {
    console.log("edit save ",editingFee)
    await updateFeeStructureAPI(auth.user.access_token, classId, editingFee.id, editingFee);
    toast.success("Fee structure updated!");
    setEditingId(null);
    setEditingFee(null);
    // Optionally trigger a refresh here, e.g. setRefreshKey(prev => prev + 1);
      const data = await getFeeStructuresForClassRoom(auth.user.access_token, classId);
    setFeeStructures(data);
  } catch (error) {
    toast.error("Failed to update fee structure");
  }
};

  const handleCancelNew = () => {
    setIsAddingNew(false);
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditingFee(null);
  };

  const handleChangeNew = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewFee(prev => ({
      ...prev,
      [name]: name === "amount" || name === "discount" ? parseFloat(value) || 0 : value
    }));
  };

  const handleChangeEdit = (e: React.ChangeEvent<HTMLInputElement  | HTMLSelectElement>) => {
    if (!editingFee) return;
    
    const { name, value } = e.target;
    setEditingFee(prev => {
      if (!prev) return null;
      
      if (name === "country") {
        const selectedCountry = countryOptions.find(opt => opt.isoCode === value);
        return {
          ...prev,
          country: value,
          currency: selectedCountry?.currencyCode || ""
        };
      }
      
      return {
        ...prev,
        [name]: name === "feeAmount" || name === "discountPercentage" ? parseFloat(value) || 0 : value
      };
    });
  };

  const handleOptIn = async (feeStructureId: string) => {
    try {
      console.log("Opting in to fee structure:", feeStructureId);
      await optInToFeeStructure(auth.user.access_token, classId, feeStructureId);
      setOptedInFeeId(feeStructureId);
      toast.success("Successfully opted in to fee structure!");
    } catch (error) {
      toast.error("Failed to opt in to fee structure");
    }
  };

  return (
    <div className="space-y-4  bg-white">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Fee Structure</h3>
        
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Country/Region</TableHead>
              <TableHead>Fee Type</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Discount (%)</TableHead>
               <TableHead>Discounted Amount</TableHead>
              <TableHead >Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
                      
            {feeStructures.map(fee => (
              <TableRow key={fee.id}>
                <TableCell>
                  {editingId === fee.id ? (
                    <Popover open={countryOpen} onOpenChange={setCountryOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={countryOpen}
                          className="w-full justify-between"
                        >
                          {editingFee?.country
                            ? countryOptions.find(opt => opt.isoCode === editingFee.country)?.flag + " " + 
                              countryOptions.find(opt => opt.isoCode === editingFee.country)?.name + " (" +
                              countryOptions.find(opt => opt.isoCode === editingFee.country)?.currencyCode + ")"
                            : "Select Country..."}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput placeholder="Search country..." />
                          <CommandList>
                            <CommandEmpty>No country found.</CommandEmpty>
                            <CommandGroup>
                              {countryOptions.sort((a, b) => a.name.localeCompare(b.name)).map(opt => (
                                <CommandItem
                                  key={opt.id}
                                  value={`${opt.name} ${opt.currencyCode}`}
                                  onSelect={() => {
                                    const selectedCountry = countryOptions.find(country => country.isoCode === opt.isoCode);
                                    setEditingFee(prev => {
                                      if (!prev) return null;
                                      return {
                                        ...prev,
                                        country: opt.isoCode,
                                        currency: selectedCountry?.currencyCode || ""
                                      };
                                    });
                                    setCountryOpen(false);
                                  }}
                                >
                                  <Check
                                    className={`mr-2 h-4 w-4 ${
                                      editingFee?.country === opt.isoCode ? "opacity-100" : "opacity-0"
                                    }`}
                                  />
                                  {opt.flag} {opt.name} ({opt.currencyCode})
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  ) : (
                    fee.country
                  )}
                </TableCell>
                <TableCell>
                  {editingId === fee.id ? (
                   <select
                  name="paymentType"
                  className="w-full border rounded px-2 py-1"
                  value={editingFee?.paymentType || ""}
                  onChange={handleChangeEdit} >     
                 <option value="">Select Payment Type</option>
                  {paymentTypeOptions.map(opt => (
                    <option key={opt.value} value={opt.value}>{opt.label}</option>
                  ))}
                </select>
                  ) : (
                    fee.paymentType
                  )}
                </TableCell>
                <TableCell>
                  {editingId === fee.id ? (
                    <Input
                    className="edu-form-field"
                   
                      name="feeAmount"
                      type="number"
                      min="0"
                      step="0.01"
                      value={editingFee?.feeAmount || 0}
                      onChange={handleChangeEdit}
                    />
                  ) : (
                     typeof fee.feeAmount === "number"
                      ? `${fee.feeAmount.toFixed(2)}`
                      : "0.00"
                  )}
                </TableCell>
                <TableCell>
                  {editingId === fee.id ? (
                    <Input
                      name="discountPercentage"
                      type="number"
                      className="edu-form-field"
                   
                      min="0"
                      max="100"
                      value={editingFee?.discountPercentage || 0}
                      onChange={handleChangeEdit}
                    />
                  ) : (
                    `${fee.discountPercentage}%`
                  )}
                </TableCell>
                <TableCell>
                  {typeof fee.feeAmount === "number" && typeof fee.discountPercentage === "number"
                    ? `${(fee.feeAmount - (fee.feeAmount * fee.discountPercentage) / 100).toFixed(2)}`
                    : "0.00"}
                </TableCell>
                <TableCell>
                  {isTeacher ? (
                    editingId === fee.id ? (
                      <div className="flex justify-end gap-2">
                        <Button size="sm" variant="ghost" className="bg-purple-600 hover:bg-purple-700"onClick={handleSaveEdit}>
                          <Check className="h-4 w-4 text-green-600" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={handleCancelEdit}>
                          <X className="h-4 w-4 text-red-600" />
                        </Button>
                      </div>
                    ) : (
                      <div className="flex justify-end gap-2">
                       <Button size="sm" variant="ghost" className ="bg-purple-600 hover:bg-purple-700" onClick={() => handleEditFee(fee)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDeleteFee(fee.id)}>
                          <Trash className="h-4 w-4 text-red-600" />
                        </Button>
                      </div>
                    )
                  ) : (
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className={optedInFeeId === fee.id ? "text-green-600 border-green-600" : "text-purple-600 border-purple-600"}
                      onClick={() => handleOptIn(fee.id)}
                      disabled={optedInFeeId !== null}
                    >
                      {optedInFeeId === fee.id ? (
                        <span className="flex items-center">
                          <Check className="h-4 w-4 mr-1" />
                          <span>Opted In</span>
                        </span>
                      ) : "Opt In"}
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ))}
            
            {feeStructures.length === 0 && !isAddingNew && (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-6 text-gray-500">
                  No fee structures defined.
                                  </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
