
import React from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { useAuth } from "react-oidc-context";  // Use OIDC auth
import { Button } from "@/components/ui/button";
import { User, Info, Award, Star, LogOut, Users, BookOpen } from "lucide-react";
import { UserRole } from "@/types";
import { useUserRole } from "@/hooks/useUserRole";
import { useDispatch } from "react-redux";
import { clearUserData } from '@/redux/userSlice';

interface ProfileSidebarProps {
  activePage: "profile" | "about" | "experience" | "reviews" | "students" | string;
}

const ProfileSidebar = ({
  activePage
}: ProfileSidebarProps) => {
  const auth = useAuth();  // Use OIDC auth
  const navigate = useNavigate();
  const { selectedRole } = useUserRole();
  const dispatch = useDispatch();
  const handleLogout = async () => {
    await auth.removeUser();
    dispatch(clearUserData()); // Clears user data from Redux
    window.location.href = window.location.origin + "/logout";
  };
  
  const user = auth.isAuthenticated ? {
    role: (selectedRole) || UserRole.STUDENT
  } : null;
const isTeacher =selectedRole === UserRole.TEACHER ;

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="p-3">
        <nav>
          <ul className="space-y-0.5">
            <li>
              <Button variant="ghost" className={`w-full justify-start relative z-10 ${activePage === "profile" ? "bg-purple-100 text-purple-700 hover:bg-purple-200 hover:text-purple-800" : "text-gray-700 hover:bg-gray-100"}`} asChild>
                <Link to="/profile">
                  <User className="w-5 h-5 mr-2" />
                  My Profile
                </Link>
              </Button>
            </li>
            <li>
              <Button variant="ghost" className={`w-full justify-start relative z-10 ${activePage === "about" ? "bg-purple-100 text-purple-700 hover:bg-purple-200 hover:text-purple-800" : "text-gray-700 hover:bg-gray-100"}`} asChild>
                <Link to="/about">
                  <Info className="w-5 h-5 mr-2" />
                  About Me
                </Link>
              </Button>
            </li>
            {isTeacher && (
              <>
              {/*  <li>
                  <Button variant="ghost" className={`w-full justify-start relative z-10 ${activePage === "experience" ? "bg-purple-100 text-purple-700 hover:bg-purple-200 hover:text-purple-800" : "text-gray-700 hover:bg-gray-100"}`} asChild>
                    <Link to="/experience">
                      <Award className="w-5 h-5 mr-2" />
                      Experience
                    </Link>
                  </Button>
                </li> */}
                <li>
                  <Button variant="ghost" className={`w-full justify-start relative z-10 ${activePage === "reviews" ? "bg-purple-100 text-purple-700 hover:bg-purple-200 hover:text-purple-800" : "text-gray-700 hover:bg-gray-100"}`} asChild>
                    <Link to="/reviews">
                      <Star className="w-5 h-5 mr-2" />
                      Reviews
                    </Link>
                  </Button>
                </li>
              </>
            )}
            <li>
              <Button variant="ghost" className={`w-full justify-start relative z-10 ${activePage === "settings" ? "bg-purple-100 text-purple-700 hover:bg-purple-200 hover:text-purple-800" : "text-gray-700 hover:bg-gray-100"}`} asChild>
                <Link to="/settingsPage">
                  <Info className="w-5 h-5 mr-2" />
                  Settings
                </Link>
              </Button>
            </li>
            <li>
              <Button variant="ghost" className="w-full justify-start text-red-600 hover:bg-red-50 hover:text-red-700 relative z-10" onClick={handleLogout}>
                <LogOut className="w-5 h-5 mr-2" />
                Logout
              </Button>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default ProfileSidebar;
