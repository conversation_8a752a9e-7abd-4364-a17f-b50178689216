
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Join<PERSON><PERSON>Form from '@/components/JoinClassForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function JoinClassPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="p-4 border-b bg-white">
        <div className="flex items-center gap-2">
          <Link to="/dashboard" className="flex items-center">
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </Link>
          <h1 className="text-lg font-medium">Join a Class</h1>
        </div>
      </div>

      <div className="container mx-auto max-w-2xl py-10 px-4">
        <Card>
          <CardHeader>
            <CardTitle>Join Class</CardTitle>
            <CardDescription>
              Enter the class join code provided by your teacher
            </CardDescription>
          </CardHeader>
          <CardContent>
            <JoinClassForm />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
