
import React from 'react';
import { Check } from 'lucide-react';

interface StepIndicatorProps {
  currentStep: number;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep }) => {
  const steps = [
    { number: 1, title: 'Class Info', completed: currentStep > 1 },
    { number: 2, title: 'Class Schedule', completed: currentStep > 2 },
    { number: 3, title: 'Fees', completed: false },
  ];

  return (
    <div className="flex items-center justify-center space-x-8">
      {steps.map((step, index) => (
        <div key={step.number} className="flex items-center">
          <div className="flex flex-col items-center">
            <div
              className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200 ${
                step.number === currentStep
                  ? 'bg-purple-600 border-purple-600 text-white'
                  : step.completed
                  ? 'bg-purple-600 border-purple-600 text-white'
                  : 'bg-white border-gray-300 text-gray-400'
              }`}
            >
              {step.completed ? (
                <Check className="h-5 w-5" />
              ) : (
                <span className="text-sm font-medium">{step.number}</span>
              )}
            </div>
            <span
              className={`mt-2 text-sm font-medium ${
                step.number === currentStep
                  ? 'text-purple-600'
                  : step.completed
                  ? 'text-gray-900'
                  : 'text-gray-400'
              }`}
            >
              {step.title}
            </span>
          </div>
          {index < steps.length - 1 && (
            <div
              className={`w-16 h-0.5 mx-4 mt-[-20px] ${
                step.completed ? 'bg-purple-600' : 'bg-gray-300'
              }`}
            />
          )}
        </div>
      ))}
    </div>
  );
};

export default StepIndicator;