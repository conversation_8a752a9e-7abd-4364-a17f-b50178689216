import React, { useState ,useEffect, useRef, useCallback} from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Users, MessageSquare, Mail, Plus, Trash2,MoreHorizontal } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useAuth } from "react-oidc-context"; // Updated import
import { Student, getEnrolledStudentsForClass, getStudents ,unEnrollStudentfrmClassroom,reEnrollStudentToClassroom} from "@/services/studentService";
import { toast } from "sonner";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useIsMobile } from "@/hooks/use-mobile";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useNavigate } from "react-router-dom";
import { Dialog, DialogContent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { isValid, parseISO, differenceInYears } from "date-fns";

const EnrollmentTab = ({ classId }: any) => {
    const [students, setStudents] = useState<Student[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    
    const auth = useAuth();
     const [searchQuery, setSearchQuery] = useState("");
     const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
    const hasFetchedStudents = useRef(false);
  const isMobile = useIsMobile();
    const [isStudentModalOpen, setIsStudentModalOpen] = useState(false);
    const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
       const handleNotifyStudent = (studentId: string) => {
    console.log("Notifying student:", studentId);
    toast.success("Notification sent to student");
  };

  const handleChatWithStudent = (studentId: string) => {
    console.log("Opening chat with student:", studentId);
    // Implement chat functionality
  };

 
    const fetchStudents = async () => {
              setIsLoading(true);
              try {
                const data = await getEnrolledStudentsForClass(auth.user.access_token,classId);
                setStudents(data);
              } catch (error) {
                console.error("Error fetching students:", error);
              } finally {
                setIsLoading(false);
              }
            };
       
    useEffect(() => {
        if (auth.user?.access_token && classId && !hasFetchedStudents.current) {
        hasFetchedStudents.current = true;
        fetchStudents();
        }
    }, [auth.user?.access_token, classId]);
  
    useEffect(() => {
        if (students.length > 0) {
        const filtered = students.filter(student => 
            student.userName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            student.phoneNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            student.classroomName?.toLowerCase().includes(searchQuery.toLowerCase())
        );
        setFilteredStudents(filtered);
        } else {
        setFilteredStudents([]);
        }
    }, [searchQuery, students]);
      const handleRemoveStudent = async(studentId: string) => {
    try {
      await unEnrollStudentfrmClassroom(auth.user.access_token, classId, studentId, "TEACHER");
      toast.success("Student removed from class");
      // Refresh the student grid after successful removal
      fetchStudents();
    } catch (error) {
      toast.error("Failed to remove from class");
    }
  };

const handleEnrollStudent = async(studentId: string) => {
  try {
    await reEnrollStudentToClassroom(auth.user.access_token, classId, studentId, "TEACHER");
    toast.success("Student enrolled to class");
    fetchStudents(); // Refresh the student list
  } catch (error) {
    toast.error("Failed to enroll student");
  }
};
  return (
    <div className="max-w-6xl mx-auto ">
        <div className="mb-4 md:mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
            <div className="w-full sm:w-64">
                <Input
                placeholder="Search students..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="edu-form-field w-full"
                />
            </div>
        </div>
        <Card>
        <CardContent className="p-4 md:p-6 overflow-x-auto">
            {filteredStudents.length > 0 ? <div className="min-w-full">
                <Table>
                <TableHeader>
                    <TableRow>
                    <TableHead>Student Name</TableHead>
                    <TableHead className={isMobile ? "hidden" : ""}>Class Room Name</TableHead>
                    <TableHead className={isMobile ? "hidden" : ""}>Enrollment Status</TableHead>
                    <TableHead className={isMobile ? "hidden" : ""}>Phone Number</TableHead>
                    <TableHead>Actions</TableHead>
                    <TableHead>View Details</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {filteredStudents.map(student => <TableRow key={student.id}>
                        <TableCell className="font-medium">{student.userName}</TableCell>
                        <TableCell className={isMobile ? "hidden" : ""}>{student.classroomName}</TableCell>
                        <TableCell className={isMobile ? "hidden" : ""}>{student.status}</TableCell>
                        
                        <TableCell className={isMobile ? "hidden" : ""}>{student.phoneNumber}</TableCell>
                        <TableCell>
                        <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                        {student.status === "ACTIVE" ? (
                            <DropdownMenuItem onClick={() => handleRemoveStudent(student.id)}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            <span>Un Enroll</span>
                            </DropdownMenuItem>
                        ) : (
                            <DropdownMenuItem onClick={() => handleEnrollStudent(student.id)}>
                            <Plus className="h-4 w-4 mr-2" />
                            <span>Enroll</span>
                            </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleNotifyStudent(student.id)}>
                            <Mail className="h-4 w-4 mr-2" />
                            <span>Notify</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleChatWithStudent(student.id)}>
                            <MessageSquare className="h-4 w-4 mr-2" />
                            <span>Chat</span>
                        </DropdownMenuItem>
                        </DropdownMenuContent>

                        </DropdownMenu>
                        </TableCell>
                        <TableCell>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                            setSelectedStudent(student);
                            setIsStudentModalOpen(true);
                            }}
                        >
                            View Details
                        </Button>
                        </TableCell>
                    </TableRow>)}
                </TableBody>
                </Table>
            </div> : <div className="p-8 md:p-12 flex flex-col items-center justify-center">
                {students.length > 0 && searchQuery ? (
                <>
                    <Users className="w-16 h-16 md:w-24 md:h-24 object-contain mb-4" />
                    <p className="text-base md:text-lg font-medium mb-1">No matching students found</p>
                    <p className="text-xs md:text-sm text-gray-500 mb-4">Try a different search term</p>
                </>
                ) : (
                <>
                    <Users className="w-16 h-16 md:w-24 md:h-24 object-contain mb-4" />
                    <p className="text-base md:text-lg font-medium mb-1">No data found here!!</p>
                    <p className="text-xs md:text-sm text-gray-500 mb-4">Please add the student information to list below</p>
                </>
                )}
            </div>}
        </CardContent>
        </Card>
     <Dialog open={isStudentModalOpen} onOpenChange={setIsStudentModalOpen}>
                       <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
           <DialogHeader>
             <DialogTitle>Student Details</DialogTitle>
           </DialogHeader>
           {selectedStudent ? (
             <div className="space-y-2">
               <div><strong>Name:</strong> {selectedStudent.studentName}</div>
               <div><strong>ID:</strong> {selectedStudent.id}</div>
               <div><strong>ClassRoomName:</strong> {selectedStudent.classroomName}</div>
               <div><strong>Class:</strong> {selectedStudent.classes}</div>
               <div><strong>Grade:</strong> {selectedStudent.progress}</div>
              <div>
               <strong>Age:</strong>{" "}
               {selectedStudent.dateOfBirth && isValid(parseISO(selectedStudent.dateOfBirth))
                 ? differenceInYears(new Date(), parseISO(selectedStudent.dateOfBirth))
                 : "N/A"}
             </div>
               <div><strong>ParentName:</strong> {selectedStudent.parentName}</div>
               <div><strong>Country:</strong> {selectedStudent.country}</div>
               <div><strong>ParentPhone:</strong> {selectedStudent.parentPhone}</div>
               {/* Add more fields as needed */}
             </div>
           ) : (
             <div>No student selected.</div>
           )}
         </DialogContent>
       </Dialog>
     </div>
     
  );
};
export default EnrollmentTab;