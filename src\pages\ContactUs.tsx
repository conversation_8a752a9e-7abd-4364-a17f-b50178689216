import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Mail, PhoneCall, MapPin,Sparkles } from "lucide-react";
import { toast } from "sonner";
import Header from "@/components/layout/Header";
import Footer from "@/components/Footer";
import { sendContactMessage } from "@/services/contactService";
import { Link } from "react-router-dom";
import { useAuth } from "react-oidc-context";

export default function ContactUs() {
    const [contactForm, setContactForm] = useState({ name: "", email: "", message: "" });
  const [isSending, setIsSending] = useState(false);
const handleContactChange = (e) => {
    setContactForm({ ...contactForm, [e.target.id]: e.target.value });
  };
const auth = useAuth();
    const [isSubmitting, setIsSubmitting] = useState(false);

const handleSignUp  = async () => {
   // window.location.href = "https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/signup?client_id=76u1v7el416ebllhpbhtqpmlh0&code_challenge=Cjh7j5XvSKwPZ5ahIhP5j2tuEvZiuoSm811Q62N0wFs&code_challenge_method=S256&redirect_uri=http%3A%2F%2Flocalhost%3A8081%2Flogin%2Foauth2%2Fcode%2Fcognito&response_type=code&scope=email+openid+phone&state=80e73e7091c04c30a0c4904373b2096f";
     setIsSubmitting(true);
    
    try {
      // Store form values in localStorage to access after redirect back from Cognito
    //  localStorage.setItem("registerFormData", JSON.stringify(form.getValues()));
      
      // Redirect to Cognito signup page
      await auth.signinRedirect({ prompt: "login" });
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };
  const handleContactSubmit = async (e) => {
    e.preventDefault();
    
    if (!contactForm.name || !contactForm.email || !contactForm.message) {
      toast.error("Please fill in all fields");
      return;
    }
    
    setIsSending(true);
    try {
      await sendContactMessage(contactForm);
      toast.success("Message sent successfully!");
      setContactForm({ name: "", email: "", message: "" });
    } catch (error) {
      toast.error("Failed to send message.");
    } finally {
      setIsSending(false);
    }
  };

    return (
        <div className="min-h-screen flex flex-col">
              <Header />
 <main className="flex-grow">
         {/* Hero Section with gradient background */}
        <section className="py-16 md:py-24 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-blue-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto relative z-10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
              <div className="max-w-2xl text-center md:text-left">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white leading-tight">
                  Transform <span className="text-yellow-300">Education</span> Through Innovation
                </h1>
                <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
                  Join thousands of educators and students in revolutionizing the learning experience
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <Button asChild size="lg" className="w-full sm:w-auto bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full shadow-lg">
                    <Link to="/register">Get Started Free</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="w-full sm:w-auto border-2 bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full">
                    <Link to="/subscription">View Pricing</Link>
                  </Button>
                </div>
                <div className="mt-12 flex flex-col sm:flex-row items-center justify-center md:justify-start gap-6">
                  <div className="flex -space-x-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="w-12 h-12 rounded-full border-4 border-purple-600 bg-white shadow-lg flex items-center justify-center text-purple-600 font-bold text-lg">
                        {String.fromCharCode(64 + i)}
                      </div>
                    ))}
                  </div>
                  <p className="text-lg text-white/90">
                    Joined by <span className="font-bold text-yellow-300">2000+</span> educators
                  </p>
                </div>
              </div>
              <div className="w-full md:w-2/5 relative">
                <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-transform duration-500">
                  <img 
                    src="https://images.unsplash.com/photo-1571260899304-425eee4c7efc?q=80&w=2070&auto=format&fit=crop" 
                    alt="EduConnect Platform" 
                    className="w-full h-64 md:h-96 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800">Modern Learning Experience</h3>
                    <p className="text-gray-600">Interactive tools for better engagement</p>
                  </div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-xl shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <p className="text-lg font-bold flex items-center gap-2">
                    <Sparkles className="h-5 w-5" />
                    AI-Powered Learning
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
    <section className="py-12 md:py-20 bg-white">
    <div className="container mx-auto px-4">
    <div className="text-center mb-8 md:mb-12">
        <h2 className="text-2xl md:text-4xl font-bold mb-3 md:mb-4">Get in Touch</h2>
        <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto">
        Have questions? We're here to help. Send us a message and we'll respond as soon as possible.
        </p>
    </div>

    <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
        <div className="space-y-6">
        <div className="flex items-center gap-4">
            <div className="bg-purple-600 p-3 rounded-full">
            <Mail className="h-6 w-6 text-white" />
            </div>
            <div>
            <h3 className="font-semibold text-lg">Email Us</h3>
            <p className="text-gray-600"><EMAIL></p>
            </div>
        </div>

        <div className="flex items-center gap-4">
            <div className="bg-purple-600 p-3 rounded-full">
            <PhoneCall className="h-6 w-6 text-white" />
            </div>
            <div>
            <h3 className="font-semibold text-lg">Call Us</h3>
            <p className="text-gray-600">+****************</p>
            </div>
        </div>

        <div className="flex items-center gap-4">
            <div className="bg-purple-600 p-3 rounded-full">
            <MapPin className="h-6 w-6 text-white" />
            </div>
            <div>
            <h3 className="font-semibold text-lg">Visit Us</h3>
            <p className="text-gray-600">123 Education Street<br />Learning City, ED 12345</p>
            </div>
        </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-4 md:p-6">
        <form className="space-y-4" onSubmit={handleContactSubmit}>
    <div>
      <Label htmlFor="name">Name</Label>
      <Input id="name"  className="edu-form-field"  value={contactForm.name} onChange={handleContactChange} placeholder="Your name" />
    </div>
    <div>
      <Label htmlFor="email">Email</Label>
      <Input id="email" className="edu-form-field" type="email" value={contactForm.email} onChange={handleContactChange} placeholder="<EMAIL>" />
    </div>
    <div>
      <Label htmlFor="message">Message</Label>
      <Textarea
        id="message"
        value={contactForm.message}
        onChange={handleContactChange}
        placeholder="How can we help you?"
        className="h-32"
      />
    </div>
    <Button className="w-full bg-purple-600 hover:bg-purple-700" type="submit" disabled={isSending}>
      {isSending ? "Sending..." : "Send Message"}
    </Button>
  </form>
              </div>
            </div>
          </div>
        </section>
        </main>
        
                   <footer className="bg-gray-900 text-white py-8 md:py-12">
                <Footer/>
              </footer>
              
        </div>
  )};
