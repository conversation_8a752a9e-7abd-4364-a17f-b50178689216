
/* eslint-disable */
// WARNING: DO NOT EDIT. This file is automatically generated by AWS Amplify.
// This file can be replaced during amplify push or manually modified.

const awsmobile = {
    "aws_project_region": "us-east-1",
    "aws_cognito_identity_pool_id": "us-east-1_CfPZWbR4P", // Replace with your Cognito Identity Pool ID
    "aws_cognito_region": "us-east-1", // Replace with your Cognito region
    "aws_user_pools_id": "us-east-1_CfPZWbR4P", // Replace with your User Pool ID
    "aws_user_pools_web_client_id": "76u1v7el416ebllhpbhtqpmlh0", // Replace with your App Client ID
    "oauth": {
        "domain": "us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com",
        "scope": ["email", "openid", "phone"],
        "redirectSignIn": window.location.origin + "/login/oauth2/code/cognito",
        "redirectSignOut": window.location.origin + "/logout",
        "responseType": "code"
    },
    "aws_cognito_username_attributes": [
        "EMAIL"
    ],
    "aws_cognito_social_providers": [],
    "aws_cognito_signup_attributes": [
        "EMAIL",
        "NAME"
    ],
    "aws_cognito_mfa_configuration": "OFF",
    "aws_cognito_mfa_types": [],
    "aws_cognito_password_protection_settings": {
        "passwordPolicyMinLength": 8,
        "passwordPolicyCharacters": []
    },
    "aws_cognito_verification_mechanisms": [
        "EMAIL"
    ]
};

export default awsmobile;
