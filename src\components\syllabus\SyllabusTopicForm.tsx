import { fileUpload } from "@/utils/fileUpload";

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RichTextEditor } from "@/components/ui/rich-text-editor";

import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, File, X } from "lucide-react"; 
import { useUserRole } from "@/hooks/useUserRole";

import { UserRole } from "@/types";
import { useAuth } from "react-oidc-context"; // Updated import
import { generateAvatarUrl } from "@/lib/utils";
import { toast } from "sonner";
const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
});
interface SyllabusTopicFormProps {
  onSubmit: (data: { title: string; description: string; attachedFiles: AttachedFile[] }) => void;
  onCancel: () => void;
}
interface AttachedFile {
 
  id: string;
  originalFilename: string;
  fileSize: number;
  downloadUrl: string;
}
export const SyllabusTopicForm: React.FC<SyllabusTopicFormProps> = ({
  onSubmit,
  onCancel,
}) => {
    const [attachedFiles, setAttachedFiles] = useState<AttachedFile[]>([]);
  const [uploading, setUploading] = useState(false);

  const auth = useAuth(); // Use OIDC auth
  const { selectedRole } = useUserRole();
  // Get user from OIDC
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
    },
  });
 const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
  const files = Array.from(event.target.files || []);
  console.log(auth.user.access_token);
  if (files.length === 0) return;
  console.log(auth.user?.access_token);
  if (!auth.user.access_token) {
    
    toast.error("Authentication required");
    return;
  }
  
  setUploading(true);
  try {
    const metadata = {
      description: "Syllabus topic attachment",
      tags: "syllabus,topic,attachment",
      relatedEntityType: "SYLLABUS_TOPIC",
      fileCategory: "DOCUMENT"
    };
    
    const uploadedFiles = await fileUpload(auth.user.access_token, files, metadata);
     
    setAttachedFiles(prev => [...prev, ...uploadedFiles]);
    toast.success("Files uploaded successfully");
  } catch (error) {
    toast.error("Failed to upload files");
  } finally {
    setUploading(false);
  }
};
 const removeFile = (fileId: string) => {
    setAttachedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  return (
    <Form {...form}>
<form
  onSubmit={form.handleSubmit((data) => onSubmit({ title: data.title, description: data.description, attachedFiles }))}
  className="space-y-4"
>
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Topic Title</FormLabel>
              <FormControl>
                <Input placeholder="Enter topic title" {...field}  className='edu-form-field'
              />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <RichTextEditor 
                  value={field.value}
                  onChange={field.onChange}
                  placeholder="Enter topic description"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* File Upload Section */}
          <div className="space-y-3">
            <FormLabel className="text-sm font-medium">Attachments</FormLabel>
            
            <Card className="border-dashed border-2 hover:border-primary/50 transition-colors">
              <CardContent className="p-6">
                <label htmlFor="file-upload" className="cursor-pointer">
                  <div className="flex flex-col items-center justify-center text-center">
                    <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm font-medium text-muted-foreground">
                      Click to upload files
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Support for multiple file types
                    </p>
                  </div>
                   <input
                  id="file-upload"
                  type="file"
                  multiple
                  className="hidden"
                  onChange={handleFileUpload}
                  accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.mp4,.mp3"
                  disabled={uploading}
                />
                </label>
              </CardContent>
            </Card>

            {/* Attached Files */}
            {attachedFiles.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">
                  Attached Files ({attachedFiles.length})
                </p>
                <div className="space-y-2">
                  {attachedFiles.map((file) => (
                    <Card key={file.id} className="bg-muted/50">
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <File className="h-4 w-4 text-primary" />
                            <div>
                              <p className="text-sm font-medium text-foreground truncate max-w-[200px]">
                                {file.originalFilename}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {formatFileSize(file.fileSize)}
                              </p>
                            </div>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(file.id)}
                            className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </div>


        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit"   size="sm"
          className="bg-purple-600 hover:bg-purple-700 flex items-center gap-2">Add</Button>
        </div>
      </form>
    </Form>
  );
};
