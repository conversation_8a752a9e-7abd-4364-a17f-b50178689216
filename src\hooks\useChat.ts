import { useState, useEffect, useCallback } from 'react';
import { ChatMessage } from '@/types';
import { chatService, ChatContact } from '@/services/chatService';
import { useApp } from '@/context/AppContext';

export const useChat = (userId: string | null) => {
  const { sendMessage: contextSendMessage } = useApp();
  const [contacts, setContacts] = useState<ChatContact[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedContactId, setSelectedContactId] = useState<string | null>(null);

  // Load contacts
  const loadContacts = useCallback(async () => {
    if (!userId) return;
    
    setLoading(true);
    try {
      const contactsData = await chatService.getContacts(userId);
      setContacts(contactsData);
    } catch (error) {
      console.error('Failed to load contacts:', error);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Load messages for selected contact
  const loadMessages = useCallback(async (contactId: string) => {
    if (!userId) return;
    
    setLoading(true);
    try {
      const messagesData = await chatService.getMessages(userId, contactId);
      setMessages(messagesData);
    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Send message
  const sendMessage = useCallback(async (content: string, receiverId: string) => {
    if (!userId || !content.trim()) return;

    const messageData = {
      senderId: userId,
      receiverId,
      content: content.trim()
    };

    // Use context method for local state
    contextSendMessage(messageData);
    
    try {
      // Also send to API (when available)
      await chatService.sendMessage(messageData);
    } catch (error) {
      console.error('API send failed, using local only:', error);
    }
  }, [userId, contextSendMessage, selectedContactId, loadMessages]);

  // Mark messages as read
  const markAsRead = useCallback(async (messageId: string) => {
    try {
      await chatService.markAsRead(messageId);
    } catch (error) {
      console.error('Failed to mark message as read:', error);
    }
  }, []);

  // Load contacts on mount
  useEffect(() => {
    loadContacts();
  }, [loadContacts]);

  // Load messages when contact is selected
  useEffect(() => {
    if (selectedContactId) {
      loadMessages(selectedContactId);
    }
  }, [selectedContactId, loadMessages]);

  return {
    contacts,
    messages,
    loading,
    selectedContactId,
    setSelectedContactId,
    sendMessage,
    markAsRead,
    loadContacts,
    loadMessages
  };
};