import { fileUpload } from "@/utils/fileUpload"; // <-- Import your upload service
import { useAuth } from "react-oidc-context"; // Updated import
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, BookOpen, Calendar, Users,Upload, Image as ImageIcon, MessageSquare, DollarSign, FileText, User, Bell, Home, Share2, Copy, Mail, Smartphone, Edit, Check, X, Plus, Menu, MoreHorizontal, Trash2 } from "lucide-react";

import React, { useState ,useEffect} from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { ClassType } from "@/types";
import { Button } from "@/components/ui/button";
import { Image} from "lucide-react";
import ReactCrop, { Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import {
  Form} from "@/components/ui/form";
interface StepOneFormProps {
  formData: {
    className: string;
    subjectName: string;
    classType: ClassType;
    image: string;
    level :number;
    batchName:string;
    description:string;
    capacity:number;
    demoVideoURL:string;  
    posterFileId?: string;
  };
  updateFormData: (data: Partial<StepOneFormProps["formData"]>) => void;
  onValidationChange?: (isValid: boolean, validate: () => boolean) => void;
  validationTriggered?: boolean;
}

export default function StepOneForm({ formData, updateFormData, onValidationChange, validationTriggered }: StepOneFormProps) {
  const [previewImage, setPreviewImage] = useState<string | null>(formData.image || null);
  const auth = useAuth(); // Use OIDC auth
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [crop, setCrop] = useState<Crop>({ unit: '%', width: 90, height: 60, x: 5, y: 20 });
  const [showImageOptions, setShowImageOptions] = useState(false);
  const [isCropping, setIsCropping] = useState(false);
  const imgRef = React.useRef<HTMLImageElement>(null);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Trigger validation when validationTriggered prop changes
  useEffect(() => {
    if (validationTriggered) {
      validateAllFields();
    }
  }, [validationTriggered]);

  const validateField = (field: string, value: any) => {
    const newErrors = { ...errors };
    
    switch (field) {
      case 'className':
        if (!value?.trim()) {
          newErrors.className = 'Class name is required';
        } else {
          delete newErrors.className;
        }
        break;
      case 'subjectName':
        if (!value?.trim()) {
          newErrors.subjectName = 'Course title is required';
        } else {
          delete newErrors.subjectName;
        }
        break;
      case 'classType':
        if (!value) {
          newErrors.classType = 'Class type is required';
        } else {
          delete newErrors.classType;
        }
        break;
      case 'batchName':
        if (!value?.trim()) {
          newErrors.batchName = 'Batch name is required';
        } else {
          delete newErrors.batchName;
        }
        break;
      case 'capacity':
        if (!value || value <= 0) {
          newErrors.capacity = 'Capacity must be greater than 0';
        } else {
          delete newErrors.capacity;
        }
        break;
      case 'level':
        if (!value || value <= 0) {
          newErrors.level = 'Level must be greater than 0';
        } else {
          delete newErrors.level;
        }
        break;
      case 'demoVideoURL':
        if (!value?.trim()) {
          newErrors.demoVideoURL = 'Demo video URL is required';
        } else {
          delete newErrors.demoVideoURL;
        }
        break;
      case 'description':
        if (!value?.trim()) {
          newErrors.description = 'Description is required';
        } else {
          delete newErrors.description;
        }
        break;
    }
    
    setErrors(newErrors);
    
    // Notify parent of validation state
    if (onValidationChange) {
      const isValid = Object.keys(newErrors).length === 0;
      onValidationChange(isValid, () => validateAllFields());
    }
  };
  
  const validateAllFields = () => {
    const allErrors: {[key: string]: string} = {};
    
    if (!formData.className?.trim()) allErrors.className = 'Class name is required';
    if (!formData.subjectName?.trim()) allErrors.subjectName = 'Course title is required';
    if (!formData.classType) allErrors.classType = 'Class type is required';
    if (!formData.batchName?.trim()) allErrors.batchName = 'Batch name is required';
    if (!formData.capacity || formData.capacity <= 0) allErrors.capacity = 'Capacity must be greater than 0';
    if (!formData.level || formData.level <= 0) allErrors.level = 'Level must be greater than 0';
    if (!formData.demoVideoURL?.trim()) allErrors.demoVideoURL = 'Demo video URL is required';
    if (!formData.description?.trim()) allErrors.description = 'Description is required';
    
    setErrors(allErrors);
    return Object.keys(allErrors).length === 0;
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setSelectedFile(file);
    const reader = new FileReader();
    reader.onload = () => {
      setSelectedImage(reader.result as string);
      setShowImageOptions(true);
    };
    reader.readAsDataURL(file);
  };
  const getCroppedImg = (image: HTMLImageElement, crop: Crop): Promise<Blob> => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('No 2d context');

    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;
    canvas.width = crop.width;
    canvas.height = crop.height;

    ctx.drawImage(
      image,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      crop.width,
      crop.height
    );

    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        if (blob) resolve(blob);
      }, 'image/jpeg', 0.9);
    });
  };

  const handleSaveOriginal = async () => {
    if (!selectedFile) return;

    try {
      const previewUrl = URL.createObjectURL(selectedFile);
      setPreviewImage(previewUrl);
      
      const uploaded = await fileUpload(auth.user.access_token, [selectedFile]);
      if (uploaded[0]?.id) {
        updateFormData({ 
          image: uploaded[0].downloadUrl || uploaded[0].url,
          posterFileId: uploaded[0].id 
        });
      }
      
      setShowImageOptions(false);
      setSelectedImage(null);
      setSelectedFile(null);
    } catch (err) {
      console.error('Upload failed:', err);
    }
  };

  const handleCropImage = () => {
    setShowImageOptions(false);
    setIsCropping(true);
  };

  const handleCropComplete = async () => {
    if (!imgRef.current || !crop.width || !crop.height) return;

    try {
      const croppedBlob = await getCroppedImg(imgRef.current, crop);
      const croppedFile = new File([croppedBlob], 'cropped-image.jpg', { type: 'image/jpeg' });
      
      const previewUrl = URL.createObjectURL(croppedBlob);
      setPreviewImage(previewUrl);
      
      const uploaded = await fileUpload(auth.user.access_token, [croppedFile]);
      if (uploaded[0]?.id) {
        updateFormData({ 
          image: uploaded[0].downloadUrl || uploaded[0].url,
          posterFileId: uploaded[0].id 
        });
      }
      
      setIsCropping(false);
      setSelectedImage(null);
      setSelectedFile(null);
    } catch (err) {
      console.error('Crop failed:', err);
    }
  };

const removeImage = () => {
    if (previewImage) {
      URL.revokeObjectURL(previewImage); // Clean up blob URL
    }
    setPreviewImage(null);
    updateFormData({ image: "" });
  };


  
  return (
  //  <Form noValidate validated={validated} onSubmit={handleSubmit}>
      <div className="space-y-6 bg-gray-50 p-6 rounded-lg">
     
         <div className="bg-gray-50 -m-4 md:-m-6 p-4 md:p-6 min-h-[400px]">
              <div className="space-y-6">
                  {/* Poster Image Section */}
                <Card className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="relative group">
                        {previewImage ? (
                          <div className="relative h-48 md:h-64">
                            <img 
                              src={previewImage} 
                              alt="Class poster preview" 
                              className="w-full h-full object-cover rounded-lg"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                                <Button
                                  onClick={() => fileInputRef.current?.click()}
                                  variant="secondary"
                                  size="sm"
                                >
                                  <Upload className="h-4 w-4 mr-2" />
                                  Change
                                </Button>
                                <Button
                                  onClick={removeImage}
                                  variant="destructive"
                                  size="sm"
                                >
                                  Remove
                                </Button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="h-48 md:h-64 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center border-2 border-dashed border-blue-200 group-hover:border-blue-300 transition-colors">
                            <div className="text-center">
                              <div className="mx-auto h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                <ImageIcon className="h-6 w-6 text-blue-600" />
                              </div>
                              <h3 className="text-lg font-medium text-gray-900 mb-2">Add Class Poster</h3>
                              <p className="text-sm text-gray-500 mb-4">Upload an image to make your class more attractive</p>
                              <Button
                                onClick={() => fileInputRef.current?.click()}
                                variant="outline"
                                className="border-blue-200 text-blue-600 hover:bg-blue-50"
                              >
                                <Upload className="h-4 w-4 mr-2" />
                                Upload Image
                              </Button>
                            </div>
                          </div>
                        )}
                    
                      <input
                        ref={fileInputRef}
                        type="file"
                        
                        accept="image/*"
                        onChange={handleImageChange}
                        className="hidden"
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Image Options Dialog */}
                <Dialog open={showImageOptions} onOpenChange={setShowImageOptions}>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>Choose Image Option</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      {selectedImage && (
                        <img
                          src={selectedImage}
                          alt="Selected image"
                          className="w-full h-48 object-cover rounded-lg"
                        />
                      )}
                      <div className="flex flex-col gap-2">
                        <Button onClick={handleSaveOriginal} className="w-full">
                          Save Original Image
                        </Button>
                        <Button onClick={handleCropImage} variant="outline" className="w-full">
                          Crop Image
                        </Button>
                        <Button
                          variant="ghost"
                          onClick={() => {
                            setShowImageOptions(false);
                            setSelectedImage(null);
                            setSelectedFile(null);
                          }}
                          className="w-full"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                {/* Crop Dialog */}
                <Dialog open={isCropping} onOpenChange={setIsCropping}>
                  <DialogContent className="max-w-md max-h-[80vh] overflow-auto">
                    <DialogHeader>
                      <DialogTitle>Crop Image</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      {selectedImage && (
                        <ReactCrop
                          crop={crop}
                          onChange={(c) => setCrop(c)}
                          aspect={16/9}
                        >
                          <img
                            ref={imgRef}
                            src={selectedImage}
                            alt="Crop preview"
                            className="max-w-full h-auto max-h-60"
                          />
                        </ReactCrop>
                      )}
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setIsCropping(false);
                            setSelectedImage(null);
                            setSelectedFile(null);
                          }}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleCropComplete}>
                          Apply Crop
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
                 {/* Class Information Cards */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 ">
                  {/* Basic Information */}
                  <Card className="shadow-sm">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                        <BookOpen className="h-5 w-5 mr-2 text-primary" />
                        Basic Information
                      </h3>
                      <div className="space-y-4">
                        <div className="space-y-2">
                           <Label htmlFor="name" className="text-sm font-medium text-foreground">Class Name*</Label>
                            <Input
                              id="name"
                              placeholder="Enter Class Name"
                              value={formData.className}
                              onChange={(e) => {
                                updateFormData({ className: e.target.value });
                                validateField('className', e.target.value);
                              }}
                              required
                              className={`edu-form-field ${errors.className ? 'border-red-500 border-2' : ''}`}
                        />
                            {errors.className && <p className="text-red-500 text-xs mt-1">{errors.className}</p>}
                         </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Course Title*</label>
                           <Input
                            id="subjectName"
                            placeholder="Enter Subject Name"
                            value={formData.subjectName}
                            onChange={(e) => {
                              updateFormData({ subjectName: e.target.value });
                              validateField('subjectName', e.target.value);
                            }}
className={`edu-form-field ${errors.subjectName ? 'border-red-500 border-2' : ''}`}
                                                  />
                          {errors.subjectName && <p className="text-red-500 text-xs mt-1">{errors.subjectName}</p>}
                          
                                                  </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Class Type*</label>
                           <Select
                            value={formData.classType}
                            onValueChange={(value) => {
                              updateFormData({ classType: value as ClassType });
                              validateField('classType', value);
                            }}
                          >
                            <SelectTrigger className={`${errors.classType ? 'border-red-500 border-2' : 'edu-form-field'}`}>
                              <SelectValue placeholder="Select Class Type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value={ClassType.ONLINE}>Online</SelectItem>
                              <SelectItem value={ClassType.OFFLINE}>Offline</SelectItem>
                              <SelectItem value={ClassType.REGULAR}>Regular</SelectItem>
                              <SelectItem value={ClassType.ADVANCED}>Advanced</SelectItem>
                              <SelectItem value={ClassType.ELECTIVE}>Elective</SelectItem>
                            </SelectContent>
                          </Select>
                          {errors.classType && <p className="text-red-500 text-xs mt-1">{errors.classType}</p>}
                    </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Class Details */}
                  <Card className="shadow-sm">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                        <Users className="h-5 w-5 mr-2 text-primary" />
                        Class Details
                      </h3>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Batch Name*</label>
                         <Input 
                          id="batchName"
                          placeholder="Enter Batch Name"
                          value={formData.batchName}
                          onChange={(e) => {
                            updateFormData({ batchName: e.target.value });
                            validateField('batchName', e.target.value);
                          }}
                          
                          className={`edu-form-field ${errors.batchName ? 'border-red-500 border-2' : ''}`}
                        />
                        {errors.batchName && <p className="text-red-500 text-xs mt-1">{errors.batchName}</p>}            
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium text-foreground">Level*</label>
            
                          <Input 
                          id="level"
                          placeholder="Enter Grade"
                          type="number"
                          value={formData.level}
                        onChange={(e) => {
                          const value = e.target.value === "" ? 0 : Number(e.target.value);
                          updateFormData({ level: value });
                          validateField('level', value);
                        }}
                        className={`edu-form-field ${errors.level ? 'border-red-500 border-2' : ''}`}
                        />
                        {errors.level && <p className="text-red-500 text-xs mt-1">{errors.level}</p>}
                        </div>

                        <div className="space-y-2 ">
                          <label className="text-sm font-medium text-foreground">Capacity*</label>
                          <Input 
                          id="capacity"
                          type="number"
                          placeholder="Enter Capacity"
                        value={formData.capacity}
                          onChange={(e) => {
                            const value = e.target.value === "" ? 0 : Number(e.target.value);
                            updateFormData({ capacity: value });
                            validateField('capacity', value);
                          }}
                    
                          className={`edu-form-field ${errors.capacity ? 'border-red-500 border-2' : ''}`}
                        />
                        {errors.capacity && <p className="text-red-500 text-xs mt-1">{errors.capacity}</p>}
                        </div>
                      </div>
                      <div  className="space-y-2 ">
                      <Label htmlFor="demoVideoURL" className="text-sm font-medium text-foreground">Demo Video URL*</Label>
                        <Input 
                          id="demoVideoURL"
                          placeholder="Demo Video URL"
                        value={formData.demoVideoURL}
                          onChange={(e) => {
                            updateFormData({ demoVideoURL: e.target.value });
                            validateField('demoVideoURL', e.target.value);
                          }}
                    
                          className={`edu-form-field ${errors.demoVideoURL ? 'border-red-500 border-2' : ''}`}
                        />
                        {errors.demoVideoURL && <p className="text-red-500 text-xs mt-1">{errors.demoVideoURL}</p>}
                        </div>
                    </CardContent>
                  </Card>
                </div>
  
                {/* Description */}
                <Card className="shadow-sm">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                      <FileText className="h-5 w-5 mr-2 text-primary" />
                      Description*
                    </h3>
                    <div className="space-y-2">
                      <RichTextEditor
                        value={formData.description}
                        onChange={(value) => {
                          updateFormData({ description: value });
                          validateField('description', value);
                        }}
                        placeholder="Enter a detailed description of your class..."
                        className={`edu-form-field${errors.description ? 'border-red-500 border-2' : ''}`}
                      />
                      {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
                    </div>
                  </CardContent>
                </Card>
            
            </div>
        </div> 
      </div>
//    </Form>
  );
}
