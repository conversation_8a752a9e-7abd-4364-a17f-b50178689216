import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "react-oidc-context";
import { UserRole } from "@/types";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { toast } from "sonner";
import { useDispatch, useSelector } from "react-redux";
import { setUserData ,setSelectedRole ,setUserId,setTimezone } from "@/redux/userSlice";
import { useUserRole } from "@/hooks/useUserRole";
import { useUserProfile } from "@/context/UserProfileContext";
import { useIsMobile } from "@/hooks/use-mobile";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {Globe } from "lucide-react";
export default function RoleSelectionPage() {
  const navigate = useNavigate();
  const auth = useAuth();
  const { selectedRole, updateRole ,userTimezone, updateUserTimezone } = useUserRole();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch();
  const userData = useSelector((state: any) => state.user.userData);
  const { data: userProfile, isLoading } = useUserProfile();
  const [userName, setUserName] = useState("");
  const [email, setEmail] = useState("");
  const isMobile = useIsMobile();
      const [selectedTimezone, setSelectedTimezone] = useState("UTC");
  // Get user from OIDC
        const user = auth.isAuthenticated ? {
          id: auth.user?.profile.sub || "",
          name: auth.user?.profile.name || "User",
          email: auth.user?.profile.email || "",
          role: (auth.user?.profile["custom:role"] as UserRole) ,
        //  avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
        } : null;
  const [availableTimeZones, setAvailableTimeZones] = useState([
          "UTC",
          "America/New_York",
          "America/Chicago",
          "America/Denver",
          "America/Los_Angeles",
          "Europe/London",
          "Europe/Paris",
          "Asia/Kolkata",
          "Asia/Shanghai",
          "Asia/Tokyo",
          "Australia/Sydney",
          "Asia/Dubai",
          "America/Toronto",
          "Europe/Berlin",
          "Asia/Singapore"
        ]);
  
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = 'Are you sure you want to leave? Your role selection will be lost.';
      return 'Are you sure you want to leave? Your role selection will be lost.';
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);




  useEffect(() => {
    console.log(userProfile)
    if (userProfile) {
      setUserName(userProfile.name || "");
      setEmail(user.email || "");
      setSelectedTimezone(userProfile.timezone || "UTC");
      dispatch(setUserData(userProfile));
        dispatch(setTimezone(userProfile.timezone || "UTC"));
    } else {
      setEmail(user?.email || "");
    }
  }, [userProfile, user?.email, dispatch]);

const handleRoleSelection = (value: string) => {
updateRole(value);

};
  const handleSubmit = async () => {
    if (!selectedRole) {
      toast.error("Please select a role to continue");
      return;
    }
 
   
    auth.user.profile["custom:role"] = selectedRole;
    setIsSubmitting(true);

    try {
      // Use the userProfile from the global state
      if (!userProfile || !userProfile.id) {
        throw new Error("User profile not available");
      }
      console.log(selectedTimezone)
      // Remove quotes from the ID if they exist
      const userId = userProfile.id.replace(/\"/g, '');
      
      const response = await fetch(`/api/userManagement/v1/users/roles`, {
        method: "POST",
        headers: {
              "Authorization": `Bearer ${auth.user?.access_token}`,
              "Content-Type": "application/json"
        },
        body: JSON.stringify({
          name: userName,
          role: selectedRole,
          timezone: selectedTimezone,
          email : email
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const result = await response.json();
      dispatch(setUserId(userId));
      console.log(result.id);
      
      
      dispatch(setSelectedRole(selectedRole));
    updateUserTimezone(selectedTimezone)
console.log(userTimezone)

      // Navigate based on selected role
      switch (selectedRole) {
        case UserRole.TEACHER:
          navigate("/teacher-dashboard");
          break;
        case UserRole.STUDENT:
          navigate("/student-dashboard");
          break;
        case UserRole.PARENT:
          navigate("/parent-dashboard");
          break;
        default:
          navigate("/dashboard");
      }
      
      toast.success(`Welcome! You're now signed in as a ${selectedRole}`);
    } catch (error: any) {
      console.error("Role selection error:", error);
      toast.error(`Failed to set role: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
        

  };

  // If user is not authenticated, redirect to login
  if (!auth.isAuthenticated) {
     useEffect(() => {
    navigate("/login");
  }, [navigate]); 
    return null;
  }

  return (
    <div className="min-h-screen flex justify-center">
      <div className="w-full  bg-white rounded-xl shadow-xl overflow-hidden md:flex">
        {!isMobile && <div className="md:w-1/2 bg-gradient-to-br from-purple-600 to-indigo-700 p-6 text-white flex flex-col justify-center items-center text-center rounded-l-xl">
          <div className="mb-8">
            <img 
              src="/public/lovable-uploads/ed4ef11f-d243-449a-9b3a-7d747086199c.png" 
              alt="Education illustration" 
              className="w-full max-w-xs mx-auto"
            />
          </div>
          <h2 className="text-2xl font-bold mb-3">A good teacher makes hard ideas easy to understand without losing their meaning.</h2>
        </div>}
        
        <div className="md:w-1/2 p-6 bg-white flex flex-col justify-center">
          <div className="text-center mb-8">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
              Introduce Yourself
            </h1>
            <p className="text-gray-600">
              We'd love to learn more about you. Can you share a little about your background?
            </p>
          </div>

          
          <div className="space-y-6">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Your Name</label>
              <input
                type="text"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                className="edu-form-field w-full"
                placeholder="Enter your display name"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Email Id</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="edu-form-field w-full"
                placeholder="Enter your email address"
              />
            </div>
            
            {/* Timezone Section */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Timezone
              </label>
                <Select value={selectedTimezone} onValueChange={setSelectedTimezone}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select your timezone" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableTimeZones.sort().map((timezone) => (
                      <SelectItem key={timezone} value={timezone}>
                        {timezone}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              
            </div>
            
           <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Choose Your Role</label>
              <RadioGroup
                value={selectedRole || ""}
                onValueChange={handleRoleSelection}
                className="flex flex-col space-y-4"
              name = "sfsf"
            >    <div className={`border-2 rounded-lg p-4 flex items-center justify-between ${selectedRole === UserRole.STUDENT ? 'border-purple-500 bg-purple-50' : 'border-gray-200'}`}>
                <label htmlFor="student-option" className="flex-1 cursor-pointer flex items-center">
                  <div>
                    <div className="font-medium">I am Student</div>
                    <div className="text-sm text-gray-500">Who want to learn</div>
                  </div>
                </label>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 bg-gray-100 rounded-lg flex items-center justify-center">
                    👨‍🎓
                  </div>
                  <RadioGroupItem value={UserRole.STUDENT} id="student-option" />
                </div>
              </div>
              
              <div className={`border-2 rounded-lg p-4 flex items-center justify-between ${selectedRole === UserRole.TEACHER ? 'border-purple-500 bg-purple-50' : 'border-gray-200'}`}>
                <label htmlFor="teacher-option" className="flex-1 cursor-pointer flex items-center">
                  <div>
                    <div className="font-medium">I am Teacher</div>
                    <div className="text-sm text-gray-500">Who want to teach</div>
                  </div>
                </label>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 bg-gray-100 rounded-lg flex items-center justify-center">
                    👩‍🏫
                  </div>
                  <RadioGroupItem value={UserRole.TEACHER} id="teacher-option" />
                </div>
              </div>
              
            {/*}  <div className={`border-2 rounded-lg p-4 flex items-center justify-between ${selectedRole === UserRole.PARENT ? 'border-purple-500 bg-purple-50' : 'border-gray-200'}`}>
                <label htmlFor="parent-option" className="flex-1 cursor-pointer flex items-center">
                  <div>
                    <div className="font-medium">I am Parent</div>
                    <div className="text-sm text-gray-500">Who want to see performance of students</div>
                  </div>
                </label>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 bg-gray-100 rounded-lg flex items-center justify-center">
                    👪
                  </div>
                  <RadioGroupItem value={UserRole.PARENT} id="parent-option" />
                </div>
              </div>*/}
            </RadioGroup>
            </div>
          </div>
          <Button
            className="w-full mt-8 bg-purple-600 hover:bg-purple-700 text-white py-3 rounded-lg font-medium"
            disabled={!selectedRole || isSubmitting || isLoading}
            onClick={handleSubmit}
          >
            {isSubmitting ? "Processing..." : "Get Started"}
          </Button>
          
          <div className="text-center mt-4 text-sm text-gray-500">
            You're ready to rock 🚀
          </div>
        </div>
      </div>
    </div>
  );
}