import React, { useRef, useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Pen, Square, Circle, Type, Minus, Eraser, Download, Trash2, MousePointer, Pa<PERSON>, Settings } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import io from 'socket.io-client';

const SOCKET_SERVER_URL = "http://localhost:3001";

interface WhiteboardProps {
  classId?: string;
  isTeacher?: boolean;
}

type Tool = "pen" | "line" | "rectangle" | "square" | "circle" | "text" | "eraser" | "select";

interface Point {
  x: number;
  y: number;
}

const Whiteboard: React.FC<WhiteboardProps> = ({ classId, isTeacher = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const socketRef = useRef<any>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [tool, setTool] = useState<Tool>("pen");
  const [color, setColor] = useState("#000000");
  const [strokeWidth, setStrokeWidth] = useState(2);
  const [startPoint, setStartPoint] = useState<Point | null>(null);
  const [savedImageData, setSavedImageData] = useState<ImageData | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [textInput, setTextInput] = useState('');
  const [textPosition, setTextPosition] = useState<Point>({ x: 0, y: 0 });
  const [fontSize, setFontSize] = useState(16);

  const colorPresets = [
    "#000000", "#FF0000", "#00FF00", "#0000FF",
    "#FFFF00", "#FF00FF", "#00FFFF", "#FFA500",
    "#800080", "#008000", "#FFC0CB", "#A52A2A"
  ];

  const tools = [
    { id: "select" as Tool, icon: MousePointer, label: "Select" },
    { id: "pen" as Tool, icon: Pen, label: "Pen" },
    { id: "line" as Tool, icon: Minus, label: "Line" },
    { id: "rectangle" as Tool, icon: Square, label: "Rectangle" },
    { id: "square" as Tool, icon: Square, label: "Square" },
    { id: "circle" as Tool, icon: Circle, label: "Circle" },
    { id: "text" as Tool, icon: Type, label: "Text" },
    { id: "eraser" as Tool, icon: Eraser, label: "Eraser" },
  ];
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas size
    const container = canvas.parentElement;
    if (container) {
      canvas.width = container.clientWidth;
      canvas.height = 600;
    }

    // Fill with white background
    ctx.fillStyle = "#FFFFFF";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Initialize socket connection
    socketRef.current = io(SOCKET_SERVER_URL, {
      query: { classId }
    });

    // Listen for drawing data
    socketRef.current.on("draw-data", ({ imageData }) => {
      if (!isTeacher && imageData) {
        console.log('Student received drawing data');
        const img = new Image();
        img.onload = () => {
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          }
        };
        img.src = imageData;
      }
    });

    // Listen for canvas clear
    socketRef.current.on("clear-canvas", () => {
      if (!isTeacher) {
        console.log('Student received clear canvas');
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
      }
    });

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [classId, isTeacher]);

  const getCanvasPoint = (e: React.MouseEvent<HTMLCanvasElement>): Point => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    return {
      x: (e.clientX - rect.left) * scaleX,
      y: (e.clientY - rect.top) * scaleY,
    };
  };

  const drawText = (ctx: CanvasRenderingContext2D, text: string, x: number, y: number) => {
    ctx.fillStyle = color;
    ctx.font = `${fontSize}px Arial`;
    ctx.textBaseline = 'top';
    ctx.fillText(text, x, y);
  };

  const handleTextSubmit = () => {
    if (!textInput.trim()) return;

    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    // Convert display coordinates back to canvas coordinates for drawing
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    const canvasX = textPosition.x * scaleX;
    const canvasY = textPosition.y * scaleY;

    drawText(ctx, textInput, canvasX, canvasY);
    setIsTyping(false);
    setTextInput('');
    sendDrawingData();
  };

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isTeacher) return;

    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    const point = getCanvasPoint(e);

    if (tool === "text") {
      // Handle text tool - use display coordinates for positioning
      const rect = canvas.getBoundingClientRect();
      const displayPoint = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      };
      setTextPosition(displayPoint);
      setIsTyping(true);
      setTextInput('');
      return;
    }

    setIsDrawing(true);
    setStartPoint(point);

    if (tool === "pen" || tool === "eraser") {
      ctx.beginPath();
      ctx.moveTo(point.x, point.y);
    } else {
      // Save current canvas state for shape tools
      setSavedImageData(ctx.getImageData(0, 0, canvas.width, canvas.height));
    }
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !isTeacher || tool === "text") return;

    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    const point = getCanvasPoint(e);

    ctx.strokeStyle = tool === "eraser" ? "#FFFFFF" : color;
    ctx.lineWidth = strokeWidth;
    ctx.lineCap = "round";
    ctx.lineJoin = "round";

    if (tool === "pen" || tool === "eraser") {
      ctx.lineTo(point.x, point.y);
      ctx.stroke();
    } else if (startPoint && savedImageData) {
      // Restore canvas for live preview
      ctx.putImageData(savedImageData, 0, 0);

      ctx.beginPath();
      const width = point.x - startPoint.x;
      const height = point.y - startPoint.y;

      switch (tool) {
        case "line":
          ctx.moveTo(startPoint.x, startPoint.y);
          ctx.lineTo(point.x, point.y);
          ctx.stroke();
          break;
        case "rectangle":
          ctx.strokeRect(startPoint.x, startPoint.y, width, height);
          break;
        case "square":
          const size = Math.max(Math.abs(width), Math.abs(height));
          ctx.strokeRect(
            startPoint.x,
            startPoint.y,
            width >= 0 ? size : -size,
            height >= 0 ? size : -size
          );
          break;
        case "circle":
          const radius = Math.sqrt(width * width + height * height);
          ctx.arc(startPoint.x, startPoint.y, radius, 0, 2 * Math.PI);
          ctx.stroke();
          break;
      }
    }
  };

  const stopDrawing = () => {
    setIsDrawing(false);
    setStartPoint(null);
    setSavedImageData(null);
    sendDrawingData();
  };
  const clearCanvas = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    ctx.fillStyle = "#FFFFFF";
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    toast.success("Canvas cleared");

    if (isTeacher && socketRef.current) {
      socketRef.current.emit("clear-canvas", { classId });
    }
  };

  const saveImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.toBlob((blob) => {
      if (!blob) return;
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `whiteboard-${Date.now()}.png`;
      a.click();
      URL.revokeObjectURL(url);
      toast.success("Image saved");
    });
  };
  const sendDrawingData = () => {
    if (!isTeacher) return;

    const canvas = canvasRef.current;
    if (canvas && socketRef.current) {
      const imageData = canvas.toDataURL();
      socketRef.current.emit("draw-data", {
        classId,
        imageData
      });
    }
  };

  return (
    <div className="w-full max-w-none">
      <div className="flex flex-wrap gap-4 items-center p-4 bg-gray-50 rounded-lg">
        
        {isTeacher && (
          <>
            {/* Drawing Tools */}
            <div className="flex items-center gap-1 p-2 bg-white border border-gray-200 rounded-md shadow-sm">
              {tools.map((t) => (
                <Button
                  key={t.id}
                  variant="ghost"
                  size="icon"
                  onClick={() => setTool(t.id)}
                  className={cn(
                    "w-8 h-8 hover:bg-gray-100 transition-colors",
                    tool === t.id && "bg-blue-100 text-blue-600 hover:bg-blue-200"
                  )}
                  title={t.label}
                >
                  <t.icon className="h-4 w-4" />
                </Button>
              ))}
            </div>

            {/* Color Picker */}
            <div className="flex items-center gap-1 p-2 bg-white border border-gray-200 rounded-md shadow-sm">
              <Button
                variant="ghost"
                size="icon"
                className="w-8 h-8 hover:bg-gray-100 relative"
                title="Color"
              >
                <div className="absolute inset-1 rounded" style={{ backgroundColor: color }} />
                <Palette className="h-4 w-4 relative z-10 mix-blend-difference" />
              </Button>
              <input
                type="color"
                className="edu-form-field w-6 h-6 rounded border-0 cursor-pointer"
                   
                value={color}
                onChange={(e) => setColor(e.target.value)}
               
                style={{ background: 'none' }}
              />
            </div>

            {/* Brush Size / Font Size */}
            <div className="flex items-center gap-1 p-2 bg-white border border-gray-200 rounded-md shadow-sm">
              <Button
                variant="ghost"
                size="icon"
                className="w-8 h-8 hover:bg-gray-100"
                title={tool === 'text' ? `Font Size: ${fontSize}px` : `Brush Size: ${strokeWidth}px`}
              >
                {tool === 'text' ? <Type className="h-4 w-4" /> : <Settings className="h-4 w-4" />}
              </Button>
              {tool === 'text' ? (
                <input
                  type="range"
                  min="8"
                  

                  max="72"
                  value={fontSize}
                  onChange={(e) => setFontSize(Number(e.target.value))}
                  className="edu-form-field w-16 h-2"
                />
              ) : (
                <input
                  type="range"
                  min="1"
                  max="50"
                  value={strokeWidth}
                  onChange={(e) => setStrokeWidth(Number(e.target.value))}
                  className="edu-form-field w-16 h-2"
                />
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-1 p-2 bg-white border border-gray-200 rounded-md shadow-sm">
              <Button
                onClick={clearCanvas}
                variant="ghost"
                size="icon"
                className="w-8 h-8 hover:bg-gray-100"
                title="Clear Canvas"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </>
        )}
         {/* Save button - available for both teacher and student */}
        <Button
          onClick={saveImage}
          className="bg-purple-600 hover:bg-purple-700"
          size="sm"
        >
          Save Image
        </Button>
      </div>
      
      <div className="border rounded-lg w-full h-screen max-h-[80vh] min-h-[500px] relative resize overflow-auto">
        <canvas
          ref={canvasRef}
          className={`bg-gray-50 w-full h-full ${
            isTeacher
              ? tool === 'text'
                ? 'cursor-text'
                : 'cursor-crosshair'
              : 'cursor-default'
          }`}
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
          style={{
            display: 'block',
            outline: 'none'
          }}
        />

        {/* Text Input Box */}
        {isTyping && (
          <div
            className="absolute bg-white border-2 border-blue-500 rounded-md shadow-lg p-2 min-w-[200px]"
            style={{
              left: `${textPosition.x}px`,
              top: `${textPosition.y}px`,
              fontSize: `${fontSize}px`,
              color: color,
            }}
          >
            <input
              type="text"
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleTextSubmit();
                } else if (e.key === 'Escape') {
                  setIsTyping(false);
                  setTextInput('');
                }
              }}
              className="edu-form-field w-full border-none outline-none bg-transparent"
              style={{ fontSize: `${fontSize}px`, color: color }}
              placeholder="Type text here..."
              autoFocus
            />
            <div className="flex gap-2 mt-2">
              <Button
                size="sm"
                onClick={handleTextSubmit}
                className="h-6 text-xs"
              >
                Add
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setIsTyping(false);
                  setTextInput('');
                }}
                className="h-6 text-xs"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Whiteboard;


