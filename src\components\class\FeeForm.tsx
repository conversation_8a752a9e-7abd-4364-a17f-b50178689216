import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { ChevronRight, ChevronLeft, Trash2, Check, ChevronsUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useApp } from "@/context/AppContext";
import { useAuth } from "react-oidc-context"; // Updated import
import { ClassType, UserRole } from "@/types";
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";

import { generateAvatarUrl } from "@/lib/utils";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { getCountryCodesMetadata } from "@/services/classService";
import FeeStructureTable from "@/components/FeeStructureTable";
import { useSelector } from "react-redux";
import { createFeeStructure ,getStudentsFeeSelections, removeFeeSelection} from "@/services/feeServices";
import { createClassScheduleService } from "@/services/scheduleService";
import { useUserRole } from "@/hooks/useUserRole";
interface FeeFormProps {
  feeformData: {
    completed: boolean;
    feeAmount: number;
    paymentType: string;
    countryCode: string;
    description : string; // Optional field for description
    discountPercentage?: number; // Optional field for discount
  };

 updateFormData: (data: Partial<FeeFormProps["feeformData"]>) => void;
      classroomId : string | null;// <-- add this line
      completed?: boolean; // Add completed property

}

// Module-level guards to prevent duplicate fetches (works across StrictMode remounts)
let hasFetchedCountriesGlobal = false;
const hasFetchedStudentFeesGlobal = new Set<string>();

const FeeForm = ({ feeformData, updateFormData,classroomId,completed, refreshTrigger }: any) => {
  const [refreshKey, setRefreshKey] = useState(0);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const auth = useAuth(); // Add this line to get the auth object
  const { selectedRole } = useUserRole();
 const [studentFeeSelections,setStudentFeeSelections] = useState<[]>([]);
    const [countries, setCountries] = useState<{ 
        id: string;
        name: string;
        isoCode: string;
        currency: string;
        currencyCode: string;
        flag: string;
        region: string;
        subregion: string;
     }[]>([]);
    
 
    // Use the formData from props as initial state
    const [feeFormData, setFeeFormData] = useState({
      feeAmount: feeformData.feeAmount || 0,
      paymentType: feeformData.paymentType || "",
      countryCode: feeformData.countryCode || "",
      discountPercentage: feeformData.discountPercentage || 0,
      description: ""
    });
    const [countryOpen, setCountryOpen] = useState(false);
     interface FormErrors {
      feeAmount?: string;
      countryCode?: string;
      description?: string;
      paymentType?: string;
    }
     
     const [errors, setErrors] = useState<FormErrors>({});

    const validate = () => {
    let newErrors: FormErrors = {};
    if (feeFormData.feeAmount <= 0) {
      newErrors.feeAmount = "Fee amount must be greater than 0";
    }
    if (!feeFormData.countryCode) {
      newErrors.countryCode = "Country is required";
    }
    if (!feeFormData.paymentType) {
      newErrors.paymentType = "Payment type is required";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

    // Update both local state and parent component state
    const updateFeeFormData = (data: any) => {
      const updatedData = {
        ...feeFormData,
        ...data
      };
      setFeeFormData(updatedData);
      updateFormData(data);
    };
  
    // Sample country data
    //const [countries, setCountries] = useState<Country[]>();

    useEffect(() => {
      async function fetchCountries() {
        try {
          const data = await getCountryCodesMetadata(auth.user.access_token);
          setCountries(data);
        } catch (error) {
          toast.error("Failed to load countries");
        }
      }
      if (auth.user?.access_token && !hasFetchedCountriesGlobal) {
        hasFetchedCountriesGlobal = true;
        fetchCountries();
      }
    }, [auth.user?.access_token]);
    
    useEffect(() => {
      async function fetchStudentsFeeSelections() {
        try {
          const data = await getStudentsFeeSelections(auth.user.access_token, classroomId);
          setStudentFeeSelections(data);
        } catch (error) {
          toast.error("Failed to load students fee selections");
        }
      }
      if (auth.user?.access_token && classroomId && (!hasFetchedStudentFeesGlobal.has(classroomId) || typeof refreshTrigger !== 'undefined')) {
        hasFetchedStudentFeesGlobal.add(classroomId);
        fetchStudentsFeeSelections();
      }
    }, [auth.user?.access_token, classroomId, refreshTrigger]);
    
    const handleRemoveFeeSelection = async (feeSelectionId: string) => {
      try {
        await removeFeeSelection(auth.user.access_token, classroomId,feeSelectionId);
        toast.success("Fee selection removed successfully");
        // Refresh the fee selections list
        const data = await getStudentsFeeSelections(auth.user.access_token, classroomId);
        setStudentFeeSelections(data);
      } catch (error) {
        toast.error("Failed to remove fee selection");
      }
    };

    return(
        <div>
            <div className="mb-4 md:mb-6 flex justify-between items-center">
            
              <Button
                className="bg-purple-600 hover:bg-purple-700 text-white"
                onClick={() => setIsDialogOpen(true)}
                disabled={completed}
              >
                Add Fee
              </Button>
            </div>
            
            {classroomId && <FeeStructureTable classId={classroomId} refreshKey={refreshKey} />}
            <div className="space-y-4 mt-8">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium">Opted Fee Plans</h2>
        
      </div>

      <div className="border rounded-md">
        <Card>
          <CardContent className=" md:p-2 overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student Name</TableHead>
              <TableHead>Country/Region</TableHead>
              <TableHead>Fee Type</TableHead>
              <TableHead>Original Amount</TableHead>
              <TableHead>Discount</TableHead>
               <TableHead>Final Amount</TableHead>
              <TableHead>Selected Date</TableHead>
              {selectedRole === 'TEACHER' && <TableHead>Actions</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
                      
            {studentFeeSelections.map((fee: any) => (
              <TableRow key={fee.id}>
                <TableCell>{fee.studentName || "N/A"}</TableCell>
                <TableCell>{fee.country}</TableCell>
                <TableCell>{fee.paymentType}</TableCell>
                <TableCell>{fee.amount?.toFixed(2) || "0.00"}</TableCell>
                <TableCell>{fee.discountValue || 0}%</TableCell>
                <TableCell>
                  {fee.finalAmount ? `${fee.finalAmount.toFixed(2)}` : "N/A"}
                </TableCell>
                <TableCell>
                  {fee.updatedDate 
                    ? new Date(fee.updatedDate).toISOString().split('T')[0]
                    : "N/A"
                  }
                </TableCell>
                {selectedRole === 'TEACHER' && (
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveFeeSelection(fee.id)}
                      className="text-red-600 hover:text-red-800 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
            
            {studentFeeSelections.length === 0 &&  (
              <TableRow>
                <TableCell colSpan={selectedRole === 'TEACHER' ? 8 : 7} className="text-center py-6 text-gray-500">
                  No Student Fee Selections
                                  </TableCell>
              </TableRow>
            )}
          </TableBody>
         
        </Table>
         </CardContent>
         </Card>
      </div>
    </div>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                                  <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add Fee Plan</DialogTitle>
                  <DialogDescription>
                    Create a new fee Plan for this class
                  </DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Base Fee Amount*</label>
                    <Input 
                      type="number" 
                      value={feeFormData.feeAmount.toString()} 
                      onChange={e => {
                        updateFeeFormData({ feeAmount: parseFloat(e.target.value) || 0 });
                        if (errors.feeAmount) setErrors(prev => ({ ...prev, feeAmount: undefined }));
                      }} 
                      placeholder="Enter amount" 
                      className={errors.feeAmount ? 'border-red-500' : 'edu-form-field'}
                    />
                    {errors.feeAmount && <p className="text-red-500 text-xs mt-1">{errors.feeAmount}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Payment Type*</label>
                    <Select 
                      value={feeFormData.paymentType} 
                      onValueChange={value => {
                        updateFeeFormData({ paymentType: value });
                        if (errors.paymentType) setErrors(prev => ({ ...prev, paymentType: undefined }));
                      }}
                    >
                        <SelectTrigger className={errors.paymentType ? 'border-red-500' : 'edu-form-field'}>
                        <SelectValue placeholder="Select payment type" />
                        </SelectTrigger>
                        <SelectContent>
                        <SelectItem value="MONTHLY">Monthly</SelectItem>
                          <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                        <SelectItem value="YEARLY">Yearly</SelectItem>
                        <SelectItem value="ONE_TIME">One Time</SelectItem>
                        </SelectContent>
                    </Select>
                    {errors.paymentType && <p className="text-red-500 text-xs mt-1">{errors.paymentType}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Country*</label>
                    <Popover open={countryOpen} onOpenChange={setCountryOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={countryOpen}
                          className={`w-full justify-between ${errors.countryCode ? 'border-red-500' : ''}`}
                        >
                          {feeFormData.countryCode
                            ? countries.find(country => country.isoCode === feeFormData.countryCode)?.flag + " " + 
                              countries.find(country => country.isoCode === feeFormData.countryCode)?.name + " (" +
                              countries.find(country => country.isoCode === feeFormData.countryCode)?.currencyCode + ")"
                            : "Select Country..."}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput placeholder="Search country..." />
                          <CommandList>
                            <CommandEmpty>No country found.</CommandEmpty>
                            <CommandGroup>
                              {countries.sort((a, b) => a.name.localeCompare(b.name)).map(country => (
                                <CommandItem
                                  key={country.id}
                                  value={`${country.name} ${country.currencyCode}`}
                                  onSelect={() => {
                                    updateFeeFormData({ countryCode: country.isoCode });
                                    if (errors.countryCode) setErrors(prev => ({ ...prev, countryCode: undefined }));
                                    setCountryOpen(false);
                                  }}
                                >
                                  <Check
                                    className={`mr-2 h-4 w-4 ${
                                      feeFormData.countryCode === country.isoCode ? "opacity-100" : "opacity-0"
                                    }`}
                                  />
                                  {country.flag} {country.name} ({country.currencyCode})
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    {errors.countryCode && <p className="text-red-500 text-xs mt-1">{errors.countryCode}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Discount (%)</label>
                    <Input type="number"className='edu-form-field'
               value={feeFormData.discountPercentage.toString()} onChange={e => updateFeeFormData({
                                            discountPercentage: parseFloat(e.target.value) || 0
                                            })} placeholder="Enter Discount" />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium mb-1">Description</label>
                    <Input value={feeFormData.description} className='edu-form-field'
              onChange={e => updateFeeFormData({
                                            description: e.target.value
                                            })} placeholder="Enter fee description" />
                  </div>
                  <div className="md:col-span-2 flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsDialogOpen(false);
                        setErrors({});
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                      onClick={async () => {
                          if (validate()) {
                          try {
                            await createFeeStructure(auth.user.access_token, classroomId, feeFormData);
                            toast.success("Fee added!");
                            setRefreshKey(prev => prev + 1);
                            setErrors({});
                            setIsDialogOpen(false);
                          } catch (error: any) {
                            if (error.response?.data?.code === "DUPLICATE_FEE_STRUCTURE") {
                              toast.error(error.response.data.details || "Fee Plan already exists");
                            } else {
                              toast.error("Fee Plan already exists");
                            }
                          }
                        } else {
                          toast.error("Please fill all required fields correctly");
                        }
                      }}
                    >
                      Add Fee
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
        </div>
      )
  };

export default FeeForm; 