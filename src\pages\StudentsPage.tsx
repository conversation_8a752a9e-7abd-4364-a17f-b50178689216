import React, { useState, useEffect, useRef } from "react";
import { useAuth } from "react-oidc-context";
import { useApp } from "@/context/AppContext";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Search, Filter, Mail, Plus, Users, Bell } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Student,  getStudents } from "@/services/studentService";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { getClassesFromBackend, ClassData } from "@/services/classService";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogDescription,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { generateAvatarUrl } from "@/lib/utils";
import { UserRole } from "@/types";
import { Console } from "console";
import { assignStudentToClassroom ,getMyEnrollments} from "@/services/enrollmentService";
import { useSelector } from "react-redux";
import { useUserRole } from "@/hooks/useUserRole";

const STUDENTS_PER_PAGE = 10;

export default function StudentsPage() {
  const { classes } = useApp();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [students, setStudents] = useState<Student[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const hasFetchedStudents = useRef(false);
  const hasFetchedClasses = useRef(false);
  const { toast } = useToast();
  const [selectedStudent, setSelectedStudent] = useState<string | null>(null);
  const [isAssignClassOpen, setIsAssignClassOpen] = useState(false);
  const [availableClasses, setAvailableClasses] = useState<ClassData[]>([]);
  const [selectedClasses, setSelectedClasses] = useState<string[]>([]);
   const auth = useAuth(); 
  const { selectedRole } = useUserRole();

   // Get user from OIDC
     const user = auth.isAuthenticated ? {
       id: auth.user?.profile.sub || "",
       name: auth.user?.profile.name || "User",
       email: auth.user?.profile.email || "",
       role: (auth.user?.profile["custom:role"] as UserRole) ,
       avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
     } : null;
   
    useEffect(() => {
      const fetchStudents = async () => {
        setIsLoading(true);
        try {
          const data = await getMyEnrollments(auth.user.access_token);
          setStudents(data);
        } catch (error) {
          console.error("Error fetching students:", error);
        } finally {
          setIsLoading(false);
        }
      };

      if (auth.user?.access_token && !hasFetchedStudents.current) {
        hasFetchedStudents.current = true;
        fetchStudents();
      }
    }, [auth.user?.access_token]);

   useEffect(() => {
    const fetchClasses = async () => {
      try {
        const classes = await getClassesFromBackend(auth.user.access_token);
        setAvailableClasses(classes.content || []);
      } catch (error) {
        console.error("Error fetching classes:", error);
        setAvailableClasses([]);
      }
    };

    if (auth.user?.access_token && !hasFetchedClasses.current) {
      hasFetchedClasses.current = true;
      fetchClasses();
    }
  }, [auth.user?.access_token]);

  const handleAssignClass = (studentId: string) => {
    setSelectedStudent(studentId);
    console.log("Selected student for class assignment:", availableClasses);
    setSelectedClasses([]);
    setIsAssignClassOpen(true);
  };

  const handleClassSelection = (classId: string) => {
    setSelectedClasses(current => {
      if (current.includes(classId)) {
        return current.filter(id => id !== classId);
      } else {
        return [...current, classId];
      }
    });
  };

  const handleAssignConfirm = async () => {
  if (selectedStudent && selectedClasses.length > 0) {
    try {
      for (const classId of selectedClasses) {
        await assignStudentToClassroom(auth.user.access_token, classId, selectedStudent);
      }
      toast({
        title: "Classes Assigned",
        description: `Successfully assigned ${selectedClasses.length} classes to the student.`,
      });
      setIsAssignClassOpen(false);
      setSelectedStudent(null);
      setSelectedClasses([]);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to assign classes.",
        variant: "destructive",
      });
    }
  }
};
  
  const filteredStudents = students.filter(student => {
    const matchesSearch = 
      (student.userName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (student.email?.toLowerCase() || '').includes(searchTerm.toLowerCase());
    
    if (selectedFilter === "all") return matchesSearch;
    if (selectedFilter === "paid") return matchesSearch && student.payments.status === "paid";
    if (selectedFilter === "pending") return matchesSearch && student.payments.status === "pending";
    if (selectedFilter === "overdue") return matchesSearch && student.payments.status === "overdue";
    
    return matchesSearch;
  });
  
  const pageCount = Math.ceil(filteredStudents.length / STUDENTS_PER_PAGE);
  const paginatedStudents = filteredStudents.slice(
    (page - 1) * STUDENTS_PER_PAGE,
    page * STUDENTS_PER_PAGE
  );
  
  const getPaymentStatusBadge = (status) => {
    switch (status) {
      case "paid":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Paid</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Pending</Badge>;
      case "overdue":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Overdue</Badge>;
      default:
        return null;
    }
  };
  
  const handleBackToDashboard = () => {
  
    if (selectedRole === UserRole.TEACHER) {
      navigate('/teacher-dashboard');
    } else if (selectedRole === UserRole.STUDENT) {
      navigate('/student-dashboard');
    } else if (selectedRole=== UserRole.PARENT) {
      navigate('/parent-dashboard');
    } else {
      navigate('/');
    }
  };

  const handleNotifyStudent = (studentId: number) => {
    console.log("Notifying student:", studentId);
    toast({
      title: "Student Notification",
      description: "Notification sent to student",
      variant: "default"
    });
  };

  const handleNotifyParent = (studentId: number) => {
    console.log("Notifying parent:", studentId);
    toast({
      title: "Parent Notification",
      description: "Notification sent to parent",
      variant: "default"
    });
  };


  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex-grow">
        <header className="bg-white p-4 border-b border-gray-200 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={handleBackToDashboard}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-xl font-semibold">My Enrollments</h1>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="relative w-64">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-500" />
              <Input 
                className="edu-form-field pl-10" 
                placeholder="Search students" 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
       {  /*  <Button variant="outline" size="sm" className="flex gap-1">
              <Filter className="h-4 w-4" /> Filter
            </Button> <Button 
              className="bg-purple-600 hover:bg-purple-700" 
              size="sm"
              onClick={() => navigate('/add-student')}
            >
              <Plus className="h-4 w-4 mr-1" /> Add Student
            </Button>*/}
           
          </div>
        </header>
        
        <div className="p-6">
          {isLoading ? (
            <div className="flex justify-center items-center p-12">
              <p>Loading students...</p>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Classes</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Enrollment Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {students.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{student.userName}</div>
                         </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {student.classroomName }
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="w-full h-2 bg-gray-200 rounded-full">
                          <div 
                            className="h-2 bg-green-500 rounded-full" 
                            style={{ width: `${student.progress}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {student.progress}% Complete
                        </div>
                      </TableCell>
                     
                      <TableCell>
                        {student.status}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleAssignClass(student.id)}>
                              <Users className="h-4 w-4 mr-2" />
                              <span>Assign class</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleNotifyStudent(Number(student.id))}>
                              <Bell className="h-4 w-4 mr-2" />
                              <span>Notify</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleNotifyParent(Number(student.id))}>
                              <Mail className="h-4 w-4 mr-2" />
                              <span>Notify parent</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {filteredStudents.length > STUDENTS_PER_PAGE && (
                <div className="flex justify-between items-center p-4 border-t border-gray-200">
                  <div className="text-sm text-gray-500">
                    Showing {(page - 1) * STUDENTS_PER_PAGE + 1} to {Math.min(page * STUDENTS_PER_PAGE, filteredStudents.length)} of {filteredStudents.length} students
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => setPage(p => Math.max(1, p - 1))}
                      disabled={page === 1}
                    >
                      Previous
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => setPage(p => Math.min(pageCount, p + 1))}
                      disabled={page === pageCount}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      
      <Dialog open={isAssignClassOpen} onOpenChange={setIsAssignClassOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Assign Classes</DialogTitle>
            <DialogDescription>
              Select classes to assign to this student.
            </DialogDescription>
          </DialogHeader>
            <div className="grid gap-4 py-4 max-h-64 overflow-y-auto">            {availableClasses && availableClasses.length > 0 ? (
              availableClasses.map((classItem) => (
                <div
                  key={classItem.id}
                  className="flex items-center space-x-2"
                >
                  <Checkbox
                    id={classItem.id}
                    checked={selectedClasses.includes(classItem.id)}
                    onCheckedChange={() => handleClassSelection(classItem.id)}
                  />
                  <label
                    htmlFor={classItem.id}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {classItem.className} - {classItem.subjectName}
                  </label>
                </div>
              ))
            ) : (
              <p className="text-sm text-muted-foreground">No classes available.</p>
            )}
          </div>
          <DialogFooter>
            <Button
              onClick={handleAssignConfirm}
              disabled={selectedClasses.length === 0}
            >
              Assign Selected Classes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
