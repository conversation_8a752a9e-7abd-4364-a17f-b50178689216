  export async function recordBulkAttendance(
    accessToken: string,
    classroomId: string,
    payload: {
      attendanceDate: string;
      studentAttendances: { studentId: string; status: string; notes?: string }[];
    }
  ) {
    console.log(payload)
    const response = await fetch(`/api/attendanceManagement/v1/classrooms/${classroomId}/attendance/bulk`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${accessToken}`,
      },
      body: JSON.stringify(payload),
    });
    if (response.status === 401) {
      window.location.href = "/";
      return;
    }
    if (!response.ok) {
      const errorData = await response.json();
      const error = new Error("Failed to record attendance") as any;
      error.response = { data: errorData };
      throw error;
    }
    return response.json();
  }
export async function getAttendanceRecordsForRange(accessToken: string, classroomId: string, startDate: string, endDate: string) {
  const response = await fetch(`/api/attendanceManagement/v1/classrooms/${classroomId}/attendance/range?startDate=${startDate}&endDate=${endDate}`, {
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json"
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch attendance records");
  }
  return response.json();
}

export async function getAttendanceStatsSummary(accessToken: string, classroomId: string,startDate : string,endDate :string) {
  const response = await fetch(`/api/attendanceManagement/v1/classrooms/${classroomId}/attendance/comprehensive-statistics?startDate=${startDate}&endDate=${endDate}`, {
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json"
    }      

  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch attendance summary");
  }
  return response.json();
}

export async function getAttendanceByStudentId(accessToken: string, startDate : string,endDate :string) {
  const response = await fetch(`/api/attendanceManagement/v1/dashboard?startDate=${startDate}&endDate=${endDate}`, {
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json"
    }      

  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch attendance summary");
  }
  return response.json();
}

export async function getCompAttenforStudent(accessToken: string, studentId :string,startDate : string,endDate :string) {
  const response = await fetch(`/api/attendanceManagement/v1/students/${studentId}/attendance/comprehensive-report?startDate=${endDate}&endDate=${endDate}`, {
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json"
    }      

  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch attendance summary");
  }
  return response.json();
}