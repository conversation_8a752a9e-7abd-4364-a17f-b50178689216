
import { BookOpen, Users, Calendar, Clock, Home } from "lucide-react";
import { UserRole } from "@/types";
import { NavigationItem } from "@/types";

export const getHomeUrl = (role: UserRole | undefined): string => {
  return role === UserRole.TEACHER ? "/teacher-dashboard" : 
         role === UserRole.STUDENT ? "/student-dashboard" : 
         role === UserRole.PARENT ? "/parent-dashboard" : "/";
};

export const getNavigationItems = (role: UserRole | undefined) => {
  // Common items for all users
  const commonItems: NavigationItem[] = [{
    title: "Home",
    url: getHomeUrl(role),
    icon: Home
  }];

  // Role-specific navigation items
  const teacherItems: NavigationItem[] = [{
    title: "Classes",
    url: "/classes",
    icon: BookOpen,
    role: UserRole.TEACHER
  }, {
    title: "Students",
    url: "/students",
    icon: Users,
    role: UserRole.TEACHER
  }, {
    title: "Meeting Scheduler",
    url: "/meeting-scheduler",
    icon: Clock,
    role: UserRole.TEACHER
  }];
  
  const parentItems: NavigationItem[] = [{
    title: "Teacher Meetings",
    url: "/parent-meetings",
    icon: Calendar,
    role: UserRole.PARENT
  }];
  
  const studentItems: NavigationItem[] = [{
    title: "My Classes",
    url: "/student-classes",
    icon: BookOpen,
    role: UserRole.STUDENT
  }, {
    title: "Schedule",
    url: "/schedule/all",
    icon: Calendar,
    role: UserRole.STUDENT
  }];

  // Select menu items based on user role
  let roleSpecificItems: NavigationItem[] = [];
  if (role === UserRole.TEACHER) {
    roleSpecificItems = teacherItems;
  } else if (role === UserRole.PARENT) {
    roleSpecificItems = parentItems;
  } else if (role === UserRole.STUDENT) {
    roleSpecificItems = studentItems;
  }

  return [...commonItems, ...roleSpecificItems];
};
