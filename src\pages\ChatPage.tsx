import React, { useState, useEffect } from "react";
import { useAuth } from "react-oidc-context";
import { useApp } from "@/context/AppContext";
import { useNavigate } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { ChevronLeft, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserRole } from "@/types";
import { generateAvatarUrl } from "@/lib/utils";
import { useUserRole } from "@/hooks/useUserRole";
import { useChat } from "@/hooks/useChat";
import { ChatContactList } from "@/components/chat/ChatContactList";
import { ChatMessageList } from "@/components/chat/ChatMessageList";
import { ChatInput } from "@/components/chat/ChatInput";


export default function ChatPage() {
  const auth = useAuth();
  const { messages: contextMessages } = useApp();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { selectedRole } = useUserRole();
  
  // Get user from OIDC
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole),
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;
  
  const {
    contacts,
    messages,
    loading,
    selectedContactId,
    setSelectedContactId,
    sendMessage
  } = useChat(user?.id || null);
  
  const [searchTerm, setSearchTerm] = useState("");
  const [showContactsOnMobile, setShowContactsOnMobile] = useState(true);
  
  // Get conversation messages (fallback to context messages if hook messages are empty)
  const conversationMessages = messages.length > 0 ? messages : 
    contextMessages.filter(m => 
      (m.senderId === user?.id && m.receiverId === selectedContactId) || 
      (m.senderId === selectedContactId && m.receiverId === user?.id)
    ).sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  
  // Handle sending a message
  const handleSendMessage = async (content: string) => {
    if (!selectedContactId || !user) return;
    await sendMessage(content, selectedContactId);
  };
  
  const selectedContact = contacts.find(c => c.id === selectedContactId);

  // Function to get correct dashboard route for back button
  const getDashboardRoute = () => {
    switch(selectedRole) {
      case UserRole.TEACHER:
        return "/teacher-dashboard";
      case UserRole.STUDENT:
        return "/student-dashboard";
      case UserRole.PARENT:
        return "/parent-dashboard";
      default:
        return "/";
    }
  }
  
  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-grow flex flex-col md:flex-row">
        {!isMobile && (
          <div className="w-80 border-r border-gray-200 bg-white">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center gap-3 mb-4">
                <Button 
                  variant="ghost" 
                  className="p-2" 
                  onClick={() => navigate(getDashboardRoute())}
                >
                  <ChevronLeft className="h-5 w-5" />
                </Button>
                <h2 className="text-xl font-semibold">Chat</h2>
              </div>
            </div>
            
            <ChatContactList
              contacts={contacts}
              selectedContactId={selectedContactId}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              onContactSelect={setSelectedContactId}
              showSelection={true}
            />
          </div>
        )}
        
        {isMobile && showContactsOnMobile && (
          <div className="flex-grow flex flex-col bg-white">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center gap-3 mb-4">
                <Button 
                  variant="ghost" 
                  className="p-2" 
                  onClick={() => navigate(getDashboardRoute())}
                >
                  <ChevronLeft className="h-5 w-5" />
                </Button>
                <h2 className="text-xl font-semibold">Chat</h2>
              </div>
            </div>
            
            <ChatContactList
              contacts={contacts}
              selectedContactId={selectedContactId}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              onContactSelect={(contactId) => {
                setSelectedContactId(contactId);
                setShowContactsOnMobile(false);
              }}
              showSelection={false}
            />
          </div>
        )}
        
        {isMobile && !showContactsOnMobile && selectedContactId ? (
          <div className="flex-grow flex flex-col">
            <div className="bg-white p-4 border-b border-gray-200 flex items-center gap-3">
              <Button 
                variant="ghost" 
                className="p-1 h-8 w-8" 
                onClick={() => setShowContactsOnMobile(true)}
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
              <Avatar className="h-8 w-8">
                <AvatarImage src={selectedContact?.avatar} alt={selectedContact?.name} />
                <AvatarFallback>{selectedContact?.name[0]}</AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-medium">{selectedContact?.name}</h3>
                <span className="text-xs text-gray-500 capitalize">{selectedContact?.role}</span>
              </div>
            </div>
            
            <ChatMessageList
              messages={conversationMessages}
              currentUserId={user?.id || ""}
              className="flex-grow bg-gray-50 p-3 md:p-6 overflow-y-auto"
            />
            
            <ChatInput
              onSendMessage={handleSendMessage}
              disabled={loading}
            />
          </div>
        ) : !isMobile ? (
          <div className="flex-grow flex flex-col">
            {!selectedContactId ? (
              <div className="flex-grow flex items-center justify-center bg-white">
                <div className="text-center">
                  <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Send className="h-10 w-10 text-gray-400" />
                  </div>
                  <h2 className="text-xl font-semibold mb-2">Your Messages</h2>
                  <p className="text-gray-500 max-w-sm">
                    Select a contact to start chatting or search for someone specific.
                  </p>
                </div>
              </div>
            ) : (
              <>
                <div className="bg-white p-4 border-b border-gray-200 flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={selectedContact?.avatar} alt={selectedContact?.name} />
                    <AvatarFallback>{selectedContact?.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium">{selectedContact?.name}</h3>
                    <span className="text-xs text-gray-500 capitalize">{selectedContact?.role}</span>
                  </div>
                </div>
                
                <ChatMessageList
                  messages={conversationMessages}
                  currentUserId={user?.id || ""}
                  className="flex-grow bg-gray-50 p-6 overflow-y-auto"
                />
                
                <ChatInput
                  onSendMessage={handleSendMessage}
                  disabled={loading}
                />
              </>
            )}
          </div>
        ) : (
          <div className="flex-grow flex items-center justify-center bg-white">
            <div className="text-center p-6">
              <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Send className="h-8 w-8 text-gray-400" />
              </div>
              <h2 className="text-lg font-semibold mb-2">Your Messages</h2>
              <p className="text-gray-500 max-w-xs mx-auto">
                Select a contact to start chatting or search for someone specific.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
