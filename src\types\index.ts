export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar: string;
}

export enum UserRole {
  TEACHER = "TEACHER",
  STUDENT = "STUDENT",
  PARENT = "PARENT",
}
export interface AssignmentSubmission {
  studentId: string;
  submittedAt: Date;
  status: "pending" | "complete" | "graded";
  score?: number;
  feedback?: string;
  fileUrl?: string;
  answers?: { questionIndex: number; answer: string; selectedOptions?: number[] }[];
}
export interface LeaveRequest {
  id: string;
  studentId: string;
  classId: string;
  startDate: Date;
  endDate: Date;
  reason: string;
  status: "DENIED"| "PENDING" | "APPROVED";
  createdDate: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  teacherComments?: string;
  studentName : string;
}
export enum ClassType {
  ONLINE = "ONLINE",
  OFFLINE = "OFFLINE",
  REGULAR = "REGULAR",
  ADVANCED = "ADVANCED",
  ELECTIVE = "ELECTIVE",
  PRIVATE = "PRIVATE",
  GROUP = "GROUP",
  WEEKEND = "WEEKEND"
}


export interface Class {
  id: string;
  teacherId :string;
  courseTitle : string;
  className: string;
  subjectName: string;
  description: string;
  level: string;
  batchName: string;
  parents: string[];
  students :string[];
  assignments: string[];
  announcements: string[];
  classType: ClassType;
  capacity :number;
  image?: string;
  joinCode: string;
  joinUrl: string;
  createdAt: Date;
  fee?: number;
  feeStructures?: FeeStructure[];
  onlineMeeting?: {
    platform: string;
    link: string;
  };
}

export interface Assignment {
  id: string;
  active:boolean;
  isSubmitted: boolean; 
  totalQuestions: number;
  totalMarks: number;
  obtainedMarks: number;  
  percentage: number;
  title: string;
  description: string;
  totalSubmissions: number;
  dueDate: Date;
  classroomId: string;
  submittedBy: string[];
  questions?: string[];
  fileUrl?: string;
  createdAt: Date;
  createdBy: string;
  status:string;
  submissions?: AssignmentSubmission[];
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  createdDate: Date;
  updatedDate :Date;
  classroomId: string;
  authorId: string;
  fileUrl: string | null;
  icon?: string;
  iconColor?: string;
  attachedFileIds?: [];
  isEditing?: boolean;
}

export interface Syllabus {
  id: string;
  title: string;
  description: string;
  status: "completed" | "pending" | "ongoing" ;
}

export interface ScheduleEvent {
  id: string;
  classroomId: string;
  classroomName: string;
  description: string;
  startTime: Date;
  startDate: string;
  endDate: string;
  status  : "SCHEDULED" | "COMPLETED" | "CANCELLED";
  endTime: Date;
  recurring: boolean;
  recurrenceType?: string;
  imageUrl: string;
  grade?: string;
  duration: string;
  date: string;
  meetingLink: string;
  daysOfWeek :string[];
  sessionStartTime: string;
  sessionEndTime: string;
}

export interface ChatMessage {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: Date;
  read: boolean;
}

export interface Payment {
  id: string;
  studentId: string;
  classId: string;
  amount: number;
 // date: string;
  status: PaymentStatus;
  description?: string;
  paymentMethod?: string;
  receiptNumber?: string;
  memo?: string;
  createdAt: Date;
  date: string;
  updatedAt: Date;
}

export enum PaymentStatus {
  PENDING = "pending",
  PAID = "paid",
  OVERDUE = "overdue",
  COMPLETED = "completed",
  FAILED = "failed"
}

export interface Expense {
  id: string;
  description: string;
  amount: number;
  date: string;
  categoryId: string;
}

export interface ExpenseCategory {
  id: string;
  name: string;
}

export interface FeeStructure {
  id: string;
  country: string;
  paymentType: string;
  feeAmount: number;
  discountPercentage: number;
  description : string;
  status: "active" | "inactive";
}

export interface Attendance {
  id: string;
  studentId: string;
  classId: string;
  date: string;
  status: "present" | "absent" | "late" | "excused";
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ParentTeacherMeeting {
  id: string;
  teacherId: string;
  classId: string;
  title: string;
  description: string;
  date: Date;
  slots: MeetingSlot[];
  createdAt: Date;
  isActive: boolean;
}

export interface MeetingSlot {
  id: string;
  startTime: Date;
  endTime: Date;
  isBooked: boolean;
  bookedBy?: string;
  studentId?: string;
}

export interface NavigationItem {
  title: string;
  url: string;
  icon: React.ComponentType;
  role?: UserRole;
}
