
import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import { Announcement } from "@/types";
import { getTop5Announcements } from "@/services/announcementService";
import { toast } from "sonner";
import { AnnouncementItem } from "./AnnouncementItem";
import { useSelector } from "react-redux";
import { useAuth } from "react-oidc-context"; 
import { generateAvatarUrl } from "@/lib/utils";
import { UserRole } from "@/types";
interface AnnouncementsSectionProps {
  hideCreateButton?: boolean;
}

export const AnnouncementsSection: React.FC<AnnouncementsSectionProps> = ({ 
  hideCreateButton = false 
}) => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const hasFetched = useRef(false);
   const auth = useAuth(); // Use OIDC auth
  const userData = useSelector((state: any) => state.user.userData);

  // Get user from OIDC
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) ,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;
  const fetchAnnouncements = async () => {
      try {
        setIsLoading(true);
        const data = await getTop5Announcements(auth.user.access_token);
        console.log("Fetched announcements:", data);    
        setAnnouncements(data.map(a => ({
              ...a,
              createdAt: Number(a.createdAt)
            })));
       setIsLoading(false);
      } catch (error) {
        toast.error("Failed to load announcements");
        setIsLoading(false);
      }
    };

  useEffect(() => {
    if (auth.user?.access_token && !hasFetched.current) {
      hasFetched.current = true;
      fetchAnnouncements();
    }
  }, [auth.user?.access_token]);

  const getAnnouncementIcon = (iconType: string) => {
    return "📢";
  };

  const getIconColorClass = (color: string) => {
    switch (color) {
      case "red":
        return "text-red-500";
      case "blue":
        return "text-blue-500";
      case "amber":
        return "text-amber-500";
      case "green":
        return "text-green-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <div>
      {!hideCreateButton && (
       <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-500 bg-clip-text text-transparent">
            📢 Announcements
          </h2>
          <Link to="/announcements">
            <Button size="sm" className="bg-gradient-to-r from-purple-600 to-pink-500 hover:from-purple-700 hover:to-pink-600 text-white text-xs sm:text-sm rounded-full shadow-lg transform hover:scale-105 transition-all duration-200">
              <span className="mr-1">✨</span> Create New
            </Button>
          </Link>
        </div>
      )}
        
      <div className="bg-white rounded-lg ">
        {isLoading ? (
          <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
            <div className="animate-pulse flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>) : announcements.length > 0 ? (
          <div >
            {announcements.map((announcement, index) => (
              <div key={announcement.id} className="p-2">
                <AnnouncementItem
                  announcement={announcement}
                  getAnnouncementIcon={getAnnouncementIcon}
                  getIconColorClass={getIconColorClass}
                  isLast={index === announcements.length - 1}
                />
              </div>
            ))}
          </div>
        ) : (
             <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl p-8 text-center border border-gray-100">
            <div className="text-4xl mb-3">📭</div>
            <p className="text-gray-500 text-sm sm:text-base font-medium">No announcements available</p>
            <p className="text-gray-400 text-xs mt-1">Check back later for updates!</p>
          </div>    )}
          <Link to="/announcements" className="flex justify-end text-sm flex items-center text-gray-500 hover:text-gray-700 mt-4">
            View All <ChevronRight className="h-4 w-4" />
          </Link>
      </div>
    </div>
  );
};
