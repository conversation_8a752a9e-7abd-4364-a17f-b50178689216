import { format } from 'date-fns';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Sparkles,Wand2, Check, Trash2,Lightbulb ,Award,CalendarIcon,
  FileText,ListChecks,CheckCircle2,GripVertical,Plus
} from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { RichTextEditor } from "@/components/ui/rich-text-editor";

interface ManualAssignmentFormProps {
  onSuccess?: () => void;
  onSubmit?: (values: any) => void;
  assignment?: any;
  mode?: 'create' | 'edit' | 'view';
}

type QuestionType = 'text' | 'mcq' | 'checkbox';

interface Question {
  id: string;
  type: QuestionType;
  question: string;
  options?: string[];
  correctAnswer?: number;
  points: number;
}

export function ManualAssignmentForm({ onSuccess, onSubmit, assignment, mode = 'create' }: ManualAssignmentFormProps) {
  const [title, setTitle] = useState(assignment?.title || '');
  const [description, setDescription] = useState(assignment?.description || '');
  const [date, setDate] = useState<Date>(assignment?.dueDate ? new Date(assignment.dueDate) : undefined);
  const [questions, setQuestions] = useState<Question[]>(() => {
    if (assignment?.questions && assignment.questions.length > 0) {
      return assignment.questions.map((q, index) => {
        if (typeof q === 'string') {
          return { id: (index + 1).toString(), type: 'text', question: q, points: 10 };
        }
        return {
          id: (index + 1).toString(),
          type: q.type === 'MULTIPLE_CHOICE' || q.type === 'mcq' ? 'mcq' : 'text',
          question: q.questionText || q.text || q.question || '',
          options: (q.type === 'MULTIPLE_CHOICE' || q.type === 'mcq') ? (q.options || ['', '', '', '']) : undefined,
          correctAnswer: q.correctAnswer ? (q.options ? q.options.findIndex(opt => opt === q.correctAnswer) : 0) : undefined,
          points: q.marks || 10
        };
      });
    }
    return [];
  });

  const addQuestion = (type: QuestionType) => {
    const newQuestion: Question = {
      id: Date.now().toString(),
      type,
      question: '',
      options: type !== 'text' ? ['', '', '', ''] : undefined,
      correctAnswer: type !== 'text' ? 0 : undefined,
      points: 10
    };
    setQuestions([...questions, newQuestion]);
  };

  const removeQuestion = (id: string) => {
    setQuestions(questions.filter(q => q.id !== id));
  };

  const updateQuestion = (id: string, field: string, value: any) => {
    setQuestions(questions.map(q => 
      q.id === id ? { ...q, [field]: value } : q
    ));
  };

  const updateOption = (questionId: string, optionIndex: number, value: string) => {
    setQuestions(questions.map(q => {
      if (q.id === questionId && q.options) {
        const newOptions = [...q.options];
        newOptions[optionIndex] = value;
        return { ...q, options: newOptions };
      }
      return q;
    }));
  };

  const getQuestionIcon = (type: QuestionType) => {
    switch (type) {
      case 'text': return <FileText className="w-4 h-4" />;
      case 'mcq': return <ListChecks className="w-4 h-4" />;
      case 'checkbox': return <CheckCircle2 className="w-4 h-4" />;
    }
  };

  const getQuestionLabel = (type: QuestionType) => {
    switch (type) {
      case 'text': return 'Text Answer';
      case 'mcq': return 'Multiple Choice';
      case 'checkbox': return 'Checkboxes';
    }
  };

  const totalPoints = questions.reduce((sum, q) => sum + q.points, 0);

  // Update state when assignment prop changes
  useEffect(() => {
    if (assignment && mode !== 'create') {
      setTitle(assignment.title || '');
      setDescription(assignment.description || '');
      setDate(assignment.dueDate ? new Date(assignment.dueDate) : undefined);
      
      if (assignment.questions && assignment.questions.length > 0) {
        const mappedQuestions = assignment.questions.map((q, index) => {
          if (typeof q === 'string') {
            return { id: (index + 1).toString(), type: 'text' as QuestionType, question: q, points: 10 };
          }
          return {
            id: (index + 1).toString(),
            type: (q.type === 'MULTIPLE_CHOICE' || q.type === 'mcq' ? 'mcq' : 'text') as QuestionType,
            question: q.questionText || q.text || q.question || '',
            options: (q.type === 'MULTIPLE_CHOICE' || q.type === 'mcq') ? (q.options || ['', '', '', '']) : undefined,
            correctAnswer: q.correctAnswer ? (q.options ? q.options.findIndex(opt => opt === q.correctAnswer) : 0) : undefined,
            points: q.marks || 10
          };
        });
        setQuestions(mappedQuestions);
      }
    }
  }, [assignment, mode]);

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card className="p-0 sm:p-6 border-2 border-blue-100 bg-blue-50/30">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Assignment Title *</Label>
            <Input 
              id="title" 
              placeholder="e.g., Chapter 5 Review: Photosynthesis"
                                
              className='edu-form-field'
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description / Instructions</Label>
            <RichTextEditor
              value={description}
              onChange={setDescription}
              placeholder="Provide clear instructions and requirements for the assignment..."
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <CalendarIcon className="w-4 h-4" />
                Due Date *
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start bg-white">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="total-points" className="flex items-center gap-2">
                <Award className="w-4 h-4" />
                Total Points
              </Label>
              <Input 
                id="total-points" 
                type="number" 

                placeholder="100"
                value={totalPoints}
                readOnly
                className='edu-form-field'
              
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Questions Section */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div>
            <h3 className="text-gray-900">Questions</h3>
            <p className="text-gray-600">Build your assignment question by question</p>
          </div>
          <div className="flex items-center gap-2">
            <Award className="w-4 h-4 text-gray-600" />
            <span className="text-gray-900">Total: {totalPoints} points</span>
          </div>
        </div>

        {/* Question List */}
        {questions.length > 0 && (
          <div className="space-y-4">
            {questions.map((question, index) => (
              <Card key={question.id} className="p-3 border-2 hover:border-blue-200 transition-colors">
                <div className="flex items-start gap-3">
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="min-w-10 justify-center">
                      Q{index + 1}
                    </Badge>
                  </div>

                  <div className="flex-1 space-y-4">
                    <div className="space-y-3">
                      <RichTextEditor
                        value={question.question}
                        onChange={(value) => updateQuestion(question.id, 'question', value)}
                        placeholder="Enter your question..."
                      />
                      
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                        <Select 
                          value={question.type}
                          onValueChange={(value) => updateQuestion(question.id, 'type', value)}
                        >
                          <SelectTrigger className="w-full sm:w-48 bg-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text">
                              <div className="flex items-center gap-2">
                                <FileText className="w-4 h-4" />
                                Text Answer
                              </div>
                            </SelectItem>
                            <SelectItem value="mcq">
                              <div className="flex items-center gap-2">
                                <ListChecks className="w-4 h-4" />
                                Multiple Choice
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>

                        <div className="flex items-center gap-3 w-full sm:w-auto">
                          <div className="flex items-center gap-1">
                            <Input
                              type="number"
                              value={question.points}
                              onChange={(e) => updateQuestion(question.id, 'points', parseInt(e.target.value) || 0)}
                              className='edu-form-field w-16'
                              min="0"
                            />
                            <span className="text-gray-600">pts</span>
                          </div>

                          {mode !== 'view' && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeQuestion(question.id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Options for MCQ and Checkbox */}
                    {(question.type === 'mcq' || question.type === 'checkbox') && question.options && (
                      <div className="pl-4 space-y-2">
                        <Label className="text-gray-700">Answer Options (Click to mark correct answer)</Label>
                        {question.options.map((option, optionIndex) => (
                          <div key={optionIndex} className="flex items-center gap-2">
                            <button
                              type="button"
                              onClick={() => updateQuestion(question.id, 'correctAnswer', optionIndex)}
                              className={`w-6 h-6 rounded-full border-2 flex items-center justify-center flex-shrink-0 transition-colors ${
                                question.correctAnswer === optionIndex 
                                  ? 'border-green-600 bg-green-600' 
                                  : 'border-gray-300 bg-white hover:border-green-400'
                              }`}
                            >
                              {question.correctAnswer === optionIndex && (
                                <Check className="w-4 h-4 text-white" />
                              )}
                            </button>
                            <span className="text-gray-500 w-6">{String.fromCharCode(65 + optionIndex)}.</span>
                            <Input
                              placeholder={`Option ${String.fromCharCode(65 + optionIndex)}`}
                              value={option}
                              onChange={(e) => updateOption(question.id, optionIndex, e.target.value)}
                             className='edu-form-field'
                
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Add Question Buttons */}
        <div className="flex flex-col sm:flex-row gap-2">
          <Button 
            variant="outline" 
            onClick={() => addQuestion('text')}
            className="flex items-center justify-center gap-2 w-full sm:w-auto"
          >
            <Plus className="w-4 h-4" />
            <FileText className="w-4 h-4" />
            Add Text Question
          </Button>
          <Button 
            variant="outline" 
            onClick={() => addQuestion('mcq')}
            className="flex items-center justify-center gap-2 w-full sm:w-auto"
          >
            <Plus className="w-4 h-4" />
            <ListChecks className="w-4 h-4" />
            Add Multiple Choice
          </Button>
        </div>
      </div>

      {/* Action Buttons */}
      {mode !== 'view' && (
      <div className="flex gap-3 pt-4">
        <Button 
          className="bg-purple-600 hover:bg-purple-700 ml-auto"
          onClick={() => {
            if (!title.trim()) {
              toast.error("Please enter an assignment title");
              return;
            }
            if (!date) {
              toast.error("Please select a due date");
              return;
            }
            if (questions.length === 0) {
              toast.error("Please add at least one question");
              return;
            }
            
            const invalidQuestions = questions.filter(q => !q.question.trim());
            if (invalidQuestions.length > 0) {
              toast.error("Please fill in all question texts");
              return;
            }
            
            const mcqWithoutOptions = questions.filter(q => 
              q.type === 'mcq' && (!q.options || q.options.some(opt => !opt.trim()))
            );
            if (mcqWithoutOptions.length > 0) {
              toast.error("Please fill in all options for multiple choice questions");
              return;
            }
            
            const mcqWithoutCorrectAnswer = questions.filter(q => 
              q.type === 'mcq' && q.correctAnswer === undefined
            );
            if (mcqWithoutCorrectAnswer.length > 0) {
              toast.error("Please select correct answers for all multiple choice questions");
              return;
            }
            
            if (onSubmit) {
              onSubmit({
                title,
                description,
                dueDate: date,
                questions: questions.map(q => ({
                  text: q.question,
                  type: q.type === 'mcq' ? 'mcq' : 'text',
                  options: q.options,
                  correctAnswers: q.correctAnswer !== undefined ? [q.correctAnswer] : []
                }))
              });
            }
          }}
        >
          {mode === 'edit' ? 'Update Assignment' : 'Create Assignment'}
        </Button>
      </div>
      )}
    </div>
  );
}