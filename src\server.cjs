const io = require("socket.io")(3001, { cors: { origin: "*" } });

console.log('Socket.IO server starting on port 3001...');

io.on("connection", (socket) => {
  const { classId } = socket.handshake.query;
  console.log(`New connection: ${socket.id}, classId: ${classId}`);
  
  if (classId) {
    socket.join(classId);
    console.log(`Socket ${socket.id} joined class ${classId}`);
    console.log(`Total clients in room ${classId}:`, io.sockets.adapter.rooms.get(classId)?.size || 0);
  }

  socket.on("draw-data", ({ classId, imageData }) => {
    console.log(`Received draw-data from ${socket.id} for class ${classId}`);
    const room = io.sockets.adapter.rooms.get(classId);
    console.log(`Room ${classId} has ${room?.size || 0} clients`);
    
    // Broadcast to all clients in the room except sender
    socket.to(classId).emit("draw-data", { imageData });
    console.log(`Broadcasted draw-data to class ${classId}`);
  });

  socket.on("clear-canvas", ({ classId }) => {
    console.log(`Received clear-canvas from ${socket.id} for class ${classId}`);
    socket.to(classId).emit("clear-canvas");
    console.log(`Broadcasted clear-canvas to class ${classId}`);
  });

  socket.on("disconnect", () => {
    console.log(`Socket ${socket.id} disconnected`);
  });
});

console.log('Socket.IO server ready on port 3001');