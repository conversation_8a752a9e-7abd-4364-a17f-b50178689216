
import React, { useState, useEffect, useRef } from "react";
import { useParams, useNavigate, Link, useSearchParams } from "react-router-dom";
import { useApp } from "@/context/AppContext";
import { useAuth } from  "react-oidc-context";
import { useIsMobile } from "@/hooks/use-mobile";
import DOMPurify from 'dompurify';

import { ArrowLeft, BookOpen, Calendar, Users, MessageSquare, DollarSign, FileText, User, Bell, Home, Share2, Copy, Mail, Smartphone, Edit, Check, X, Plus, Menu, MoreHorizontal, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { toast } from "sonner";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ClassType } from "@/types";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import AssignmentList from "@/components/assignment/AssignmentList";
import AssignmentForm from "@/components/assignment/AssignmentForm";
import { AddStudentForm } from "@/components/student/AddStudentForm";
import { SyllabusTopicForm } from "@/components/syllabus/SyllabusTopicForm";
import { StudentAttendanceTab } from "./StudentAttendanceTab";
import FeeStructureTable from "@/components/FeeStructureTable";
import { getAllSyllabus } from "@/services/syllabusService";
 import { generateAvatarUrl } from "@/lib/utils";
import { UserRole } from "@/types";
import { useSelector } from "react-redux";
import { useUserRole } from "@/hooks/useUserRole";
import { getClassesForStudent, ClassData ,getClassOverViewForStudent,getClassNotes } from "@/services/classService";
import StudentSyllabusTab from "./StudentSyllabusTab";
import StudentsAssignmentsTab from "./StudentsAssignmentsTab";
import StudentPaymentsTab from "./StudentPaymentsTab";
import StudentClassDetailsTab from "./StudentClassDetailsTab";
import StudentReviewForm from "./StudentReviewForm";
import StudentScheduleAllPage from "./StudentScheduleAllPage";
import{ LeaveRequestsList} from "@/components/leave/LeaveRequestsList";
import{ StudentLeaveForm} from "@/components/leave/StudentLeaveForm";
import Whiteboard from "@/components/Whiteboard";


const StudentClassManageDetailPage = () => {
  const {
    classId
  } = useParams<{
    classId: string;
  }>();
  const navigate = useNavigate();
  const {
    classes,
    shareClass,
    updateClass,
    assignments,
    createAssignment,
    submitAssignment
  } = useApp();
  const auth= useAuth();
  const [overView, setOverView] = useState<any>(null);
  const [overViewLoading, setOverViewLoading] = useState(false);
  const [classNotes, setClassNotes] = useState("");
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  
// Get user from OIDC
    const user = auth.isAuthenticated ? {
      id: auth.user?.profile.sub || "",
      name: auth.user?.profile.name || "User",
      email: auth.user?.profile.email || "",
      role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
      avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
    } : null;
  const { selectedRole } = useUserRole();
    const [availableClasses, setAvailableClasses] = useState<ClassData[]>([]);
  const hasFetched = useRef(false);

  useEffect(() => {
    if (hasFetched.current) return;
    
    const fetchClasses = async () => {
      hasFetched.current = true;
      try {
        const classes = await getClassesForStudent(auth.user.access_token);
       console.log("Fetched classes:", classes);
        setAvailableClasses(classes || []);
      } catch (error) {
        console.error("Error fetching classes:", error);
        setAvailableClasses([]);
      }
    };

    

fetchClassNotes();
    fetchClasses();
    fetchOverview();
  }, []);
  const currentClass = availableClasses.find(c => c.classroomId === classId);
  const isMobile = useIsMobile();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(() => {
    const tabParam = searchParams.get('tab');
    return tabParam || "overview";
  });

  // Refresh functions for tab content
  const fetchOverview = async () => {
    if (!auth.user?.access_token || !classId) return;
    setOverViewLoading(true);
    try {
      const data = await getClassOverViewForStudent(classId, auth.user.access_token);
      setOverView(data);
    } catch (error) {
      toast.error("Failed to load overview");
    } finally {
      setOverViewLoading(false);
    }
  };

  const fetchClassNotes = async () => {
    if (!auth.user?.access_token || !classId) return;
    try {
      const data = await getClassNotes(auth.user.access_token, classId);
      if (data && data.length > 0) {
        setClassNotes(data[0].content);
      }
    } catch (error) {
      console.error("Failed to load class notes:", error);
    }
  };

  // Refresh function for tab content
  const refreshTabContent = (tabName: string) => {
    switch (tabName) {
      case "overview":
        fetchOverview();
        fetchClassNotes();
        break;
      default:
        break;
    }
  };

  // Enhanced tab change handler
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    refreshTabContent(newTab);
    // Update URL without causing a page reload
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('tab', newTab);
    navigate(`/class/${classId}/student-manageclass?${newSearchParams.toString()}`, { replace: true });
  };

  // Handle URL parameter changes
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam && tabParam !== activeTab) {
      setActiveTab(tabParam);
      refreshTabContent(tabParam);
    }
  }, [searchParams]);
  const [feeFormData, setFeeFormData] = useState({
    fee: 0,
    paymentType: "monthly",
    description: "",
    feeStructures: []
  });
  const [editMode, setEditMode] = useState(false);
  const [syllabusContent, setSyllabusContent] = useState("<p>Welcome to the course! Here you can add your syllabus content.</p>");
  const [editedClass, setEditedClass] = useState({
    name: "",
    courseTitle: "",
    classType: ClassType.REGULAR
  });
  const [isLoading, setIsLoading] = useState(true);
  const classAssignments = assignments.filter(a => a.classroomId === classId);
  
  const mockStudents = [{
  }];


  React.useEffect(() => {
    if (editMode && currentClass) {
      setEditedClass({
        name: currentClass.className,
        courseTitle: currentClass.subjectName,
        classType: currentClass.classType as ClassType
      });
    }
  }, [editMode, currentClass]);

  React.useEffect(() => {
    if (currentClass) {
      setFeeFormData({
        fee:  0,
        paymentType: "monthly",
        description: "",
        feeStructures:  []
      });
    }
  }, [currentClass]);

  const handleGoBack = () => {
    navigate("/student-classes");
  };

  const handleShare = (method: "email" | "whatsapp" | "copy" | "other") => {
    if (currentClass) {
      shareClass(currentClass.classroomId, method);
    }
  };

  const handleCopyJoinCode = () => {
    if (currentClass) {
      navigator.clipboard.writeText(currentClass.joinCode);
      toast("Join code copied to clipboard!");
    }
  };

  const updateFeeFormData = (data: any) => {
    setFeeFormData(prev => ({
      ...prev,
      ...data
    }));
  };

  const handleEditToggle = () => {
    if (editMode) {
      if (updateClass && currentClass) {
        updateClass(currentClass.classroomId, editedClass);
        toast.success("Class details updated successfully!");
      }
    }
    setEditMode(!editMode);
  };

  const handleSyllabusChange = (content: string) => {
    setSyllabusContent(content);
    const saveTimeout = setTimeout(() => {
      toast.success("Syllabus content saved");
    }, 1000);
    return () => clearTimeout(saveTimeout);
  };

  const handleCreateAssignment = (assignmentData: any) => {
    createAssignment({
      ...assignmentData,
      submittedBy: []
    });
  };

  const handleSubmitAssignment = (assignmentId: string) => {
    if (user) {
      submitAssignment(assignmentId, user.id);
    }
  };

  const handleViewAssignmentDetails = (assignmentId: string) => {
    navigate(`/class/${classId}/assignments/${assignmentId}`);
  };

  const handleNotifyStudent = (studentId: number) => {
    console.log("Notifying student:", studentId);
    toast.success("Notification sent to student");
  };

  const handleChatWithStudent = (studentId: number) => {
    console.log("Opening chat with student:", studentId);
    // Implement chat functionality
  };

  const handleRemoveStudent = (studentId: number) => {
    console.log("Removing student:", studentId);
    toast.success("Student removed from class");
  };


  const MobileTabSelector = () => <div className="md:hidden w-full mb-4">
      <Select value={activeTab} onValueChange={handleTabChange}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select tab" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="overview">Overview</SelectItem>
          <SelectItem value="syllabus">Syllabus</SelectItem>
          <SelectItem value="attendance">Attendance</SelectItem>
          <SelectItem value="assignments">Assignments</SelectItem>
          <SelectItem value="payments">Payments</SelectItem>
          <SelectItem value="fees">Fee Plan</SelectItem>
          <SelectItem value="classdetails">Class Details</SelectItem>
          <SelectItem value="schedule">Schedule</SelectItem>
          <SelectItem value="reviews">Reviews</SelectItem>
          <SelectItem value="leaves">Leaves</SelectItem>
         <SelectItem value="whiteboard">White Board</SelectItem>



        </SelectContent>
      </Select>
    </div>;

  if (!hasFetched.current || availableClasses.length === 0) {
    return <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading class...</h1>
        </div>
      </div>;
  }

  if (!currentClass) {
    return <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Class not found</h1>
          <Button onClick={() => navigate('/student-dashboard')}>Return to Classes</Button>
        </div>
      </div>;
  }


  return <div className="min-h-screen bg-gray-50">
      <header className="bg-white p-4 border-b border-gray-200 sticky top-0 z-10">
        <div className="flex items-center max-w-screen-xl mx-auto">
          <Button variant="ghost" className="mr-2" onClick={handleGoBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="overflow-hidden">
            <h1 className="text-lg md:text-2xl font-medium truncate md:p-1">
              {isMobile ? currentClass.className : `${currentClass.className}`}
            </h1>
          </div>
          {/*<div className="ml-auto flex gap-2">
            {isMobile ? <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Menu className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleShare("email")}>
                    <Mail className="h-4 w-4 mr-2" />
                    Email
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleShare("whatsapp")}>
                    <Smartphone className="h-4 w-4 mr-2" />
                    WhatsApp
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleShare("copy")}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy link
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu> : <></>}
          </div>
       */}
        
       <div className="p-4 ml-auto">
        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
         {isMobile ? <></> : <span className="font-medium text-sm">Join Code:</span>}
          <div className="flex items-center gap-2">
            <code className="bg-white px-2 py-0.5 rounded border text-sm">{currentClass.joinCode}</code>
            <Button variant="ghost" size="icon" className="h-6 w-6" onClick={handleCopyJoinCode}>
              <Copy className="h-3 w-3" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleShare("email")}>
                  <Mail className="h-4 w-4 mr-2" />
                  <span>Email</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleShare("whatsapp")}>
                  <Smartphone className="h-4 w-4 mr-2" />
                  <span>WhatsApp</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleShare("copy")}>
                  <Copy className="h-4 w-4 mr-2" />
                  <span>Copy link</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
 </div>
      </header>


      <div className="p-4" >
              <div className="max-w-6xl mx-auto px-4">
                {isMobile ? <MobileTabSelector /> : <Tabs className="w-full" value={activeTab} onValueChange={handleTabChange} >
                  <div className="bg-white border-b border-gray-200 overflow-x-auto">
                    <div className="px-4 md:px-6">
                      <TabsList className="flex gap-1 min-w-max h-auto bg-transparent border-0 p-0">
                        <TabsTrigger value="overview" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          Overview
                        </TabsTrigger>
                        <TabsTrigger value="schedule" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          Schedule
                        </TabsTrigger>
                        <TabsTrigger value="reviews" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          Reviews
                        </TabsTrigger>
                        <TabsTrigger value="syllabus" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          Syllabus
                        </TabsTrigger>
                        <TabsTrigger value="attendance" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          Attendance
                        </TabsTrigger>
                        <TabsTrigger value="assignments" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          Assignments
                        </TabsTrigger>
                        <TabsTrigger value="payments" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          Payments
                        </TabsTrigger>
                        <TabsTrigger value="fees" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          Fee Plan
                        </TabsTrigger>
                        <TabsTrigger value="classdetails" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          Class Details
                        </TabsTrigger>
                        <TabsTrigger value="leaves" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          Leaves
                        </TabsTrigger>
                        <TabsTrigger value="whiteboard" className="px-4 py-3 text-nowrap relative transition-colors data-[state=active]:text-gray-900 text-gray-600 hover:text-gray-900 data-[state=active]:bg-transparent bg-transparent data-[state=active]:shadow-none shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none">
                          White Board
                        </TabsTrigger>
                      </TabsList>
                    </div>
                  </div>
              
              <TabsContent value="overview" className="mt-6 border-0 p-0">
                   <div className="space-y-6">
                                {/* Stats Cards */}
                                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                                  {/* Completion Status */}
                                  <Card>
                                    <CardContent className="p-6">
                                      <div className="text-center">
                                        <div className="relative w-20 h-20 mx-auto mb-4">
                                          <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                            <path
                                              d="M18 2.0845
                                                a 15.9155 15.9155 0 0 1 0 31.831
                                                a 15.9155 15.9155 0 0 1 0 -31.831"
                                              fill="none"
                                              stroke="hsl(var(--muted))"
                                              strokeWidth="2"
                                            />
                                            <path
                                              d="M18 2.0845
                                                a 15.9155 15.9155 0 0 1 0 31.831
                                                a 15.9155 15.9155 0 0 1 0 -31.831"
                                              fill="none"
                                              stroke="hsl(var(--primary))"
                                              strokeWidth="2"
                                              strokeDasharray={`${Math.round(overView?.completionStatus?.completionPercentage || 0)}, 100`}
                                            />
                                          </svg>
                                          <div className="absolute inset-0 flex items-center justify-center">
                                            <span className="text-2xl font-bold text-primary">{Math.round(overView?.completionStatus?.completionPercentage || 0)}%</span>
                                          </div>
                                        </div>
                                        <h3 className="font-semibold mb-1">Completion Status</h3>
                                        <div className="text-sm text-muted-foreground space-y-1">
                                          <div>{overView?.completionStatus?.completedSchedules || 0}/{overView?.completionStatus?.totalSchedules || 0} Completed</div>
                                          <div>{overView?.completionStatus?.pendingSchedules || 0} Pending</div>
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                  
                                  {/* Attendance */}
                                  <Card>
                                    <CardContent className="p-6">
                                      <div className="text-center">
                                        <div className="relative w-20 h-20 mx-auto mb-4">
                                          <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                            <path
                                              d="M18 2.0845
                                                a 15.9155 15.9155 0 0 1 0 31.831
                                                a 15.9155 15.9155 0 0 1 0 -31.831"
                                              fill="none"
                                              stroke="hsl(var(--muted))"
                                              strokeWidth="2"
                                            />
                                            <path
                                              d="M18 2.0845
                                                a 15.9155 15.9155 0 0 1 0 31.831
                                                a 15.9155 15.9155 0 0 1 0 -31.831"
                                              fill="none"
                                              stroke="hsl(var(--chart-2))"
                                              strokeWidth="2"
                                              strokeDasharray={`${Math.round(overView?.attendanceStats?.attendancePercentage || 0)}, 100`}
                                            />
                                          </svg>
                                          <div className="absolute inset-0 flex items-center justify-center">
                                            <span className="text-2xl font-bold text-orange-500">{Math.round(overView?.attendanceStats?.attendancePercentage || 0)}%</span>
                                          </div>
                                        </div>
                                        <h3 className="font-semibold mb-1">Attendance</h3>
                                        <div className="text-sm text-muted-foreground space-y-1">
                                          <div>{Math.round(overView?.attendanceStats?.attendancePercentage || 0)}% Present</div>
                                          <div>{overView?.attendanceStats?.classesAttended || 0}/{overView?.attendanceStats?.totalClassesHeld || 0} Classes</div>
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                  
                                  {/* Assignment */}
                                  <Card>
                                    <CardContent className="p-6">
                                      <div className="text-center">
                                        <div className="relative w-20 h-20 mx-auto mb-4">
                                          <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                            <path
                                              d="M18 2.0845
                                                a 15.9155 15.9155 0 0 1 0 31.831
                                                a 15.9155 15.9155 0 0 1 0 -31.831"
                                              fill="none"
                                              stroke="hsl(var(--muted))"
                                              strokeWidth="2"
                                            />
                                            <path
                                              d="M18 2.0845
                                                a 15.9155 15.9155 0 0 1 0 31.831
                                                a 15.9155 15.9155 0 0 1 0 -31.831"
                                              fill="none"
                                              stroke="hsl(var(--destructive))"
                                              strokeWidth="2"
                                              strokeDasharray="0, 100"
                                            />
                                          </svg>
                                          <div className="absolute inset-0 flex items-center justify-center">
                                            <span className="text-2xl font-bold text-red-500">0%</span>
                                          </div>
                                        </div>
                                        <h3 className="font-semibold mb-1">Assignment</h3>
                                        <div className="text-sm text-muted-foreground space-y-1">
                                          <div>0% Submitted</div>
                                          <div>0% Not Submit</div>
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                  
                                  {/* Payments */}
                                  <Card>
                                    <CardContent className="p-6">
                                      <div className="text-center">
                                        <div className="relative w-20 h-20 mx-auto mb-4">
                                          <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                            <path
                                              d="M18 2.0845
                                                a 15.9155 15.9155 0 0 1 0 31.831
                                                a 15.9155 15.9155 0 0 1 0 -31.831"
                                              fill="none"
                                              stroke="hsl(var(--muted))"
                                              strokeWidth="2"
                                            />
                                            <path
                                              d="M18 2.0845
                                                a 15.9155 15.9155 0 0 1 0 31.831
                                                a 15.9155 15.9155 0 0 1 0 -31.831"
                                              fill="none"
                                              stroke="hsl(var(--chart-3))"
                                              strokeWidth="2"
                                              strokeDasharray={`${Math.round(overView?.paymentStats?.paymentCompletionPercentage || 0)}, 100`}
                                            />
                                          </svg>
                                          <div className="absolute inset-0 flex items-center justify-center">
                                            <span className="text-2xl font-bold text-green-500">{Math.round(overView?.paymentStats?.paymentCompletionPercentage || 0)}%</span>
                                          </div>
                                        </div>
                                        <h3 className="font-semibold mb-1">Payments</h3>
                                        <div className="text-sm text-muted-foreground space-y-1">
                                          <div>{overView?.paymentStats?.paymentsCompleted || 0} Completed</div>
                                          <div>{overView?.paymentStats?.paymentsPending || 0} Pending</div>
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                </div>
                  
                                {/* Main Content Area */}
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                  {/* Class Notes - Left Side */}
                                  <div className="lg:col-span-2">
                                    <Card>
                                      <CardContent className="p-6">
                                        <div className="flex items-center justify-between mb-4">
                                          <h3 className="text-lg font-semibold">Class Notes</h3>
                                          
                                        </div>
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                                          <div className="flex items-center text-blue-800 text-sm">
                                            <Bell className="h-4 w-4 mr-2" />
                                            <span className="font-medium">Note: These notes are visible to all students in this class.</span>
                                          </div>
                                        </div>
                                        
                                           <div 
                    className="prose prose-sm max-w-none [&_ul]:list-disc [&_ul]:ml-6 [&_ol]:list-decimal [&_ol]:ml-6 [&_li]:mb-1"

                                dangerouslySetInnerHTML={{ 
                      __html:  classNotes
                    }}
                  />
                                        
                                      </CardContent>
                                    </Card>
                                  </div>
                  
                                  {/* Daily Activities - Right Side */}
                                  <div>
                                    <Card>
                                      <CardContent className="p-6">
                                        <h3 className="text-lg font-semibold mb-4">Daily Activities</h3>
                                        <div className="space-y-4">
                                          <div>
                                            <h4 className="font-medium mb-2">• Topic Name 1</h4>
                                            <p className="text-sm text-muted-foreground">
                                              Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text.
                                            </p>
                                          </div>
                                          <div>
                                            <h4 className="font-medium mb-2">• Topic Name 2</h4>
                                            <p className="text-sm text-muted-foreground">
                                              Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text.
                                            </p>
                                          </div>
                                          <div>
                                            <h4 className="font-medium mb-2">• Topic Name 3</h4>
                                            <p className="text-sm text-muted-foreground">
                                              Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text.
                                            </p>
                                          </div>
                                        </div>
                                      </CardContent>
                                    </Card>
                                  </div>
                                </div>
                              </div>
              </TabsContent>
              
                           
              <TabsContent value="syllabus" className="mt-6 border-0 p-0">
            <StudentSyllabusTab classId={classId} />

              </TabsContent>
              
              <TabsContent value="attendance" className="mt-6 border-0 p-0">
                <StudentAttendanceTab averageAttendance={92} classesThisMonth={8} studentsBelow75={3} onRecordAttendance={() => navigate(`/class/${classId}/attendance`)} />

                
              </TabsContent>
              
              <TabsContent value="assignments" className="mt-6 border-0 p-0">
                <StudentsAssignmentsTab classId={classId} onSelectAssignment={handleViewAssignmentDetails} assignments={classAssignments} handleSubmitAssignment={handleSubmitAssignment} onViewDetails={handleViewAssignmentDetails} onSubmit={handleSubmitAssignment} />
              </TabsContent>
              
              <TabsContent value="payments" className="mt-6 border-0 p-0">
                <StudentPaymentsTab classId={classId} />
              </TabsContent>
              
              <TabsContent value="fees" className="mt-6 border-0 p-0">
                <div>
             
                  {classId && <FeeStructureTable classId={classId} />}
                </div>
              </TabsContent>
              <TabsContent value="classdetails" className="mt-6 border-0 p-0">
                <StudentClassDetailsTab classId={classId} editedClass={currentClass}/>
              </TabsContent>
              <TabsContent value="schedule" className="mt-6 border-0 p-0">
                                <StudentScheduleAllPage classId={classId}/>

              </TabsContent>
               <TabsContent value="reviews" className="mt-6 border-0 p-0">
                  <StudentReviewForm 
                  teacherId={currentClass.teacherId} 
                  teacherName={currentClass.teacherName} 
                  classroomId ={classId}
                  courseName={currentClass.subjectName}
                  onSubmit={(data) => console.log('Review submitted:', data)}
                  onCancel={() => console.log('Review cancelled')}
                />
              </TabsContent>
              <TabsContent value = "leaves" className="mt-6 border-0 p-0">
                <div className="max-w-6xl mx-auto px-4 py-6">
                  <div>
                    <StudentLeaveForm 
                      classId={classId!} 
                      studentId={user?.id || ""} 
                      onLeaveSubmitted={() => setRefreshTrigger(prev => prev + 1)} 
                    />
                    <LeaveRequestsList 
                      classId={classId!} 
                      isTeacher={false} 
                      studentId={user?.id}
                      refreshTrigger={refreshTrigger}
                    />
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="whiteboard" className="mt-6 border-0 p-0">
                <div className="max-w-6xl mx-auto px-4 py-6">
                  <div>
<Whiteboard classId={classId} isTeacher={user.role === "TEACHER"} />                  </div>
                </div>
              </TabsContent>
            </Tabs>
          }
        </div>
        
        {isMobile && <div className="mt-6">
            {activeTab === "overview" &&
             <div className="space-y-6 mt-6">
                          {/* Stats Cards */}
                          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                            {/* Completion Status */}
                            <Card>
                              <CardContent className="p-6">
                                <div className="text-center">
                                  <div className="relative w-20 h-20 mx-auto mb-4">
                                    <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                      <path
                                        d="M18 2.0845
                                          a 15.9155 15.9155 0 0 1 0 31.831
                                          a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="hsl(var(--muted))"
                                        strokeWidth="2"
                                      />
                                      <path
                                        d="M18 2.0845
                                          a 15.9155 15.9155 0 0 1 0 31.831
                                          a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="hsl(var(--primary))"
                                        strokeWidth="2"
                                        strokeDasharray={`${Math.round(overView?.completionStatus?.completionPercentage || 0)}, 100`}
                                      />
                                    </svg>
                                    <div className="absolute inset-0 flex items-center justify-center">
                                      <span className="text-2xl font-bold text-primary">{Math.round(overView?.completionStatus?.completionPercentage || 0)}%</span>
                                    </div>
                                  </div>
                                  <h3 className="font-semibold mb-1">Completion Status</h3>
                                  <div className="text-sm text-muted-foreground space-y-1">
                                    <div>{overView?.completionStatus?.completedSchedules || 0}/{overView?.completionStatus?.totalSchedules || 0} Completed</div>
                                    <div>{overView?.completionStatus?.pendingSchedules || 0} Pending</div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
            
                            {/* Attendance */}
                            <Card>
                              <CardContent className="p-6">
                                <div className="text-center">
                                  <div className="relative w-20 h-20 mx-auto mb-4">
                                    <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                      <path
                                        d="M18 2.0845
                                          a 15.9155 15.9155 0 0 1 0 31.831
                                          a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="hsl(var(--muted))"
                                        strokeWidth="2"
                                      />
                                      <path
                                        d="M18 2.0845
                                          a 15.9155 15.9155 0 0 1 0 31.831
                                          a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="hsl(var(--chart-2))"
                                        strokeWidth="2"
                                        strokeDasharray={`${Math.round(overView?.attendanceStatistics?.overallPresencePercentage || 0)}, 100`}
                                      />
                                    </svg>
                                    <div className="absolute inset-0 flex items-center justify-center">
                                      <span className="text-2xl font-bold text-orange-500">{Math.round(overView?.attendanceStatistics?.overallPresencePercentage || 0)}%</span>
                                    </div>
                                  </div>
                                  <h3 className="font-semibold mb-1">Attendance</h3>
                                  <div className="text-sm text-muted-foreground space-y-1">
                                    <div>{Math.round(overView?.attendanceStatistics?.overallPresencePercentage || 0)}% Present</div>
                                    <div>{Math.round(overView?.attendanceStatistics?.overallAbsencePercentage || 100)}% Absent</div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
            
                            {/* Assignment */}
                            <Card>
                              <CardContent className="p-6">
                                <div className="text-center">
                                  <div className="relative w-20 h-20 mx-auto mb-4">
                                    <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                      <path
                                        d="M18 2.0845
                                          a 15.9155 15.9155 0 0 1 0 31.831
                                          a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="hsl(var(--muted))"
                                        strokeWidth="2"
                                      />
                                      <path
                                        d="M18 2.0845
                                          a 15.9155 15.9155 0 0 1 0 31.831
                                          a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="hsl(var(--destructive))"
                                        strokeWidth="2"
                                        strokeDasharray="0, 100"
                                      />
                                    </svg>
                                    <div className="absolute inset-0 flex items-center justify-center">
                                      <span className="text-2xl font-bold text-red-500">0%</span>
                                    </div>
                                  </div>
                                  <h3 className="font-semibold mb-1">Assignment</h3>
                                  <div className="text-sm text-muted-foreground space-y-1">
                                    <div>0% Submitted</div>
                                    <div>0% Not Submit</div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
            
                            {/* Payments */}
                            <Card>
                              <CardContent className="p-6">
                                <div className="text-center">
                                  <div className="relative w-20 h-20 mx-auto mb-4">
                                    <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                      <path
                                        d="M18 2.0845
                                          a 15.9155 15.9155 0 0 1 0 31.831
                                          a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="hsl(var(--muted))"
                                        strokeWidth="2"
                                      />
                                      <path
                                        d="M18 2.0845
                                          a 15.9155 15.9155 0 0 1 0 31.831
                                          a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="hsl(var(--chart-3))"
                                        strokeWidth="2"
                                        strokeDasharray={`${Math.round(overView?.paymentStatistics?.paymentCompletedPercentage || 0)}, 100`}
                                      />
                                    </svg>
                                    <div className="absolute inset-0 flex items-center justify-center">
                                      <span className="text-2xl font-bold text-green-500">{Math.round(overView?.paymentStatistics?.paymentCompletedPercentage || 0)}%</span>
                                    </div>
                                  </div>
                                  <h3 className="font-semibold mb-1">Payments</h3>
                                  <div className="text-sm text-muted-foreground space-y-1">
                                    <div>{overView?.paymentStatistics?.studentsWithCompletedPayment || 0} Paid</div>
                                    <div>{overView?.paymentStatistics?.studentsWithPendingPayment || 0} Pending</div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </div>
            
                          {/* Main Content Area */}
                          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            {/* Class Notes - Left Side */}
                            <div className="lg:col-span-2">
                              <Card>
                                <CardContent className="p-6">
                                  <div className="flex items-center justify-between mb-4">
                                    <h3 className="text-lg font-semibold">Class Notes</h3>
                                  
                                  </div>
                                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                                    <div className="flex items-center text-blue-800 text-sm">
                                      <Bell className="h-4 w-4 mr-2" />
                                      <span className="font-medium">Note: These notes are visible to all students in this class.</span>
                                    </div>
                                  </div>
                                       <div 
                    className="prose prose-sm max-w-none [&_ul]:list-disc [&_ul]:ml-6 [&_ol]:list-decimal [&_ol]:ml-6 [&_li]:mb-1"

                                dangerouslySetInnerHTML={{ 
                      __html:  classNotes
                    }}
                  />
                                  </CardContent>
                              </Card>
                            </div>
            
                            {/* Daily Activities - Right Side */}
                            <div>
                              <Card>
                                <CardContent className="p-6">
                                  <h3 className="text-lg font-semibold mb-4">Daily Activities</h3>
                                  <div className="space-y-4">
                                    <div>
                                      <h4 className="font-medium mb-2">• Topic Name 1</h4>
                                      <p className="text-sm text-muted-foreground">
                                        Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text.
                                      </p>
                                    </div>
                                    <div>
                                      <h4 className="font-medium mb-2">• Topic Name 2</h4>
                                      <p className="text-sm text-muted-foreground">
                                        Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text.
                                      </p>
                                    </div>
                                    <div>
                                      <h4 className="font-medium mb-2">• Topic Name 3</h4>
                                      <p className="text-sm text-muted-foreground">
                                        Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text. Lorem Ipsum is simply dummy text.
                                      </p>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            </div>
                          </div>
                        </div>
            }
            
            
            
            {activeTab === "syllabus" && 
            <div className="mt-6">
              <StudentSyllabusTab classId={classId} />
            </div>
                
                }
            
            {activeTab === "attendance" && 
                <div className="mt-6">
                  <StudentAttendanceTab averageAttendance={92} classesThisMonth={8} studentsBelow75={3} onRecordAttendance={() => navigate(`/class/${classId}/attendance`)} />
                </div>
            }
            
            {activeTab === "assignments" && <div className="mt-6">
                                <StudentsAssignmentsTab classId={classId} assignments={classAssignments} handleSubmitAssignment={handleSubmitAssignment} onViewDetails={handleViewAssignmentDetails} onSubmit={handleSubmitAssignment} onSelectAssignment={handleViewAssignmentDetails} />
                              </div>}
            
            {activeTab === "payments" &&
                            <div className="mt-6">
                              <StudentPaymentsTab  classId={classId} />
                            </div>

            }
            
            {activeTab === "fees" && <div className="mt-6">
               
                
                {classId && <FeeStructureTable classId={classId} />}
              </div>}
              {activeTab === "classdetails" && <div className="mt-6">  
                <StudentClassDetailsTab classId={classId} editedClass={currentClass}/>
              </div>}
              {activeTab === "schedule" && <div className="mt-6">
                <StudentScheduleAllPage classId={classId} />
                </div>} 
                {activeTab === "reviews" && <div className="mt-6">
                  <StudentReviewForm 
                  teacherId={currentClass.teacherId} 
                  teacherName={currentClass.teacherName} 
                  classroomId ={classId}
                  courseName={currentClass.subjectName}
                  onSubmit={(data) => console.log('Review submitted:', data)}
                  onCancel={() => console.log('Review cancelled')}
                />
                </div>} 
            {activeTab === "leaves" &&
              <div className="bg-gray-50 -m-4 md:-m-6 p-4 md:p-6 min-h-[400px]">
              <div className="space-y-6">
                    <StudentLeaveForm 
                      classId={classId!} 
                      studentId={user?.id || ""} 
                      onLeaveSubmitted={() => setRefreshTrigger(prev => prev + 1)} 
                    />
                    <LeaveRequestsList 
                      classId={classId!} 
                      isTeacher={false} 
                      studentId={user?.id}
                      refreshTrigger={refreshTrigger}
                    />
                              </div>
            </div>}  
            {activeTab === "whiteboard" &&
              <div className="mt-6">
<Whiteboard classId={classId} isTeacher={user.role === "TEACHER"} />              </div>
            }              
          </div>}
      </div>
    </div>;
};

export default StudentClassManageDetailPage;
