
import { useEffect } from 'react';
import { useAuth } from 'react-oidc-context';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { useUserRole } from "@/hooks/useUserRole";

export default function OAuthCallback() {
  const auth = useAuth();
  const navigate = useNavigate();
 const { selectedRole } = useUserRole();  

  useEffect(() => {
    if (auth.isAuthenticated) {
      toast.success('Successfully authenticated!');
      // Always redirect to role selection after successful login
      navigate('/role-selection');
    }
    
    if (auth.error) {
      toast.error(`Authentication error: ${auth.error.message}`);
      navigate('/login');
    }
  }, [auth.isAuthenticated, auth.error, auth.user, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-4">Processing your login...</h2>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-edu-blue mx-auto"></div>
      </div>
    </div>
  );
}
