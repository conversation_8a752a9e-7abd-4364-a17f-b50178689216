import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import Header from "@/components/layout/Header";
import Footer from "@/components/Footer"; // or your preferred toast library
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";    
import { Sparkles } from "lucide-react";
import { toast } from "sonner";
import { useState } from "react";
import { useAuth } from "react-oidc-context";

export default function TermsOfServicePage() {
  const auth = useAuth();
    const [isSubmitting, setIsSubmitting] = useState(false);

const handleSignUp  = async () => {
   // window.location.href = "https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/signup?client_id=76u1v7el416ebllhpbhtqpmlh0&code_challenge=Cjh7j5XvSKwPZ5ahIhP5j2tuEvZiuoSm811Q62N0wFs&code_challenge_method=S256&redirect_uri=http%3A%2F%2Flocalhost%3A8081%2Flogin%2Foauth2%2Fcode%2Fcognito&response_type=code&scope=email+openid+phone&state=80e73e7091c04c30a0c4904373b2096f";
     setIsSubmitting(true);
    
    try {
      // Store form values in localStorage to access after redirect back from Cognito
    //  localStorage.setItem("registerFormData", JSON.stringify(form.getValues()));
      
      // Redirect to Cognito signup page
      await auth.signinRedirect({ prompt: "login" });
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-white to-edu-light">
            <Header />
       {/* Hero Section with gradient background */}
        <section className="py-16 md:py-24 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-blue-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto relative z-10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
              <div className="max-w-2xl text-center md:text-left">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white leading-tight">
                  Transform <span className="text-yellow-300">Education</span> Through Innovation
                </h1>
                <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
                  Join thousands of educators and students in revolutionizing the learning experience
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <Button asChild size="lg" className="w-full sm:w-auto bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full shadow-lg">
                    <Link to="/register">Get Started Free</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="w-full sm:w-auto border-2 bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full">
                    <Link to="/subscription">View Pricing</Link>
                  </Button>
                </div>
                <div className="mt-12 flex flex-col sm:flex-row items-center justify-center md:justify-start gap-6">
                  <div className="flex -space-x-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="w-12 h-12 rounded-full border-4 border-purple-600 bg-white shadow-lg flex items-center justify-center text-purple-600 font-bold text-lg">
                        {String.fromCharCode(64 + i)}
                      </div>
                    ))}
                  </div>
                  <p className="text-lg text-white/90">
                    Joined by <span className="font-bold text-yellow-300">2000+</span> educators
                  </p>
                </div>
              </div>
              <div className="w-full md:w-2/5 relative">
                <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-transform duration-500">
                  <img 
                    src="https://images.unsplash.com/photo-1571260899304-425eee4c7efc?q=80&w=2070&auto=format&fit=crop" 
                    alt="EduConnect Platform" 
                    className="w-full h-64 md:h-96 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800">Modern Learning Experience</h3>
                    <p className="text-gray-600">Interactive tools for better engagement</p>
                  </div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-xl shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <p className="text-lg font-bold flex items-center gap-2">
                    <Sparkles className="h-5 w-5" />
                    AI-Powered Learning
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      <div className="max-w-4xl mx-auto p-4">
        <div className="mb-8 mt-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">Terms of Service</h1>
          <p className="text-muted-foreground">Last updated: {new Date().toLocaleDateString()}</p>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>1. Acceptance of Terms</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-neutral dark:prose-invert max-w-none">
              <p>
                By accessing and using our educational platform, you accept and agree to be bound by the terms 
                and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>2. Use License</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-neutral dark:prose-invert max-w-none">
              <p>
                Permission is granted to temporarily access the materials on our platform for personal, 
                non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:
              </p>
              <ul>
                <li>modify or copy the materials</li>
                <li>use the materials for any commercial purpose</li>
                <li>attempt to reverse engineer any software contained on the platform</li>
                <li>remove any copyright or other proprietary notations from the materials</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>3. Educational Content</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-neutral dark:prose-invert max-w-none">
              <p>
                All educational content, including but not limited to courses, assignments, and materials, 
                are provided for educational purposes only. We strive to ensure accuracy but make no warranties 
                about the completeness, reliability, and accuracy of this information.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>4. User Accounts</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-neutral dark:prose-invert max-w-none">
              <p>
                When you create an account with us, you must provide information that is accurate, complete, 
                and current at all times. You are responsible for safeguarding the password and for all activities 
                that occur under your account.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>5. Privacy Policy</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-neutral dark:prose-invert max-w-none">
              <p>
                Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect 
                your information when you use our service.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>6. Limitation of Liability</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-neutral dark:prose-invert max-w-none">
              <p>
                In no event shall our platform or its suppliers be liable for any damages (including, without limitation, 
                damages for loss of data or profit, or due to business interruption) arising out of the use or inability 
                to use the materials on our platform.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>7. Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-neutral dark:prose-invert max-w-none">
              <p>
                If you have any questions about these Terms of Service, please contact <NAME_EMAIL>.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
                 <footer className="bg-gray-900 text-white py-8 md:py-12 mt-8">
              <Footer/>
            </footer>
      
    </div>
    
  );
}