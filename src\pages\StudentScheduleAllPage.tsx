 
 import React, { useState, useEffect, useRef } from "react";
 import { useAuth } from  "react-oidc-context";
 import { useApp } from "@/context/AppContext";
 import { Link, useNavigate, useParams } from "react-router-dom";
import {
  Clock,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  Grid,
  List,
  Bell,
  User,
  CalendarIcon,
  Menu,
  X,
  FilterX,
  Edit,
  Trash2,
  Globe
} from "lucide-react";
 import { But<PERSON> } from "@/components/ui/button";
 import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
 import { Badge } from "@/components/ui/badge";
 import {
   Table,
   TableBody,
   TableCell,
   TableHead,
   TableHeader,
   TableRow
 } from "@/components/ui/table";
 import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
 } from "@/components/ui/dropdown-menu";
 import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
 } from "@/components/ui/select";
 import { Input } from "@/components/ui/input";
import { format, isAfter, isBefore, isSameDay, startOfDay, endOfDay, addDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isPast, addMonths, eachWeekOfInterval, eachDayOfInterval, isSameMonth } from "date-fns";
import { formatInTimeZone } from "date-fns-tz";
 import { ScheduleEvent } from "@/types";
 import { getSchedulesByClassId ,deleteScheduleEvent,updateScheduleEvent} from "@/services/scheduleService";
 import { useIsMobile } from "@/hooks/use-mobile";
 import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
 import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
 import { Calendar } from "@/components/ui/calendar";
 import { cn } from "@/lib/utils";
 import { UserRole } from "@/types";
 import { useSelector } from "react-redux";
 import { useUserRole } from "@/hooks/useUserRole";
 import { toast } from "sonner";
 import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
 import { Plus } from "lucide-react";
 import { createClassScheduleService, getTimeZones } from "@/services/scheduleService";
 import ClassSchedule from "@/components/class/ClassSchedule";
 import EditSchedule from "@/components/class/EditSchedule";
import { convertUTCToLocalTime } from "@/utils/convertFromUTC";

 import { getClassesFromBackend, ClassData } from "@/services/classService";

 export default function StudentScheduleAllPage({classId}) {
   const auth = useAuth();
   const  user  = useAuth().user;
   const { classes } = useApp();
   const navigate = useNavigate();
   const isMobile = useIsMobile();
   const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
   const [isEditMode, setIsEditMode] = useState(false);
   const [currentScheduleId, setCurrentScheduleId] = useState<string | null>(null);
   const [schedules, setSchedules] = useState([]);
   const [isLoading, setIsLoading] = useState(true);
   const [viewMode, setViewMode] = useState(isMobile ? "grid" : "list");
   const [searchQuery, setSearchQuery] = useState("");
   const [selectedClass, setSelectedClass] = useState<string>("all");
   const dateFilterOptions = new Map([["all", "All Dates"], ["today", "Today"], ["week", "Week"], ["month", "Month"]]);
   const { selectedRole,userTimezone } = useUserRole();
         const [classDetails, setClassDetails] = useState([]);
   const [availableTimeZones, setAvailableTimeZones] = useState([
    "UTC",
    "America/New_York",
    "America/Chicago",
    "America/Denver",
    "America/Los_Angeles",
    "Europe/London",
    "Europe/Paris",
    "Asia/Kolkata",
    "Asia/Shanghai",
    "Asia/Tokyo",
    "Australia/Sydney",
    "Asia/Dubai",
    "America/Toronto",
    "Europe/Berlin",
    "Asia/Singapore"
  ]);
  const [selectedTimezone, setSelectedTimezone] = useState(() => userTimezone || 'UTC');
  const [timezoneSearch, setTimezoneSearch] = useState("");
console.log(selectedTimezone)
  // Handle timezone change without saving to cookie
  const handleTimezoneChange = (value) => {
    setSelectedTimezone(value);
    // Force calendar to re-render with new timezone
    setSchedules([...schedules]);
  };

  // Filter timezones based on search
  const filteredTimeZones = availableTimeZones.filter(tz => 
    tz.toLowerCase().includes(timezoneSearch.toLowerCase())
  );

   const [dateFilter, setDateFilter] = useState<string>("all");
   const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
   const hasFetched = useRef(false);

   const fetchSchedules = async () => {
     if (hasFetched.current) return;
     hasFetched.current = true;
     try {
       setIsLoading(true);
       const data = await getSchedulesByClassId(user.access_token, classId);
       setSchedules(data || []);
       setIsLoading(false);
     } catch (error) {
       console.error("Error fetching schedules:", error);
       setIsLoading(false);
     }
   };
   
   useEffect(() => {
     fetchSchedules();
   }, []);

   // Force calendar re-render when timezone changes
   useEffect(() => {
     if (selectedDate) {
       setSelectedDate(new Date(selectedDate));
     }
   }, [selectedTimezone]);
  
  // Function to check if a schedule is expired
  const isScheduleExpired = (schedule) => {
    if (!schedule) return false;
    
    try {
      // Handle different schedule object structures
      if (schedule.date) {
        // This is a schedule from getSchedulesForDate
        const scheduleDate = new Date(schedule.date);
        const today = new Date();
        return isPast(scheduleDate) && !isSameDay(scheduleDate, today);
      } else if (schedule.startDate) {
        // This is a regular schedule object
        const scheduleDate = new Date(schedule.startDate);
        
        // If it's a single event, check if the date is in the past
        if (schedule.recurrenceType === "SINGLE") {
          // Create a date with the schedule's time
          const scheduleDateTime = new Date(scheduleDate);
          if (schedule.sessionEndTime) {
            const [hours, minutes] = schedule.sessionEndTime.split(':').map(Number);
            scheduleDateTime.setHours(hours, minutes, 0, 0);
          }
          return isPast(scheduleDateTime);
        } 
        // For recurring events, check if the end date is in the past
        else if (schedule.endDate) {
          const endDate = new Date(schedule.endDate);
          return isPast(endDate);
        }
      }
      
      return false;
    } catch (error) {
      console.error("Error checking if schedule is expired:", error);
      return false;
    }
  };

  // Function to check if schedule is within 10 minutes of start time
  const isWithin10Minutes = (schedule) => {
    if (!schedule || !schedule.startDate || !schedule.sessionStartTime) return false;
    
    try {
      const now = new Date();
      const scheduleDate = new Date(schedule.startDate);
      const [hours, minutes] = schedule.sessionStartTime.split(':').map(Number);
      const scheduleDateTime = new Date(scheduleDate);
      scheduleDateTime.setHours(hours, minutes, 0, 0);
      
      const timeDiff = scheduleDateTime.getTime() - now.getTime();
      const minutesDiff = timeDiff / (1000 * 60);
      
      // Return true if within 10 minutes (0 to 10 minutes before start)
      return minutesDiff >= 0 && minutesDiff <= 10;
    } catch (error) {
      console.error("Error checking if schedule is within 10 minutes:", error);
      return false;
    }
  };
   
   useEffect(() => {
     
     if (isMobile && viewMode === "list") {
       setViewMode("grid");
     }
   }, [isMobile]);
    
   // Get classes based on user role
   const userClasses = selectedRole === UserRole.TEACHER
     ? classes.filter(c => c.teacherId === user?.profile.sub)
     : selectedRole === UserRole.STUDENT
       ? classes.filter(c => c.students.includes(user?.profile.sub || ''))
       : [];
   const resetDateFilters = () => {
     setDateFilter("all");
     setSelectedDate(undefined);
   };

   const getSchedulesForDate = (date) => {
     if (!date) return [];
     
     return schedules.filter(schedule => {
       if (!schedule || !schedule.startDate) return false;
       
       const convertedDate = convertUTCToLocalTime(schedule.startDate, schedule.sessionStartTime, selectedTimezone).date;
       const selectedDateStr = format(date, 'yyyy-MM-dd');
       return convertedDate === selectedDateStr;
     }).map(schedule => ({
       id: schedule.id,
       title: schedule.subjectName || "Untitled Schedule",
       class:  schedule.classroomName || "Unknown class",
       time: `${convertUTCToLocalTime(schedule.startDate, schedule.sessionStartTime, selectedTimezone).time} - ${convertUTCToLocalTime(schedule.startDate, schedule.sessionEndTime, selectedTimezone).time}`,
       classroomId: schedule.classroomId,
       date: format(date, "yyyy-MM-dd"),
       status: schedule.status
     }));
   };
  
   
   const filteredSchedules = schedules.filter(schedule => {
     // Skip invalid schedules
     if (!schedule) return false;
     
     // For debugging
     if (!schedule.classroomName) {
       console.log("Schedule missing classroomName:", schedule);
       return true; // Include it anyway for debugging
     }
     
     // Text search filter - only apply if there's a search query
     const matchesSearch = !searchQuery || 
       (schedule.classroomName && schedule.classroomName.toLowerCase().includes(searchQuery.toLowerCase()));

     // Class filter - simplified to just check if it matches the selected class
     const matchesClass = selectedClass === "all" || schedule.classroomId === selectedClass;

     // Date filter
     let matchesDate = true;
     if (dateFilter !== "all") {
       const today = new Date();
       // Parse the schedule date from startDate property
       let scheduleDate;
       try {
         scheduleDate = schedule.startDate ? new Date(schedule.startDate) : null;
         if (!scheduleDate || isNaN(scheduleDate.getTime())) {
           return false; // Skip invalid dates
         }
       } catch (e) {
         return false; // Skip if date parsing fails
       }

       if (dateFilter === "today") {
         matchesDate = isSameDay(scheduleDate, today);
       } else if (dateFilter === "week") {
         const weekStart = startOfWeek(today);
         const weekEnd = endOfWeek(today);
         matchesDate = (isAfter(scheduleDate, weekStart) || isSameDay(scheduleDate, weekStart)) && 
                      (isBefore(scheduleDate, weekEnd) || isSameDay(scheduleDate, weekEnd));
       } else if (dateFilter === "month") {
         const monthStart = startOfMonth(today);
         const monthEnd = endOfMonth(today);
         matchesDate = (isAfter(scheduleDate, monthStart) || isSameDay(scheduleDate, monthStart)) && 
                      (isBefore(scheduleDate, monthEnd) || isSameDay(scheduleDate, monthEnd));
       } else if (dateFilter === "custom" && selectedDate) {
         matchesDate = isSameDay(scheduleDate, selectedDate);
       }
     }

     return matchesSearch && matchesClass && matchesDate;
   });
   // First filter out any schedules with null startTime to prevent errors
   const validSchedules = filteredSchedules.filter(schedule => schedule && schedule.startTime);
     
   // Then sort only the valid schedules
   const sortedSchedules = validSchedules.sort((a, b) => {
     return a.startTime.getTime() - b.startTime.getTime();
   });
  // Log the schedules to debug
   
  const handleBack = () => {
     if (selectedRole === UserRole.TEACHER) {
       navigate('/teacher-dashboard');
     } else if (selectedRole === UserRole.STUDENT) {
       navigate('/student-dashboard');
     } else {
       navigate('/');
     }
   };

   const DateFilterComponent = () => (

     <div className=" flex items-center gap-2">
       <Select  
           value={dateFilter}
           onValueChange={(value) => {
             setDateFilter(value);
             // Force re-render by updating state
             setSchedules([...schedules]);
           }}
         >
         <SelectTrigger className="edu-form-field">
           <SelectValue placeholder="Filter by Date" />
         </SelectTrigger>
         <SelectContent>
          {Array.from(dateFilterOptions.keys()).map((key) => (
             <SelectItem key={key} value={key}>
               {dateFilterOptions.get(key)}
             </SelectItem>
           ))}
         </SelectContent>
       </Select>
      
     </div>



   );

   const FilterComponent = () => (
     <div className="w-full">
       <Select
         value={selectedClass}
         onValueChange={setSelectedClass}
       >
         <SelectTrigger>
           <SelectValue placeholder="Filter by class" />
         </SelectTrigger>
         <SelectContent>
           <SelectItem value="all">All Classes</SelectItem>
           {userClasses.map(cls => (
             <SelectItem key={cls.id} value={cls.id}>
               {cls.className}
             </SelectItem>
           ))}
         </SelectContent>
       </Select>
     </div>
   );

   return (
     <div>
      {/* Schedule Statistics */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-3 mt-6">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Schedules</p>
                      <p className="text-2xl font-bold">{filteredSchedules.length}</p>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <CalendarIcon className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Completed</p>
                      <p className="text-2xl font-bold text-green-600">{filteredSchedules.filter(s => s.status === 'COMPLETED').length}</p>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                      <CalendarIcon className="h-5 w-5 text-green-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Pending</p>
                      <p className="text-2xl font-bold text-orange-600">{filteredSchedules.filter(s => s.status === 'SCHEDULED').length}</p>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center">
                      <CalendarIcon className="h-5 w-5 text-orange-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            {!isMobile ? (
                <div className="flex flex-row gap-4 items-center justify-between my-6">
                   <div className="flex items-center space-x-4">
                     <div className="w-48">
                       <DateFilterComponent />
                     </div>
                     <div className="flex items-center gap-2">
                       <Button
                         variant="outline"
                         size="icon"
                         onClick={() => setViewMode("list")}
                         className={viewMode === "list" ? "bg-purple-100 text-purple-600 border-purple-300" : ""}
                       >
                         <List className="h-4 w-4" />
                       </Button>
                       <Button
                         variant="outline"
                         size="icon"
                         onClick={() => setViewMode('calendar')}
                         className={viewMode === "calendar" ? "bg-purple-100 text-purple-600 border-purple-300" : ""}
                       >
                         <CalendarIcon className="h-4 w-4" />
                       </Button>
                     </div>
                   </div>
                   <div className="flex items-center gap-2">
                     <Select 
                       defaultValue="UTC" 
                       value={selectedTimezone}
                       onValueChange={handleTimezoneChange}
                       onOpenChange={(open) => !open && setTimezoneSearch("")}
                     >
                       <SelectTrigger className="edu-form-field w-[200px] h-10 text-xs">
                         <SelectValue placeholder="Select timezone" />
                       </SelectTrigger>
                       <SelectContent className="max-h-[200px] overflow-y-auto" align="end">
                         <div className="p-2" onClick={(e) => e.stopPropagation()}>
                           <Input
                             placeholder="Search timezone..."
                             value={timezoneSearch}
                             onChange={(e) => setTimezoneSearch(e.target.value)}
                             className="edu-form-field h-8 text-xs"
                             onClick={(e) => e.stopPropagation()}
                             onKeyDown={(e) => e.stopPropagation()}
                           />
                         </div>
                         {filteredTimeZones.map((tz) => (
                           <SelectItem key={tz} value={tz}>
                             {tz}
                           </SelectItem>
                         ))}
                       </SelectContent>
                     </Select>
                   </div>
                 </div>
               ) : (
                   <Sheet>
                     <SheetTrigger asChild>
                       <Button variant="outline" className="mb-3 w-full flex justify-between items-center">
                         <span>Filter Options</span>
                         <Filter className="h-4 w-4 ml-2" />
                       </Button>
                     </SheetTrigger>
                     <SheetContent side="bottom" className="h-[60vh]">
                       <div className="py-4 space-y-6">
                         <h3 className="text-lg font-medium mb-2">Filter Schedules</h3>
                         <div className="space-y-4">
                          {/* <div>
                             <h4 className="text-sm font-medium mb-2">By Class</h4>
                             <FilterComponent />
                           </div>*/}
                           <div>
                             <h4 className="text-sm font-medium mb-2">By Date</h4>
                             <DateFilterComponent />
                           </div>
                         </div>
                       </div>
                     </SheetContent>
                   </Sheet>
                 )}
               {isLoading ? (

                 <div className="text-center py-12">
                   <p className="text-gray-500">Loading schedules...</p>
                 </div>
               ) : filteredSchedules.length === 0 ? (
                 <div className="text-center py-12">
                   <CalendarIcon className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                   <h3 className="text-lg font-medium text-gray-600 mb-2">No Schedules Found</h3>
                   <p className="text-gray-500">
                     There are no scheduled events matching your criteria.
                   </p>
                 </div>
               ) : viewMode === "list" && !isMobile ? (
                 <div className="space-y-4">
                  {filteredSchedules.length === 0 ? (
                    <Card className="p-8 text-center">
                      <p className="text-gray-600">No sessions found.</p>
                    </Card>
                  ) : (
                    filteredSchedules.map((schedule) => {
                      const isPast = isBefore(new Date(schedule.startDate), new Date()) && !isSameDay(new Date(schedule.startDate), new Date());
                      const isToday = isSameDay(new Date(schedule.startDate), new Date());
                      const attendanceRate = schedule.totalStudents > 0
                        ? Math.round((schedule.attendees / schedule.totalStudents) * 100)
                        : 0;

                      return (
                        <Card key={schedule.id} className="p-4 md:p-6 hover:shadow-md transition-shadow">
                          <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                            <div className="flex-shrink-0">
                              <div className={`w-16 h-16 rounded-lg flex flex-col items-center justify-center ${
                                isToday 
                                  ? 'bg-purple-100 border-2 border-purple-600' 
                                 : isPast 
                                  ? 'bg-gray-100' 
                                  : 'bg-blue-50 border border-blue-200'
                              }`}>
                                <span className={`text-gray-900 ${isToday ? 'text-purple-700' : ''}`}>
                                  {format(new Date(schedule.startDate), 'MMM')}
                                </span>
                                <span className={`text-gray-900 ${isToday ? 'text-purple-700' : ''}`}>
                                  {format(new Date(schedule.startDate), 'd')}
                                </span>
                              </div>
                            </div>

                            <div className="flex-1 min-w-0">
                              <div className="flex flex-wrap items-start justify-between gap-2 mb-2">
                                <div>
                                  <h3 className="text-gray-900 mb-1">{schedule.classroomName}</h3>
                                  {schedule.description && (
                                    <p className="text-gray-600 mb-2">{schedule.description}</p>
                                  )}
                              </div>
                                <Badge
                                  className={
                                    schedule.status === 'COMPLETED'
                                      ? 'bg-green-100 text-green-800 border-green-200'
                                      : schedule.status === 'CANCELLED'
                                      ? 'bg-red-100 text-red-800 border-red-200'
                                      : 'bg-purple-100 text-purple-800 border-purple-200'
                                  }
                                >
                                  {schedule.status.charAt(0).toUpperCase() + schedule.status.slice(1)}
                                </Badge>
                              </div>

                              <div className="flex flex-wrap items-center gap-3 md:gap-4 text-gray-600 mb-3">
                                <div className="flex items-center gap-2">
                                  <Clock className="w-4 h-4" />
                                  <span>{convertUTCToLocalTime(schedule.startDate, schedule.sessionStartTime, selectedTimezone).time} - {convertUTCToLocalTime(schedule.startDate, schedule.sessionEndTime, selectedTimezone).time}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  {schedule.sessionType === 'online' ? (
                                    <>
                                      <Globe className="w-4 h-4" />
                                      <span>Online</span>
                                    </>
                                  ) : (
                                    <>
                                      <CalendarIcon className="w-4 h-4" />
                                      <span>{schedule.meetingLink || 'Classroom'}</span>
                                    </>
                                  )}
                                </div>
                                {schedule.status === 'COMPLETED' && schedule.attendees && (
                                  <div className="flex items-center gap-2">
                                    <span>
                                      {schedule.attendees}/{schedule.totalStudents || 0} attended ({attendanceRate}%)
                                    </span>
                                  </div>
                                )}
                              </div>

                              <div className="flex flex-wrap gap-2">
                                {schedule.status === 'SCHEDULED' && isWithin10Minutes(schedule) && (
                                  <Button
                                    size="sm"
                                    className="bg-purple-600 hover:bg-purple-700"
                                    onClick={() => navigate(`/classroom/${schedule.classroomId}`)}
                                  >
                                    <Globe className="w-4 h-4 mr-2" />
                                    Join Meeting
                                  </Button>
                                )}
                                {selectedRole === UserRole.TEACHER && schedule.status !== 'COMPLETED' && (
                                  <>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      className={`${isScheduleExpired(schedule) || schedule.status === 'COMPLETED' ? "text-gray-400 border-gray-400 cursor-not-allowed" : "text-purple-600 hover:text-purple-700 hover:bg-purple-50"}`}
                                      disabled={isScheduleExpired(schedule) || schedule.status === 'COMPLETED'}
                                      onClick={() => {
                                        // open edit dialog (prefill logic can be added where the dialog consumes currentScheduleId/isEditMode)
                                        setCurrentScheduleId(schedule.id);
                                        setIsEditMode(true);
                                        setIsScheduleDialogOpen(true);
                                      }}
                                    >
                                     <Edit className="w-4 h-4 mr-2" />
                                     Edit
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      className={`text-red-600 hover:text-red-700 hover:bg-red-50 ${schedule.status === 'COMPLETED' ? "text-gray-400 border-gray-400 cursor-not-allowed" : ""}`}
                                      disabled={schedule.status === 'COMPLETED'}
                                     onClick={async () => {
                                        if (schedule.status === 'COMPLETED') return;
                                       if (confirm("Are you sure you want to delete this schedule?")) {
                                          try {
                                            await deleteScheduleEvent(schedule.id, auth.user.access_token);
                                            toast.success("Schedule deleted successfully!");
                                            fetchSchedules();
                                          } catch (error) {
                                            toast.error("Failed to delete schedule");
                                           console.error("Error deleting schedule:", error);
                                          }
                                        }
                                      }}
                                    >
                                      <Trash2 className="w-4 h-4 mr-2" />
                                      Delete
                                    </Button>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        </Card>
                      );
                    })
                  )}
               </div>
               ) : viewMode === "calendar" ? (
                 <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                   <div className="lg:col-span-2">
                     <Card className="p-4 md:p-6">
                       {/* Calendar Header */}
                       <div className="flex items-center justify-between mb-6">
                         <h3 className="text-gray-900">{format(selectedDate, 'MMMM yyyy')}</h3>
                         <div className="flex gap-2">
                           <Button
                             variant="outline"
                             size="sm"
                             onClick={() => setSelectedDate(addMonths(selectedDate, -1))}
                           >
                             <ChevronLeft className="w-4 h-4" />
                           </Button>
                           <Button
                             variant="outline"
                             size="sm"
                             onClick={() => setSelectedDate(new Date())}
                           >
                             Today
                           </Button>
                           <Button
                             variant="outline"
                             size="sm"
                             onClick={() => setSelectedDate(addMonths(selectedDate, 1))}
                           >
                             <ChevronRight className="w-4 h-4" />
                           </Button>
                         </div>
                       </div>

                       {/* Calendar Grid */}
                       <div className="border rounded-lg overflow-hidden">
                         {/* Weekday Headers */}
                         <div className="grid grid-cols-7 bg-gray-50 border-b">
                           {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                             <div key={day} className="p-2 text-center text-gray-600 border-r last:border-r-0">
                               {day}
                             </div>
                           ))}
                         </div>

                         {/* Calendar Days */}
                         {eachWeekOfInterval({
                           start: startOfMonth(selectedDate),
                           end: endOfMonth(selectedDate)
                         }).map((week, weekIdx) => (
                           <div key={weekIdx} className="grid grid-cols-7">
                             {eachDayOfInterval({
                               start: startOfWeek(week),
                               end: endOfWeek(week)
                             }).map((day, dayIdx) => {
                               const daySchedules = getSchedulesForDate(day);
                               const isCurrentMonth = isSameMonth(day, selectedDate);
                               const isToday = isSameDay(day, new Date());
                               
                               return (
                                 <div
                                   key={dayIdx}
                                   className={`min-h-[100px] p-2 border-r border-b last:border-r-0 ${
                                     !isCurrentMonth ? 'bg-gray-50' : 'bg-white'
                                   } ${isToday ? 'bg-purple-50' : ''}`}
                                 >
                                   <div className={`mb-2 ${
                                     isToday 
                                       ? 'w-6 h-6 rounded-full bg-purple-600 text-white flex items-center justify-center text-center' 
                                       : isCurrentMonth 
                                       ? 'text-gray-900' 
                                       : 'text-gray-400'
                                   }`}>
                                     {format(day, 'd')}
                                   </div>
                                   
                                   <div className="space-y-1">
                                     {daySchedules.map((schedule) => {
                                       const isPast = isBefore(new Date(schedule.date), new Date()) && !isSameDay(new Date(schedule.date), new Date());
                                       
                                       return (
                                         <div
                                           key={schedule.id}
                                           className={`p-1.5 rounded text-white cursor-pointer hover:opacity-80 transition-opacity ${
                                             schedule.status === 'completed'
                                               ? 'bg-green-600'
                                               : schedule.status === 'cancelled'
                                               ? 'bg-red-600'
                                               : isPast
                                               ? 'bg-gray-500'
                                               : 'bg-purple-600'
                                           }`}
                                           onClick={() => {
                                             if (!isPast && selectedRole === UserRole.TEACHER) {
                                               setIsEditMode(true);
                                               setCurrentScheduleId(schedule.id);
                                               setIsScheduleDialogOpen(true);
                                             }
                                           }}
                                         >
                                           <p className="truncate mb-0.5">{schedule.class}</p>
                                           <p className="flex items-center gap-1">
                                             <Clock className="w-3 h-3" />
                                             <span>{schedule.time}</span>
                                           </p>
                                         </div>
                                       );
                                     })}
                                   </div>
                                 </div>
                               );
                             })}
                           </div>
                         ))}
                       </div>

                       {/* Legend */}
                       <div className="flex flex-wrap gap-4 mt-4 text-gray-600">
                         <div className="flex items-center gap-2">
                           <div className="w-4 h-4 rounded bg-purple-600"></div>
                           <span>Scheduled</span>
                         </div>
                         <div className="flex items-center gap-2">
                           <div className="w-4 h-4 rounded bg-green-600"></div>
                           <span>Completed</span>
                         </div>
                         <div className="flex items-center gap-2">
                           <div className="w-4 h-4 rounded bg-red-600"></div>
                           <span>Cancelled</span>
                         </div>
                       </div>
                     </Card>
                   </div>
         
                 </div>
               ) : (
                 <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                   {filteredSchedules.sort((a, b) => {
                     const dateA = new Date(`${a.startDate}T${a.sessionStartTime}`);
                     const dateB = new Date(`${b.startDate}T${b.sessionStartTime}`);
                     return dateA.getTime() - dateB.getTime();
                   }).map(schedule => {
                     return (
                       <div key={schedule.id} className={`bg-white border border-gray-200 rounded-lg overflow-hidden ${isScheduleExpired(schedule) || schedule.status === 'COMPLETED' ? "text-gray-400 opacity-60" : ""}`}>
                         <div className="p-4">
                           <div className="flex items-center mb-2 text-xs text-gray-500">
                             <span>
                              {schedule.startDate ? convertUTCToLocalTime(schedule.startDate,schedule.sessionStartTime, selectedTimezone).date : "N/A" }
                              {/* Display status next to date */}
                              <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                                schedule.status === "COMPLETED" ? "bg-green-100 text-green-700" :
                                schedule.status === "CANCELLED" ? "bg-red-100 text-red-700" :
                                schedule.status === "IN_PROGRESS" ? "bg-blue-100 text-blue-700" :
                                "bg-orange-100 text-orange-700"
                              }`}>
                                {schedule.status}
                              </span>
                              </span>
                           </div>
                           <h3 className="font-medium mb-4">{schedule.classroomName}</h3>
                           <div className="flex items-center text-xs text-gray-500 mb-3">
                             <CalendarIcon className="h-3 w-3 mr-1" />
                             <span>{convertUTCToLocalTime(schedule.startDate,schedule.sessionStartTime, selectedTimezone).time || "N/A"}</span>
                             <span className="mx-1">•</span>
                             <span>{convertUTCToLocalTime(schedule.startDate,schedule.sessionStartTime, selectedTimezone).time}-{convertUTCToLocalTime(schedule.startDate,schedule.sessionEndTime, selectedTimezone).time}</span>
                             {selectedTimezone !== "UTC" && <span className="text-xs text-gray-400 ml-1">({selectedTimezone.split('/').pop()})</span>}
                           </div>
                           <div className="flex justify-between items-center">
                             {/*{schedule.recurrenceType =="SINGLE" ? (
                                 <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                                  single
                                 </Badge>
                               ) : (
                                 <Badge variant="outline">Yes</Badge>
                               )}*/}
                             <div className="flex-grow"></div>
                             <div className="flex items-center space-x-2">
                               {isWithin10Minutes(schedule) && !isScheduleExpired(schedule) && schedule.status !== 'COMPLETED' && (
                                 <Button
                                   size="sm"
                                   className="bg-purple-600 hover:bg-purple-700 text-xs py-1"
                                   onClick={() => navigate(`/classroom/${schedule.classroomId}`)}
                                 >
                                   Join Now
                                 </Button>
                               )}
                             </div>
                           </div>
                         </div>
                       </div>
                     );
                   })}

                 </div>
               )}

     </div>
       
   );
 }


