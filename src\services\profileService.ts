
import profileData from '../data/profile.json';

interface Profile {
  id: string;
  name: string;
  avatar: string;
  role: string;
  employeeId: string;
}

interface PersonalInfo {
  phone: string;
  gender: string;
  dob: string;
  socialMediaLink: string;
}

interface Address {
  country: string;
  zipCode: string;
  state: string;
  city: string;
  address: string;
}

interface ProfileData {
  profile: Profile;
  personalInfo: PersonalInfo;
  address: Address;
}
export const createProfile = async (profileData: any, accessToken: string): Promise<Profile> => {
  const response = await fetch('/api/profileManagement/v1/profile', {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(profileData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create/update profile");
  }
  return response.json();
};
export const updateProfile = async (profileData: any, accessToken: string ,id: string): Promise<Profile> => {
  const response = await fetch(`/api/userManagement/v1/users/profile`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(profileData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create/update profile");
  }
  return response.json();
};

export const getProfile = async (accessToken: string) => {
  const response = await fetch('/api/userManagement/v1/me', {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch profile");
  }
  return response.json();
};




export const getAllAddresses = async (accessToken: string): Promise<Address[]> => {
  const response = await fetch('/api/addressManagement/v1/addresses', {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch class by id");
  }
  return response.json();
};

export const addAddress = async (address: Address, accessToken: string): Promise<Address> => {
  const response = await fetch('/api/addressManagement/v1/addresses', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(address),
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to add address");
  }
  return response.json();
};

export const updateAddress = async (address: Address, accessToken: string,addressId :string): Promise<Address> => {
  const response = await fetch(`/api/addressManagement/v1/addresses/${addressId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(address),
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update address");
  }
  return response.json();
};
export const deleteAddress = async (accessToken: string, addId: string) => {
  const response = await fetch(`/api/addressManagement/v1/addresses/${addId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to delete certificate");
  }
  return response.ok;
};

export const getAllCertificates= async (accessToken: string) => {
  const response = await fetch('/api/profileManagement/v1/certification', {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch class by id");
  }
  return response.json();
};


export const addCertificate = async (certificate , accessToken: string) => {
  const response = await fetch('api/profileManagement/v1/certification', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(certificate),
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to add address");
  }
  return response.json();
};
export const updateCertificate = async (certificate, accessToken: string,certid :string) => {
  const response = await fetch(`/api/profileManagement/v1/certification/${certid}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(certificate),
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update address");
  }
  return response.json();
};

export const deleteCertificate = async (accessToken: string, certId: string) => {
  const response = await fetch(`/api/profileManagement/v1/certification/${certId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to delete certificate");
  }
  return response.ok;
};

export const getSkills = async (accessToken: string)=> {
  const response = await fetch('/api/profileManagement/v1/skill', {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch skills ");
  }
  return response.json();
};

export const addSkill = async (skill , accessToken: string) => {
  const response = await fetch('/api/profileManagement/v1/skill', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(skill),
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to add skill");
  }
  return response.json();
};

export const updateSkill = async (skill, accessToken: string,skillid :string) => {
  const response = await fetch(`/api/profileManagement/v1/skill/${skillid}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(skill),
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update skill");
  }
  return response.json();
};

export const deleteSkill = async (accessToken: string, skillId: string) => {
  const response = await fetch(`/api/profileManagement/v1/skill/${skillId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to delete Skill");
  }
  return response.ok;
};

export const getEducation = async (accessToken: string)=> {
  const response = await fetch('/api/profileManagement/v1/education' , {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch skills ");
  }
  return response.json();
};


export const addEducation = async (education , accessToken: string) => {
  const response = await fetch('/api/profileManagement/v1/education', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(education),
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to add Education");
  }
  return response.json();
};

export const updateEducation = async (education, accessToken: string,eduid :string) => {
  const response = await fetch(`/api/profileManagement/v1/education/${eduid}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(education),
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update Education ");
  }
  return response.json();
};

export const deleteEducation = async (accessToken: string, eduId: string) => {
  const response = await fetch(`/api/profileManagement/v1/education/${eduId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to delete Education");
  }
  return response.ok;
};

export const getAllExperience= async (accessToken: string) => {
  const response = await fetch('/api/workExperience/v1/work-experience', {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetchExperience");
  }
  return response.json();
};

export const addExperience = async (experience , accessToken: string) => {
  const response = await fetch('/api/workExperience/v1/work-experience', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(experience),
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to add Experience");
  }
  return response.json();
};

export const updateExperience = async (experience, accessToken: string,workExperienceId :string) => {
  const response = await fetch(`/api/workExperience/v1/work-experience/${workExperienceId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(experience),
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update Experience ");
  }
  return response.json();
};

export const deleteExperience = async (accessToken: string, workExperienceId: string) => {
  const response = await fetch(`/api/workExperience/v1/work-experience/${workExperienceId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to delete Education");
  }
  return response.ok;
};
