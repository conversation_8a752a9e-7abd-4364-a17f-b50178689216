
import { Amplify } from 'aws-amplify';
import { signIn as amplifySignIn, signUp as amplifySignUp, signOut as amplifySignOut, getCurrentUser as amplifyGetCurrentUser } from 'aws-amplify/auth';
import awsExports from '../aws-exports';
import { User, UserRole } from '@/types';
import { generateAvatarUrl } from '@/lib/utils';

// Configure Amplify
Amplify.configure(awsExports);

// Mock data for development environment
const MOCK_USERS = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: UserRole.TEACHER,
    avatar: "https://ui-avatars.com/api/?name=John+Teacher&background=3498db&color=fff"
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: UserRole.STUDENT,
    avatar: "https://ui-avatars.com/api/?name=<PERSON>+Student&background=f39c12&color=fff"
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: UserRole.PARENT,
    avatar: "https://ui-avatars.com/api/?name=Bob+Parent&background=27ae60&color=fff"
  }
];

// Determine if we're in a production environment
const isProduction = import.meta.env.VITE_APP_STAGE === 'production';

/**
 * Sign in a user with email and password
 */
export const signIn = async (email: string, password: string): Promise<{ user: User }> => {
  try {
    if (isProduction) {
      // Use actual Cognito authentication in production
      const signInOutput = await amplifySignIn({ username: email, password });
      
      // Get user information - in Amplify v6, we need to make another call to get the user details
      const currentUser = await amplifyGetCurrentUser();
      
      // Create our user object from the Cognito data
      // Using optional chaining and correcting property access pattern
      // In Amplify v6, the structure has changed - we need to adjust accordingly
      const name = currentUser.userId || 'User'; // Fallback to userId or 'User'
      const userEmail = email; // Default to the provided email
      let role = UserRole.STUDENT; // Default role
      let picture;
      
      // Access custom attributes if available
      if (currentUser.username) {
        // Try to get attributes through fetchUserAttributes if needed in production
        // For now use basic info
      }
      
      const user: User = {
        id: currentUser.userId,
        name: name,
        email: userEmail,
        role: role,
        avatar: picture || generateAvatarUrl(name, '3498db')
      };
      
      return { user };
    } else {
      // Use mock authentication in development
      const foundUser = MOCK_USERS.find(u => u.email === email);
      
      if (foundUser && password === "password") {
        return { user: foundUser };
      } else {
        throw new Error("Invalid email or password");
      }
    }
  } catch (error) {
    throw error;
  }
};

/**
 * Register a new user
 */
export const signUp = async (name: string, email: string, password: string, role: UserRole): Promise<void> => {
  try {
    if (isProduction) {
      // Use actual Cognito registration in production
      await amplifySignUp({
        username: email,
        password,
        options: {
          userAttributes: {
            email,
            name,
            'custom:role': role
          }
        }
      });
    } else {
      // Use mock registration in development
      if (MOCK_USERS.some(u => u.email === email)) {
        throw new Error("Email already in use");
      }
      
      const avatarUrl = generateAvatarUrl(name, "3498db");
      
      // Add to mock users (in a real app this wouldn't persist after refresh)
      MOCK_USERS.push({
        id: String(MOCK_USERS.length + 1),
        name,
        email,
        role,
        avatar: avatarUrl
      });
    }
  } catch (error) {
    throw error;
  }
};

/**
 * Sign out the current user
 */
export const signOut = async (): Promise<void> => {
  if (isProduction) {
    // Use actual Cognito sign out in production
    await amplifySignOut();
  }
  // For development, we'll just let the AuthContext clear local state
};

/**
 * Get the currently authenticated user
 */
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    if (isProduction) {
      // Get actual Cognito user in production
      const currentUser = await amplifyGetCurrentUser();
      
      // Using proper property access with defaults
      // In Amplify v6, the structure has changed - we need to adjust accordingly
      const name = currentUser.userId || 'User'; // Fallback to userId or 'User'
      const userEmail = currentUser.username || '';
      let role = UserRole.STUDENT; // Default role
      let picture;
      
      return {
        id: currentUser.userId,
        name: name,
        email: userEmail,
        role: role,
        avatar: picture || generateAvatarUrl(name, '3498db')
      };
    } else {
      // For development, we'll just return null and let the AuthContext handle saved user
      return null;
    }
  } catch (error) {
    // User is not signed in
    return null;
  }
};
