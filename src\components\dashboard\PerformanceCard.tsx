
import React from "react";
import { <PERSON><PERSON>ef<PERSON>, ArrowRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { User } from "@/types";

interface PerformanceCardProps {
  user: User | null;
}

export const PerformanceCard: React.FC<PerformanceCardProps> = ({ user }) => {
  return (
    <Card className="bg-gray-800 text-white">
      <CardContent className="p-4 sm:p-6">
        <h2 className="text-base sm:text-lg font-medium mb-4">Lecture Performance</h2>
        <div className="flex items-center mb-4">
          <div className="h-12 w-12 sm:h-16 sm:w-16 rounded-full bg-white/10 p-1 mr-4">
            <img 
              src={user?.avatar} 
              alt={user?.name}
              className="h-full w-full object-cover rounded-full"
            />
          </div>
          <div>
            <div className="text-2xl sm:text-4xl font-bold">91.2%</div>
            <div className="text-xs sm:text-sm text-gray-300">Overall Performance Score</div>
          </div>
        </div>

        <div className="mb-5">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xl sm:text-2xl font-bold">86%</span>
            <div className="flex">
              <ArrowLeft className="h-5 w-5 sm:h-6 sm:w-6 p-1 bg-gray-700 rounded-full mr-1" />
              <ArrowRight className="h-5 w-5 sm:h-6 sm:w-6 p-1 bg-gray-700 rounded-full" />
            </div>
          </div>
          <p className="text-xs sm:text-sm">Your Lesson Planning Score increased by 25% from the last month. Pretty good performance!</p>
        </div>
      </CardContent>
    </Card>
  );
};
