 
 import React, { useEffect, useState, useRef } from "react";
 import { Card, CardContent } from "@/components/ui/card";
 import { Button } from "@/components/ui/button";
 import { ArrowLeft, BookOpen, Calendar, Users, MessageSquare, DollarSign, FileText, User, Bell, Home, Share2, Copy, Mail, Smartphone, Edit, Check, X, Plus, Menu, MoreHorizontal, Trash2 } from "lucide-react";
 import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
 import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
 import { Input } from "@/components/ui/input";
 import { useAuth } from "react-oidc-context";
 import { useSelector } from "react-redux";
 import { getCompAttenforStudent } from "@/services/attendanceService";


 interface StudentAttendanceTabProps {
   averageAttendance: string | number;
   classesThisMonth: number;
   studentsBelow75: number;
   onRecordAttendance: () => void;
 }

 export const StudentAttendanceTab = ({
   averageAttendance,
   classesThisMonth,
   studentsBelow75,
   onRecordAttendance
 }: StudentAttendanceTabProps) => {
   const { user } = useAuth();
   const userData = useSelector((state: any) => state.user.userData);
   const [attendanceReport, setAttendanceReport] = useState(null);
   const [isLoading, setIsLoading] = useState(true);
   const [startDate, setStartDate] = useState(new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]);
   const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);

   const hasFetched = useRef(false);

   useEffect(() => {
     if (hasFetched.current) return;
     
     const fetchAttendanceReport = async () => {
       hasFetched.current = true;
       try {
         setIsLoading(true);
         const data = await getCompAttenforStudent(user?.access_token, userData.id, startDate, endDate);
         setAttendanceReport(data);
       } catch (error) {
         console.error('Error fetching attendance report:', error);
       } finally {
         setIsLoading(false);
       }
     };

     if (user?.access_token && userData?.id) {
       fetchAttendanceReport();
     }
   }, [user?.access_token, userData?.id, startDate, endDate]);
      

   return (
     <div className="space-y-6">
       <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
         <div>
           <label className="block text-sm font-medium mb-1">Start Date</label>
           <Input className="edu-form-field" type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} />
         </div>
         <div>
           <label className="block text-sm font-medium mb-1">End Date</label>
           <Input className="edu-form-field" type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} />
         </div>
       </div>
       
       {isLoading && (
         <div className="text-center py-8">
           <p>Loading attendance data...</p>
         </div>
       )}

       {!isLoading && (
         <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
           <Card>
             <CardContent className="pt-6">
               <div className="text-center">
                 <div className="text-4xl font-bold text-green-500 mb-2">{attendanceReport?.overallAttendanceRate || averageAttendance}%</div>
                 <p className="text-sm text-gray-600">Average Attendance</p>
               </div>
             </CardContent>
           </Card>
           <Card>
             <CardContent className="pt-6">
               <div className="text-center">
                 <div className="text-4xl font-bold text-blue-500 mb-2">{attendanceReport?.totalClassesScheduled || classesThisMonth}</div>
                 <p className="text-sm text-gray-600">Classes This Month</p>
               </div>
             </CardContent>
           </Card>
           <Card>
             <CardContent className="pt-6">
               <div className="text-center">
                 <div className="text-4xl font-bold text-purple-500 mb-2">{studentsBelow75}</div>
                 <p className="text-sm text-gray-600">Students Below 75%</p>
               </div>
             </CardContent>
           </Card>
         </div>
       )}
         {!isLoading && (
           <Card className="mt-6">
             <CardContent className="p-4 md:p-6">
               <h3 className="text-lg font-medium mb-4">Recent Attendance Records</h3>
           
               <Table>
                 <TableHeader>
                   <TableRow>
                     <TableHead>Date</TableHead>
                     <TableHead>Present</TableHead>
                     <TableHead>Absent</TableHead>
                     <TableHead>Late</TableHead>
                     <TableHead>Percentage</TableHead>
                   </TableRow>
                 </TableHeader>
                 <TableBody>
                {attendanceReport?.classroomSummaries?.map((classroom) => (
                    <TableRow key={classroom.classroomId}>
                       <TableCell>{classroom.lastAttendanceDate}</TableCell>
                       <TableCell className="text-green-600">{classroom.presentCount}</TableCell>
                       <TableCell className="text-red-600">{classroom.absentCount}</TableCell>
                       <TableCell className="text-amber-600">{classroom.lateCount}</TableCell>
                       <TableCell>{classroom.attendanceRate}%</TableCell>
                     </TableRow>
                   )) || (
                     <TableRow>
                       <TableCell colSpan={5} className="text-center text-gray-500">No attendance data available</TableCell>
                     </TableRow>
                   )}
                 </TableBody>
               </Table>
             </CardContent>
           </Card>
         )}
      
     </div>
   );
 };
