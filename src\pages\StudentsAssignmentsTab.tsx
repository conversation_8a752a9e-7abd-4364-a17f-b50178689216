import { useState, useEffect } from "react";
import AssignmentList from "@/components/assignment/AssignmentList";
import React from "react";
import { Assignment } from "@/types";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useAuth } from "react-oidc-context"; // Updated import
import { UserRole } from "@/types";
import { format } from "date-fns";
import { Edit, FileCheck, FileClock, Trash2, ArrowLeft,
  Award,Users,
  Clock,CheckCircle2, Calendar, AlertCircle, TrendingUp } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { generateAvatarUrl } from "@/lib/utils";
import { useUserRole } from "@/hooks/useUserRole";
import { getAssignmentsForStudent } from "@/services/assignmentService";
import { toast } from "sonner";

export default function StudentsAssignmentsTab({
  classId, assignments, handleSubmitAssignment, onViewDetails, onSubmit, onSelectAssignment}){
const auth = useAuth();
const navigate = useNavigate();
  
  // Get user from OIDC
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) || UserRole.STUDENT,
    avatar: generateAvatarUrl(auth.user?.profile.name || "User", "3498db")
  } : null;
 const { selectedRole } = useUserRole();  
  const [fetchedAssignments, setFetchedAssignments] = useState<Assignment[]>([]);
  const [assignmentsLoading, setAssignmentsLoading] = useState(false);

  useEffect(() => {
  const fetchAssignments = async () => {
    console.log("fetchAssignments triggered", { token: !!auth?.user?.access_token, classId });
    if (!auth?.user?.access_token || !classId) return;
    
    // Clear existing assignments first
    setFetchedAssignments([]);
    setAssignmentsLoading(true);
    
    try {
      // call student assignments service to display assignments for students
      const data = await getAssignmentsForStudent(auth.user.access_token,classId);

      const list = Array.isArray(data)
        ? data
        : (data?.content || data?.assignments || []);
      // optional: keep only assignments for this classroom/classId
      const filtered = list.filter((a: any) => a.classroomId === classId);
      setFetchedAssignments(filtered);
      console.log("fetched assignments:", filtered);
    } catch (err) {
      console.error("Failed to load assignments:", err);
      toast.error("Failed to load assignments");
      setFetchedAssignments([]); // Ensure empty state on error
    } finally {
      setAssignmentsLoading(false);
    }
  };

  fetchAssignments();
}, [auth?.user?.access_token, classId]);
// ...existing code...
  const sortedAssignments = [...assignments].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const isOverdue = (dueDate: Date) => {
    return dueDate < new Date();
  };

  const hasSubmitted = (assignment: Assignment) => {
    return user && assignment.submittedBy && assignment.submittedBy.includes(user.id);
  };

  if (fetchedAssignments.length === 0) {
    return (
      <div className="p-12 flex flex-col items-center justify-center text-center">
        <div className="rounded-full bg-gray-100 p-4 mb-4">
          <FileCheck className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium mb-1">No assignments yet</h3>
       
      </div>
    );
  }

  const getTimeRemaining = (dueDate: Date) => {
    const now = new Date();
    const diff = new Date(dueDate).getTime() - now.getTime();
    const days = Math.ceil(diff / (1000 * 60 * 60 * 24));
    return { days, urgent: days <= 3 && days > 0 };
  };

  const getSubmissionProgress = (assignment: Assignment) => {
    const totalStudents = 30; // You might want to get this from props or context
    const submitted = (assignment.submittedBy || []).length;
    return Math.round((submitted / totalStudents) * 100);
  };
 const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'submitted': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'graded': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <AlertCircle className="w-4 h-4" />;
      case 'submitted': return <Clock className="w-4 h-4" />;
      case 'graded': return <CheckCircle2 className="w-4 h-4" />;
      default: return null;
    }
  };

  // Calculate assignment statuses based on the actual data structure
  const pendingAssignments = fetchedAssignments.filter(a => {
    // An assignment is pending if it's published and active
    return a.status === 'PUBLISHED' && a.isSubmitted === false;
  });
  
  const submittedAssignments = fetchedAssignments.filter(a => {
    // Assignments that are submitted by the current user
    return a.isSubmitted === true;
  });
  
  const completedAssignments = fetchedAssignments.filter(a => {
    // Completed means graded submissions exist
    return a.isSubmitted === true;
  });
  // Function to strip HTML tags
  const stripHtmlTags = (html: string): string => {
    return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();
  };

  // Handle assignment selection - navigate to SubmitAssignment page
  const handleSelectAssignment = (assignment: Assignment) => {
    navigate(`/class/${classId}/submit-assignments/${assignment.id}`, {
      state: { isSubmitted: assignment.isSubmitted }
    });
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-gray-900 mb-2">My Assignments</h1>
        <p className="text-gray-600">View and complete your assignments</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card className="p-6 border-2 border-yellow-200 bg-yellow-50/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 mb-1">Pending</p>
              <p className="text-gray-900">{pendingAssignments.length} Assignments</p>
            </div>
            <div className="w-12 h-12 rounded-full bg-yellow-200 flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-yellow-700" />
            </div>
          </div>
        </Card>

        <Card className="p-6 border-2 border-blue-200 bg-blue-50/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 mb-1">Submitted</p>
              <p className="text-gray-900">{submittedAssignments.length} Assignments</p>
            </div>
            <div className="w-12 h-12 rounded-full bg-blue-200 flex items-center justify-center">
              <Clock className="w-6 h-6 text-blue-700" />
            </div>
          </div>
        </Card>

        <Card className="p-6 border-2 border-green-200 bg-green-50/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 mb-1">Completed</p>
              <p className="text-gray-900">{completedAssignments.length} Assignments</p>
            </div>
            <div className="w-12 h-12 rounded-full bg-green-200 flex items-center justify-center">
              <CheckCircle2 className="w-6 h-6 text-green-700" />
            </div>
          </div>
        </Card>
      </div>

      {/* Pending Assignments */}
      {pendingAssignments.length > 0 && (
        <div className="mb-8">
          <h2 className="text-gray-900 mb-4 font-bold">Pending Assignments</h2>
          <div className="space-y-4">
            {pendingAssignments.map((assignment) => (
              <Card 
                key={assignment.id} 
                className={`p-6 hover:shadow-lg transition-shadow cursor-pointer border-2 ${
                  isOverdue(assignment.dueDate) 
                    ? 'border-red-200 bg-red-50/30' 
                    : 'border-gray-200 hover:border-blue-300'
                }`}
                onClick={() => handleSelectAssignment(assignment)}
              >
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-start gap-3 mb-2">
                      <h3 className="text-gray-900 flex-1">{assignment.title}</h3>
                      <Badge className={getStatusColor(assignment.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(assignment.status)}
                          {assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1)}
                        </div>
                      </Badge>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{stripHtmlTags(assignment.description)}</p>
                    
                    <div className="flex flex-wrap items-center gap-4 text-gray-600">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        <span className={`whitespace-nowrap ${isOverdue(new Date(assignment.dueDate)) ? 'text-red-600' : ''}`}>
                          Due {format(new Date(assignment.dueDate), 'MMM d, yyyy')}
                        </span>
                        {isOverdue(new Date(assignment.dueDate)) && (
                          <Badge variant="destructive">Overdue</Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Award className="w-4 h-4" />
                        <span>{assignment.totalMarks} points</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span>{assignment.totalQuestions || assignment.questions?.length || 0} questions</span>
                      </div>
                    </div>
                  </div>
                  
                  {!assignment.isSubmitted && (
                    <Button 
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 w-full sm:w-auto flex-shrink-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSelectAssignment(assignment);
                      }}
                    >
                      Start Assignment
                    </Button>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Completed/Submitted Assignments */}
      {completedAssignments.length > 0 && (
        <div>
          <h2 className="text-gray-900 mb-4 font-bold">Submitted & Completed</h2>
          <div className="space-y-4">
            {completedAssignments.map((assignment) => (
              <Card 
                key={assignment.id} 
                className="p-6 hover:shadow-lg transition-shadow cursor-pointer border-2 border-gray-200 hover:border-blue-300"
                onClick={() => handleSelectAssignment(assignment)}
              >
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-start gap-3 mb-2">
                      <h3 className="text-gray-900 flex-1">{assignment.title}</h3>
                      <Badge className={getStatusColor("submitted")}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon("submitted")}
                          {"SUBMITTED"}
                        </div>
                      </Badge>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{stripHtmlTags(assignment.description)}</p>
                    
                    <div className="flex flex-wrap items-center gap-4 text-gray-600">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        <span>Due {format(new Date(assignment.dueDate), 'MMM d, yyyy')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Award className="w-4 h-4" />
                        {assignment.totalSubmissions > 0 ? (
                          <span className="text-green-600">
                            Score: {assignment.totalMarks || 0}/{assignment.totalMarks}
                          </span>
                        ) : (
                          <span>{assignment.totalMarks} points</span>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <span>{assignment.totalQuestions || assignment.questions?.length || 0} questions</span>
                      </div>
                    </div>
                  </div>
                  
                  <Button 
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectAssignment(assignment);
                    }}
                  >
                    View Details
                  </Button>
                </div>
                </Card>
              
            ))}
          </div>
          </div>
       
      )}
    </div>
  );  
}