export const createMembershipRequest = async (accessToken: string,selectedFeatures,billingCycle) => {
  
  const response = await fetch('/api/membership/subscribe', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' ,
         "Authorization": `Bearer ${accessToken}`
   
    },
    body: JSON.stringify({featureIds: Array.from(selectedFeatures || []),
      billingCycle}),
  });
  
if (response.status === 401) {
    window.location.href = "/";
    //return;
  }
  if (!response.ok) {
    throw new Error("Failed to create Membership");
  }  return response.json();
};
export const cancelMembership = async (accessToken: string) => {
  const response = await fetch('/api/membership/cancel', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' ,
         "Authorization": `Bearer ${accessToken}`
   
    },
    body: JSON.stringify({}),
  });
  
if (response.status === 401) {
    window.location.href = "/";
    //return;
  }
  if (!response.ok) {
    throw new Error("Failed to create Leaves");
  }  return response.json();
};
export const myMembership = async (accessToken: string) => {
  const response = await fetch(`/api/membership/my-membership`,{
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
});
  if (!response.ok) {
    throw new Error("Failed to fetch membership");
  }
  return response.json();
};



export const getFeatures = async () => {
  const response = await fetch(`/api/membership/features`,{
    method: "GET",
    headers: {
      "Content-Type": "application/json"
    }
});
  if (!response.ok) {
    throw new Error("Failed to fetch membership");
  }
  return response.json();
};
