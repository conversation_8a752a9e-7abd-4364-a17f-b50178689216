import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useAuth } from 'react-oidc-context';
import { setUserProfile } from '@/redux/userSlice';

export const useUserProfile = () => {
  const dispatch = useDispatch();
  const auth = useAuth();
  const hasFetched = useRef(false);
  
  const { userProfile, isProfileLoaded } = useSelector((state: any) => ({
    userProfile: state.user.userProfile,
    isProfileLoaded: state.user.isProfileLoaded
  }));

  useEffect(() => {
    if (hasFetched.current || !auth.isAuthenticated || !auth.user || isProfileLoaded) return;
    
    hasFetched.current = true;
    
    fetch("/api/userManagement/v1/me", {
      headers: {
        'Authorization': `Bearer ${auth.user.access_token}`
      }
    })
    .then((response) => {
      if (response.status === 500) {
        return null;
      }
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.json();
    })
    .then((result) => {
      if (result) {
        dispatch(setUserProfile(result));
      }
    })
    .catch((error) => {
      console.error("Error fetching user profile:", error);
    });
  }, [auth.isAuthenticated, auth.user, dispatch, isProfileLoaded]);

  return {
    userProfile,
    isProfileLoaded,
    // Helper functions to access common profile data
    userName: userProfile?.name || '',
    userEmail: userProfile?.email || '',
    userTimezone: userProfile?.timezone || 'UTC',
    userRoles: userProfile?.roles || [],
    userId: userProfile?.id || null
  };
};