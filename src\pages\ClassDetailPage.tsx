
import React, { useState ,useEffect, useRef, useCallback} from "react";
import { useParams, useNavigate, Link, useSearchParams } from "react-router-dom";
import { useApp } from "@/context/AppContext";
import { useAuth } from "react-oidc-context"; // Updated import
import { useIsMobile } from "@/hooks/use-mobile";
import { ArrowLeft, BookOpen, Calendar, Users,Upload, Image as ImageIcon, MessageSquare, CheckCircle2,DollarSign, FileText, User, Bell, Home, Share2, Copy, Mail, Smartphone, Edit, Check, X, Plus, Menu, MoreHorizontal, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { toast } from "sonner";
import { getPaymentStatistics ,getPaymentHistory } from "@/services/paymentService";
import AddPaymentForm from "@/components/AddPaymentForm";
import RecordAttendance from './RecordAttendance';
 import { getSchedulesByClassId} from "@/services/scheduleService";
import DOMPurify from 'dompurify';

import ScheduleTab from './ScheduleTab';
import { format,isPast } from "date-fns";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { AddStudentForm } from "@/components/student/AddStudentForm";
import { SyllabusTopicList, type SyllabusTopic } from "@/components/syllabus/SyllabusTopicList";
import { SyllabusTopicForm } from "@/components/syllabus/SyllabusTopicForm";
import { AttendanceSummary } from "@/components/attendance/AttendanceSummary";
import FeeStructureTable from "@/components/FeeStructureTable";
import { getAllSyllabus } from "@/services/syllabusService";
import { useSelector } from "react-redux";
import { Student, getEnrolledStudentsForClass, getStudents ,unEnrollStudentfrmClassroom,reEnrollStudentToClassroom} from "@/services/studentService";
import { getClassesFromBackend, ClassData  ,updateClassOnBackend,
  getClassOverViewForTeacher,createClassNotes,getClassNotes,updateClassNotes,MarkClassAsComplete
} from "@/services/classService";
import FeeForm from "@/components/class/FeeForm";
import { addSyllabusToClassroom } from "@/services/syllabusService";
import { set } from "date-fns";
import { Description } from "@radix-ui/react-dialog";
import Schedule from "@/components/class/ClassSchedule";
import { createClassScheduleService } from "@/services/scheduleService";
import { getAttendanceStatsSummary } from "@/services/attendanceService";
import { string } from "zod";
import { getAttendanceRecordsForRange } from "@/services/attendanceService"; // Make sure this service exists
import { subMonths ,isSameDay} from "date-fns"; // Add this import
import { differenceInYears, parseISO, isValid } from "date-fns"; // Add this import at the top
import { startOfMonth } from "date-fns";
import { truncate } from "fs";
import { useUserRole } from "@/hooks/useUserRole";
import{ LeaveRequestsList} from "@/components/leave/LeaveRequestsList";
import{ StudentLeaveForm} from "@/components/leave/StudentLeaveForm";
import AITopicForm from "@/components/AITopicForm";
import { convertUTCToLocalTime  } from "@/utils/convertFromUTC";
import { time } from "console";
import Whiteboard from "@/components/Whiteboard";

import SyllabusTab from "@/pages/SyllabusTab";
import OverviewTab from "@/pages/OverviewTab";
import AttendanceTab from "@/pages/AttendanceTab";
import EnrollmentTab from "@/pages/EnrollmentTab";
import AssignmentsTab from "@/pages/AssignmentsTab";
import PaymentsTab from "@/pages/PaymentsTab";
import ClassDetailsTab from "@/pages/ClassDetailsTab";
type TabKey = 'overview' | 'enrollments' | 'schedule' | 'syllabus' | 'attendance' | 'assignments' | 'payments' | 'feePlan' | 'classDetails' | 'leaves' | 'whiteBoard';

interface Tab {
  key: TabKey;
  label: string;
  component: JSX.Element;
}
const ClassDetailPage = () => {

  
  const [classNotes, setClassNotes] = useState("Welcome to our class! Here are some important notes for students...");
  const [classNotesId , setClassNotesId] = useState("");
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [notesExist, setNotesExist] = useState(false);
  const auth = useAuth();
 const {
    classId
  } = useParams<{
    classId: string;
  }>();
const { selectedRole ,userTimezone} = useUserRole();
    const [timezone] = useState(() => userTimezone || 'UTC');

  const [paymentHistory, setPaymentHistory] = useState<any[]>([]);
const [paymentHistoryLoading, setPaymentHistoryLoading] = useState(false);
const [paymentStartDate, setPaymentStartDate] = useState<Date>(startOfMonth(new Date()));
const [paymentEndDate, setPaymentEndDate] = useState<Date>(new Date());
const [selectedScheduleDate, setSelectedScheduleDate] = useState<string | null>(null);
  
const [isAddPaymentOpen, setIsAddPaymentOpen] = useState(false);
  const [isCreateAssignmentOpen, setIsCreateAssignmentOpen] = useState(false);
  const [isEditAssignmentOpen, setIsEditAssignmentOpen] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit' | 'view'>('create');
  const userData = useSelector((state: any) => state.user.userData);
 
const [paymentStats, setPaymentStats] = useState<any>(null);
const [paymentStatsLoading, setPaymentStatsLoading] = useState(false);

const navigate = useNavigate();
  const {
    classes,
    shareClass,
    updateClass,
    assignments,
    submitAssignment
  } = useApp();
  const user = auth.user;
  const [showCompleteDialog, setShowCompleteDialog] = useState(false);
  const isMobile = useIsMobile();
  const [searchParams] = useSearchParams();

const [activeTab, setActiveTab] = useState<TabKey>('overview');


  // Refresh function for tab content
  const [tabsRefreshKey, setTabsRefreshKey] = useState(0);
  const refreshTabContent = (tabName?: string) => {
    // bump refresh key to remount tab components and trigger their effects
    setTabsRefreshKey(k => k + 1);
    // allow callers to request specific fetches if needed
    switch (tabName) {
     case "schedule":
        fetchSchedules();
        break;
      default:
        break;
    }
  };

  // Enhanced tab change handler
  const handleTabChange = (newTab: TabKey) => {
    setActiveTab(newTab);
    refreshTabContent(newTab as string);
    // Update URL without causing a page reload
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('tab', newTab);
    navigate(`/class/${classId}?${newSearchParams.toString()}`, { replace: true });
    };

  // Handle URL parameter changes
  useEffect(() => {
    const tabParam = searchParams.get('tab') as TabKey | null;
    if (tabParam && tabParam !== activeTab) {
      setActiveTab(tabParam);
      refreshTabContent(tabParam as string);
    }
  }, [searchParams]);
  const [feeFormData, setFeeFormData] = useState({
    completed: false,
        feeAmount: 0,
    paymentType: "monthly",
    description: "",
    feeStructures: [],
    countryCode: "IN" // Default country code
  });
  const [syllabusContent, setSyllabusContent] = useState("<p>Welcome to the course! Here you can add your syllabus content.</p>");
 
  
const [schedulesRecordsLoading ,setSchedulesRecordsLoading] = useState(false);
  const [syllabusTopics, setSyllabusTopics] = useState<SyllabusTopic[]>();
  const [isLoading, setIsLoading] = useState(true);
  const [isAITopicOpen, setIsAITopicOpen] = useState(false);
  const [isAddTopicOpen, setIsAddTopicOpen] = useState(false);
  const [activeSyllabusTab, setActiveSyllabusTab] = useState("add");
    const [availableClasses, setAvailableClasses] = useState<ClassData[]>([]);
  const hasFetchedClasses = useRef(false);
  const hasFetchedSyllabus = useRef(false);
  const currentClass = availableClasses.find(c => c.id === classId);
  const [isCompleted, setIsCompleted] = useState(() =>  currentClass?.completed || false);

// Update isCompleted if currentClass.completed changes (e.g., after refresh)
useEffect(() => {
  setIsCompleted( currentClass?.completed || false);
}, [currentClass?.completed]);
  
  
  useEffect(() => {
    const fetchClasses = async () => {
      try {
        const classes = await getClassesFromBackend(auth.user.access_token);
        setAvailableClasses(classes.content || []);
      } catch (error) {
        console.error("Error fetching classes:", error);
        setAvailableClasses([]);
      }
    };

    if (auth.user?.access_token && !hasFetchedClasses.current) {
      hasFetchedClasses.current = true;
    
      fetchClasses();
    }
  }, [auth.user?.access_token]);
  const [schedules, setSchedules] = useState<any[]>([]);
     
   const hasFetched = useRef(false);

  const fetchSchedules = async () => {
       try {
         setSchedulesRecordsLoading(true);
         const data = await getSchedulesByClassId(user.access_token, classId);
         setSchedules(data || []);
         console.log("Schedules:", data);
         setSchedulesRecordsLoading(false);
       } catch (error) {
         console.error("Error fetching schedules:", error);
         setSchedulesRecordsLoading(false);
       }
     };
   useEffect(() => {
     if (user?.access_token && classId && !hasFetched.current) {
       hasFetched.current = true;
       fetchSchedules();
     }
   }, [user?.access_token, classId]);
   
    
  React.useEffect(() => {
    if (currentClass) {
      setFeeFormData({
        feeAmount:  0,
        paymentType: "monthly",
        description: "",
        feeStructures: [],
        countryCode : "IN", // Default country code
        completed: !!currentClass.completed // Add completed property
      });
    }
  }, [currentClass]);
  const updateFeeFormData = (data: any) => {
    setFeeFormData(prev => ({
      ...prev,
      ...data
    }));
  };
    
  const tabs = [
    { key: 'overview' as TabKey, label: 'Overview', render: () => <OverviewTab key={`${tabsRefreshKey}-overview`} classId={classId} isCompleted={isCompleted} currentClass={currentClass} auth={auth} onComplete={() => refreshTabContent()} /> },
    { key: 'enrollments' as TabKey, label: 'Enrollments', render: () => <EnrollmentTab key={`${tabsRefreshKey}-enrollments`} classId={classId} auth={auth} currentClass={currentClass} refreshTrigger={tabsRefreshKey} /> },
    { key: 'schedule' as TabKey, label: 'Schedule', render: () => <ScheduleTab key={`${tabsRefreshKey}-schedule`} classId={classId} auth={auth} currentClass={currentClass} schedules={schedules} fetchSchedules={fetchSchedules} refreshTrigger={tabsRefreshKey} /> },
    { key: 'syllabus' as TabKey, label: 'Syllabus', render: () => <SyllabusTab key={`${tabsRefreshKey}-syllabus`} currentClass={currentClass} auth={auth} refreshTrigger={tabsRefreshKey} /> },
    { key: 'attendance' as TabKey, label: 'Attendance', render: () => <AttendanceTab key={`${tabsRefreshKey}-attendance`} classId={classId} auth={auth} timezone={timezone} currentClass={currentClass} selectedScheduleDate={selectedScheduleDate} setSelectedScheduleDate={setSelectedScheduleDate} refreshTrigger={tabsRefreshKey} /> },
    { key: 'assignments' as TabKey, label: 'Assignments', render: () => <AssignmentsTab key={`${tabsRefreshKey}-assignments`} auth={auth} classId={classId} currentClass={currentClass} refreshTrigger={tabsRefreshKey} /> },
    { key: 'payments' as TabKey, label: 'Payments', render: () => <PaymentsTab key={`${tabsRefreshKey}-payments`} classId={classId} auth={auth} currentClass={currentClass} refreshTrigger={tabsRefreshKey} /> },
    { key: 'feePlan' as TabKey, label: 'Fee Plan', render: () => <div key={`${tabsRefreshKey}-feePlan`} className="max-w-6xl mx-auto "><FeeForm feeformData={feeFormData} updateFormData={updateFeeFormData} classroomId={classId} completed={currentClass?.completed} refreshTrigger={tabsRefreshKey} /></div> },
    { key: 'classDetails' as TabKey, label: 'Class Details', render: () => <ClassDetailsTab key={`${tabsRefreshKey}-classDetails`} classId={classId} currentClass={currentClass} auth={auth} /> },
    { key: 'leaves' as TabKey, label: 'Leaves', render: () => <div key={`${tabsRefreshKey}-leaves`} className="max-w-6xl mx-auto "><LeaveRequestsList classId={classId!} isTeacher={true} /></div> },
    { key: 'whiteBoard' as TabKey, label: 'White Board', render: () => <div key={`${tabsRefreshKey}-whiteBoard`} className="max-w-6xl mx-auto "><Whiteboard classId={classId} isTeacher={true} /></div> },
  ];

  const activeTabComponent = tabs.find(t => t.key === activeTab)?.render?.();
      


  const handleGoBack = () => {
    navigate("/classes");
  };

  const handleShare = (method: "email" | "whatsapp" | "copy" | "other") => {
    if (currentClass) {
      if (method === "email") {
        const subject = encodeURIComponent(`Join my class: ${currentClass.className}`);
        const body = encodeURIComponent(
          `Hi,\n\nI'd like to invite you to join my class "${currentClass.className}".\n\n` +
          `Class Details:\n` +
          `- Subject: ${currentClass.subjectName || 'N/A'}\n` +
          `- Join Code: ${currentClass.joinCode}\n\n` +
          `You can join the class using this code.\n\n` +
          `Best regards`
        );
        window.location.href = `mailto:?subject=${subject}&body=${body}`;
      } else  if(method === "whatsapp"){
        shareClass(currentClass.id, method);
         const message = encodeURIComponent("Hey! Check out this code snippet:\n\nconst sum = (a, b) => a + b;");
          const whatsappUrl = `https://wa.me/?text= ${currentClass.joinCode}`;
          window.open(whatsappUrl, '_blank');

      }else if(method === "copy"){
        navigator.clipboard.writeText(currentClass.joinCode);
        toast("Join code copied to clipboard!");
      }
    }
  };

  const handleCopyJoinCode = () => {
    if (currentClass) {
      navigator.clipboard.writeText(currentClass.joinCode);
      toast("Join code copied to clipboard!");
    }
  };



  

  



  
  
if (!hasFetchedClasses.current || !availableClasses.length) {
    return <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading class...</h1>
        </div>
      </div>;
  }

  if (!currentClass) {
    return <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Class not found</h1>
          <Button onClick={() =>{
                          if (selectedRole === "TEACHER") {
                    
                      navigate('/classes');
                    }else
                      navigate('/student-dashboard');
          }    
                  
             }>
              
                Return to Classes
          </Button>
        </div>
      </div>;
  }


  return <div className="min-h-screen bg-gray-50">
      <header className="bg-white p-4 border-b border-gray-200 sticky top-0 z-10">
        <div className="flex items-center max-w-screen-xl mx-auto">
          <Button variant="ghost" className="mr-2" onClick={handleGoBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="overflow-hidden">
            <h1 className="text-lg md:text-2xl font-medium truncate md:p-1">
              {isMobile ? currentClass.className : ` ${currentClass.className}`  }
            </h1>
          </div>
        <div className="p-4 ml-auto">
        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
         {isMobile ? <></> : <span className="font-medium text-sm">Join Code:</span>}
          <div className="flex items-center gap-2">
            <code className="bg-white px-2 py-0.5 rounded border text-sm">{currentClass.joinCode}</code>
            <Button variant="ghost" size="icon" className="h-6 w-6" onClick={handleCopyJoinCode}>
              <Copy className="h-3 w-3" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleShare("email")}>
                  <Mail className="h-4 w-4 mr-2" />
                  <span>Email</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleShare("whatsapp")}>
                  <Smartphone className="h-4 w-4 mr-2" />
                  <span>WhatsApp</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleShare("copy")}>
                  <Copy className="h-4 w-4 mr-2" />
                  <span>Copy link</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
          
        </div>
        
      </header>

      <div>
      <div className="bg-white border-b border-gray-200 overflow-x-auto">
        <div className="px-4 md:px-6">
          <div className="flex gap-1 min-w-max h-auto">
            {tabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`px-4 py-3 text-nowrap relative transition-colors ${activeTab === tab.key ? 'text-gray-900' : 'text-gray-600 hover:text-gray-900'}`}>
                {tab.label}
                {activeTab === tab.key && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-purple-600" />
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="p-4 md:p-6">
        {activeTabComponent}
      </div>
    </div>
      
  
 

    </div>;
};

export default ClassDetailPage;
