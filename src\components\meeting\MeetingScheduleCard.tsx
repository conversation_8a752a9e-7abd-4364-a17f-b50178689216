
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Calendar, Clock, User } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import { UserRole } from '@/types';
import { useUserRole } from "@/hooks/useUserRole";

interface MeetingScheduleCardProps {
  totalMeetings: number;
  upcomingMeetings: number;
  bookedSlots?: number;
  totalSlots?: number;
}

const MeetingScheduleCard = ({ 
  totalMeetings, 
  upcomingMeetings, 
  bookedSlots = 0,
  totalSlots = 0
}: MeetingScheduleCardProps) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { selectedRole } = useUserRole();  
 
  const handleNavigate = () => {
    if (selectedRole === UserRole.TEACHER) {
      navigate('/meeting-scheduler');
    } else if (selectedRole === UserRole.PARENT) {
      navigate('/parent-meetings');
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-semibold">Parent-Teacher Meetings</CardTitle>
        <CardDescription>
          {selectedRole === UserRole.TEACHER 
            ? 'Manage parent-teacher conferences' 
            : 'Schedule meetings with teachers'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-blue-500" />
              <span className="text-sm">Total Meetings</span>
            </div>
            <Badge variant="outline">{totalMeetings}</Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2 text-purple-500" />
              <span className="text-sm">Upcoming</span>
            </div>
            <Badge variant="outline">{upcomingMeetings}</Badge>
          </div>
          
          {selectedRole === UserRole.TEACHER && (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2 text-green-500" />
                <span className="text-sm">Booked Slots</span>
              </div>
              <Badge variant="outline">{bookedSlots}/{totalSlots}</Badge>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          variant="outline" 
          className="w-full" 
          onClick={handleNavigate}
        >
          {selectedRole === UserRole.TEACHER ? 'Schedule Meetings' : 'View & Book Meetings'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default MeetingScheduleCard;
