import { useQuery } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import { useEffect } from 'react';

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  timezone: string;
  roles: any[];
}

const fetchUserProfile = async (token: string): Promise<UserProfile> => {
  const response = await fetch("/api/userManagement/v1/me", {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  if (!response.ok) {
    throw new Error('Failed to fetch profile');
  }
  
  return response.json();
};

export const useUserProfile = () => {
  const auth = useAuth();
  
  const query = useQuery({
    queryKey: ['userProfile', auth.user?.access_token],
    queryFn: () => fetchUserProfile(auth.user!.access_token),
    enabled: auth.isAuthenticated && !!auth.user?.access_token,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000,   // 10 minutes
    initialData: () => {
      const stored = sessionStorage.getItem('userProfile');
      return stored ? JSON.parse(stored) : undefined;
    },
  });

  useEffect(() => {
    if (query.data) {
      sessionStorage.setItem('userProfile', JSON.stringify(query.data));
    }
  }, [query.data]);

  return query;
};