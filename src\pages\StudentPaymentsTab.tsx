import { Card, CardContent } from "@/components/ui/card";
import React, { useEffect, useState, useRef } from "react";
 import { useAuth } from "react-oidc-context";
 import { useSelector } from "react-redux";
 import { getStudentPayments } from "@/services/paymentService";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";


interface StudentPaymentsTabProps {
  classId: string;
}

export default function StudentPaymentsTab({ classId }: StudentPaymentsTabProps) {
    const { user } = useAuth();
    const userData = useSelector((state: any) => state.user.userData);
    const [isLoading, setIsLoading] = useState(true);
    const [paymentReport, setPaymentReport] = useState(null);
    
    const hasFetched = useRef(false);

    useEffect(() => {
     if (hasFetched.current) return;
     
     const fetchPaymentsReport = async () => {
       hasFetched.current = true;
       try {
         setIsLoading(true);
         const data = await getStudentPayments(user?.access_token, classId,userData.id);
         setPaymentReport(data);
       } catch (error) {
         console.error('Error fetching attendance report:', error);
       } finally {
         setIsLoading(false);
       }
     };

       fetchPaymentsReport();
     
   }, [classId,userData.id, user?.access_token]);

    return <div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
            <CardContent className="pt-6">
                <div className="text-center">
                <div className="text-4xl font-bold text-green-500 mb-2">$0</div>
                <p className="text-sm text-gray-600">Total Paid</p>
                </div>
            </CardContent>
            </Card>
            <Card>
            <CardContent className="pt-6">
                <div className="text-center">
                <div className="text-4xl font-bold text-amber-500 mb-2">$0</div>
                <p className="text-sm text-gray-600">Pending</p>
                </div>
            </CardContent>
            </Card>
           
        </div>
        
         <Card>
            <CardContent className="p-4 md:p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Payment History</h3>
              </div>
              {isLoading ? (
                <div>Loading payment history...</div>
              ) : paymentReport && paymentReport.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Student</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paymentReport.map((payment, idx) => (
                      <TableRow key={payment.id || idx}>
                        <TableCell>{payment.paymentDate ? payment.paymentDate.slice(0, 10) : "-"}</TableCell>
                        <TableCell>{payment.studentName || payment.studentId}</TableCell>
                        <TableCell>₹{payment.amount}</TableCell>
                        <TableCell>{payment.paymentMethod}</TableCell>
                        <TableCell>{payment.status || "Completed"}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <img src="/lovable-uploads/6829d795-39c9-4216-a398-3b7ada11f608.png" alt="No payments" className="w-16 h-16 md:w-24 md:h-24 object-contain mb-4" />
                  <p className="text-base md:text-lg font-medium mb-1">No payment records found</p>
                  <p className="text-xs md:text-sm text-gray-500 mb-4 text-center">
                    Payment records will appear here once students start making payments.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
    </div>
 }