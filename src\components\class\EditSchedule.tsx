import React, { useState, useMemo,useEffect } from 'react';
import { Calendar as CalendarIcon, Video, ArrowLeft, ArrowRight, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { format, addDays, set } from 'date-fns';
import { cn } from '@/lib/utils';
import StepIndicator from '@/components/StepIndicator';
import DaySelector from '@/components/DaySelector';
import TimeSelector from '@/components/TimeSelector';
 import {  getTimeZones } from "@/services/scheduleService";

interface EditScheduleProps {
  formData: {
    classroomId: string; // optional if not always availables
    sessionType:string,
    startDate: Date;
    endDate: Date;
    daysOfWeek: string[];
    sessionStartTime: string;
    sessionEndTime: string;
    recurrenceType: string;
    meetingLink:string
    timezone: string;
  };
  updateFormData: (data: Partial<EditScheduleProps["formData"]>) => void;
  classroomId : string | null;// <-- add this line
  onValidationChange?: (isValid: boolean, validate: (forceValidation?: boolean) => boolean) => void;
}
const EditSchedule = ({formData, updateFormData ,classroomId, onValidationChange}: EditScheduleProps)  => {
  const [startDate, setStartDate] = useState<Date>(new Date()); // May 24th, 2025
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [sessionStartTime, setSessionStartTime] = useState('');
  const [sessionEndTime, setSessionEndTime] = useState('');
  const [meetingLink, setMeetingLink] = useState(formData.meetingLink || '');
  const [address, setAddress] = useState('');
  const [sessionType, setSessionType] = useState(formData.sessionType || 'SINGLE');
  const [daysOfWeek, setDaysOfWeek] = useState([]);
  const [meetingType, setMeetingType] = useState('online');
  const [recurrenceType, setRecurrenceType] = useState(formData.recurrenceType || 'MULTIPLE');
    const [selectedTimezone, setSelectedTimezone] = useState("UTC");
  
  // Validation errors state
  const [errors, setErrors] = useState({
    startDate: '',
    endDate: '',
    sessionStartTime: '',
    sessionEndTime: '',
    daysOfWeek: '',
    meetingLink: '',
    address: ''
  });
  const [hasInteracted, setHasInteracted] = useState(false);
   const [availableTimeZones, setAvailableTimeZones] = useState([
        "UTC",
        "America/New_York",
        "America/Chicago",
        "America/Denver",
        "America/Los_Angeles",
        "Europe/London",
        "Europe/Paris",
        "Asia/Kolkata",
        "Asia/Shanghai",
        "Asia/Tokyo",
        "Australia/Sydney",
        "Asia/Dubai",
        "America/Toronto",
        "Europe/Berlin",
        "Asia/Singapore"
      ]);
  useEffect(() => {
  updateFormData({
        classroomId: classroomId ?? "",

    sessionStartTime,
    sessionEndTime,
    sessionType,
    startDate,
    endDate,
    daysOfWeek,
    meetingLink,
    timezone: selectedTimezone
  });
  // eslint-disable-next-line
}, [classroomId,sessionStartTime, sessionEndTime, sessionType, startDate, endDate, daysOfWeek, meetingLink]);

   // Calculate session duration in hours
  const sessionDurationHours = useMemo(() => {
    if (!sessionStartTime || !sessionEndTime) return 0;
   
    const [startHour, startMin] = sessionStartTime.split(':').map(Number);
    const [endHour, endMin] = sessionEndTime.split(':').map(Number);
   
    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;
   
    return (endMinutes - startMinutes) / 60;
  }, [sessionStartTime, sessionEndTime]);

  // Calculate total sessions and hours
  const { totalSessions, totalHours } = useMemo(() => {
    if (sessionType === 'SINGLE') {
      return {
        totalSessions: 1,
        totalHours: sessionDurationHours
      };
    }

    if (!startDate || !endDate || daysOfWeek.length === 0) {
    
      return { totalSessions: 0, totalHours: 0 };
    }
console.log(startDate , endDate);
  const dayMap = {
  'SUNDAY': 0, 'MONDAY': 1, 'TUESDAY': 2, 'WEDNESDAY': 3, 'THURSDAY': 4, 'FRIDAY': 5, 'SATURDAY': 6
};  
    const dayNames = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
    let sessions = 0;
    let currentDate = new Date(startDate);
    const endDateTime = new Date(endDate);

    while (currentDate <= endDateTime) {
      const dayName = dayNames[currentDate.getDay()];
      if (daysOfWeek.includes(dayName)) {
        sessions++;
      }
      currentDate = addDays(currentDate, 1);
    }

    return {
      totalSessions: sessions,
      totalHours: sessions * sessionDurationHours
    };
  }, [sessionType, startDate, endDate, daysOfWeek, sessionDurationHours]);

  // Validation function
  const validateForm = (forceValidation = false) => {
    const newErrors = {
      startDate: !startDate ? 'Start date is required' : '',
      endDate: sessionType === 'RECURRING' && !endDate ? 'End date is required' : '',
      sessionStartTime: !sessionStartTime ? 'Start time is required' : '',
      sessionEndTime: !sessionEndTime ? 'End time is required' : '',
      daysOfWeek: sessionType === 'RECURRING' && daysOfWeek.length === 0 ? 'At least one day must be selected' : '',
      meetingLink: meetingType === 'online' && !meetingLink ? 'Meeting link is required for online sessions' : '',
      address: meetingType === 'in-person' && !address ? 'Address is required for in-person sessions' : ''
    };
    
    if (hasInteracted || forceValidation) {
      setErrors(newErrors);
      setHasInteracted(true);
    }
    return !Object.values(newErrors).some(error => error !== '');
  };

  // Expose validation to parent
  useEffect(() => {
    if (onValidationChange) {
      const isValid = validateForm();
      onValidationChange(isValid, validateForm);
    }
  }, [startDate, endDate, sessionStartTime, sessionEndTime, daysOfWeek, meetingLink, address, sessionType, meetingType]);

  

  return (
        
      <div className="bg-white rounded-lg shadow-sm no-border p-2 ">
           <div className="space-y-3">
            {/* Session Type and Timezone */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Session Type*
                </Label>
                <RadioGroup value={sessionType}  onValueChange={value => {
                  setSessionType(value);
                  setRecurrenceType(value);
                      updateFormData({ sessionType: value ,recurrenceType :value});
            }} className="flex space-x-6">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="SINGLE" id="single" />
                    <Label htmlFor="single" className="text-sm font-medium text-gray-900">
                      Single Session
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="RECURRING" id="recurring" />
                    <Label htmlFor="recurring" className="text-sm font-medium text-gray-900">
                      Multiple Sessions
                    </Label>
                  </div>
                </RadioGroup>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Timezone*
                </Label>
                <Select 
                  defaultValue="UTC" 
                  value={selectedTimezone}
                  onValueChange={(value) => setSelectedTimezone(value)}
                >
                  <SelectTrigger className="edu-form-field w-full bg-[#f6f7f8]">
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    {availableTimeZones.map((tz) => (
                      <SelectItem key={tz} value={tz}>
                        {tz}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            {/* Date Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="start-date" className="text-sm font-medium text-gray-700 mb-2 block">
                  {sessionType === 'SINGLE' ? 'Session Date*' : 'Start Date*'}
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "edu-form-field w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "yyyy-MM-dd") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                      initialFocus
                      className="p-3 pointer-events-auto"
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {sessionType === 'RECURRING' && (
                <div>
                  <Label htmlFor="end-date" className="text-sm font-medium text-gray-700 mb-2 block">
                    End Date*
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "edu-form-field w-full justify-start text-left font-normal",
                          !endDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, "yyyy-MM-dd") : "Select date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                                            mode="single"
                                            selected={endDate}
                                            onSelect={setEndDate}
                                            disabled={(date) => {
                                              const start = new Date(startDate);
                                              start.setHours(0, 0, 0, 0);
                                              const d = new Date(date);
                                              d.setHours(0, 0, 0, 0);
                                              return d < start;
                                            }}   
                                            initialFocus
                                            className="p-3 pointer-events-auto"
                                          />
                    </PopoverContent>
                  </Popover>
                  {endDate < startDate && (
  <div className="text-red-600 text-sm mt-2">
    End date should not be less than start date.
  </div>
)}
                </div>
              )}
            </div>
              
            {/* Time Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <TimeSelector
                  label="Start Time"
                  value={sessionStartTime}
                  
                  onChange={value => {
                      setSessionStartTime(value);
                      // Auto-set end time to 1 hour later
                      if (value) {
                        const [hour, minute] = value.split(':').map(Number);
                        const endHour = hour + 1;
                        const endTime = `${endHour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                        setSessionEndTime(endTime);
                        updateFormData({ sessionEndTime: endTime });
                      }
                      setHasInteracted(true);
                      setErrors(prev => ({ ...prev, sessionStartTime: '' }));
                      updateFormData({ sessionStartTime: value });
                    }}
                  placeholder="Select start time"
               className="edu-form-field bg-[#f6f7f8]"
                />
                {errors.sessionStartTime && (
                  <div className="text-red-600 text-sm mt-1">{errors.sessionStartTime}</div>
                )}
              </div>
              <div>
                <TimeSelector
                  label="End Time"
                  value={sessionEndTime}
                  className="edu-form-field bg-[#f6f7f8]"
                
                  onChange={value => {
                    setSessionEndTime(value);
                    setHasInteracted(true);
                    setErrors(prev => ({ ...prev, sessionEndTime: '' }));
                    console.log("End Time:", value);
                    updateFormData({ sessionEndTime: value });
                  }}
                  placeholder="Select end time"
                  disabledTimes={sessionStartTime ? (() => {
                    const disabled = [];
                    const [startHour, startMinute] = sessionStartTime.split(':').map(Number);
                    for (let hour = 6; hour <= 23; hour++) {
                      for (let minute = 0; minute < 60; minute += 15) {
                        const timeValue = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                        if (hour < startHour || (hour === startHour && minute <= startMinute)) {
                          disabled.push(timeValue);
                        }
                      }
                    }
                    return disabled;
                  })() : []}
                />
                {errors.sessionEndTime && (
                  <div className="text-red-600 text-sm mt-1">{errors.sessionEndTime}</div>
                )}
              </div>
            </div>

            {/* Day Selection for Multiple Sessions */}
            {sessionType === 'RECURRING' && (
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-3 block">
                  Select Days*
                </Label>
                <DaySelector selectedDays={daysOfWeek} onDaysChange={(days) => {
                  setDaysOfWeek(days);
                  setHasInteracted(true);
                  setErrors(prev => ({ ...prev, daysOfWeek: '' }));
                }} />
                {errors.daysOfWeek && (
                  <div className="text-red-600 text-sm mt-1">{errors.daysOfWeek}</div>
                )}
              </div>
            )}

            {/* Meeting Type Selection */}
            <div className="space-y-4">
              <Label className="text-sm font-medium text-gray-700">
                Meeting Type*
              </Label>
              <RadioGroup value={meetingType} 
                 onValueChange={(value) => {
                setMeetingType(value);
                updateFormData({
                    meetingLink: value === "online" ? meetingLink : "",
                  
                });
              }}
              className="flex space-x-6">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="online" id="online" />
                  <Label htmlFor="online" className="text-sm font-medium text-gray-900 flex items-center">
                    <Video className="mr-2 h-4 w-4 text-blue-600" />
                    Online Meeting
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="in-person" id="in-person" />
                  <Label htmlFor="in-person" className="text-sm font-medium text-gray-900 flex items-center">
                    <MapPin className="mr-2 h-4 w-4 text-green-600" />
                    In-Person Meeting
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Online Meeting Details - Conditional */}
            {meetingType === 'online' && (
              <div className="space-y-6">
               
                <div>
                  <Label htmlFor="meeting-link" className="text-sm font-medium text-gray-700 mb-2 block">
                    Meeting Link/ID
                  </Label>
                  <Input
                    type="text"
                    value={meetingLink}
                    onChange={(e) => {
                    setMeetingLink(e.target.value);
                    setHasInteracted(true);
                    setErrors(prev => ({ ...prev, meetingLink: '' }));
                    updateFormData({
                        meetingLink: e.target.value
                    });                  }}
                    placeholder="Enter meeting link or ID"
                    className="edu-form-field h-12"
                  />
                  {errors.meetingLink && (
                    <div className="text-red-600 text-sm mt-1">{errors.meetingLink}</div>
                  )}
                </div>
              </div>
            )}

            {/* In-Person Meeting Details - Conditional */}
            {meetingType === 'in-person' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5 text-green-600" />
                  <h3 className="text-lg font-medium text-gray-900">In-Person Meeting Details</h3>
                </div>

                <div>
                  <Label htmlFor="address" className="text-sm font-medium text-gray-700 mb-2 block">
                    Address
                  </Label>
                  <Textarea
                    value={address}
                    onChange={(e) => {
                      setAddress(e.target.value);
                      setHasInteracted(true);
                      setErrors(prev => ({ ...prev, address: '' }));
                    }}
                    placeholder="Enter the full address where the class will be held"
                    className="min-h-[100px]"
                  />
                  {errors.address && (
                    <div className="text-red-600 text-sm mt-1">{errors.address}</div>
                  )}
                </div>
              </div>
            )}

            {/* Session Summary */}
            {(sessionStartTime && sessionEndTime) && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Session Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-blue-700 font-medium">Total Sessions:</span>
                    <span className="ml-2 text-blue-900">{totalSessions}</span>
                  </div>
                  <div>
                    <span className="text-blue-700 font-medium">Total Hours:</span>
                    <span className="ml-2 text-blue-900">{totalHours.toFixed(1)} hours</span>
                  </div>
                </div>
              </div>
            )}

          </div>
        </div>
      );
};

export default EditSchedule;