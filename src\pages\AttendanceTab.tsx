import React, { useState, useEffect ,useRef} from "react";
// auth is passed as a prop now
import { getAttendanceStatsSummary } from "@/services/attendanceService";
import { Button } from "@/components/ui/button";
import { subMonths ,isSameDay} from "date-fns"; // Add this import
import { toast } from "sonner";
 import { getSchedulesByClassId} from "@/services/scheduleService";
import { AttendanceSummary } from "@/components/attendance/AttendanceSummary";
import { format,isPast } from "date-fns";

import { Card } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import RecordAttendance from './RecordAttendance';
import { convertUTCToLocalTime  } from "@/utils/convertFromUTC";
const AttendanceTab = ({ classId, auth, timezone, currentClass, selectedScheduleDate, setSelectedScheduleDate }: any) => {
     const [attendanceSummary, setAttendanceSummary] = useState(null);
    const [attendanceSummaryLoading, setAttendanceSummaryLoading] = useState(false);
    const [attendanceRecords, setAttendanceRecords] = useState<any[]>([]);
    const [attendanceRecordsLoading, setAttendanceRecordsLoading] = useState(false);
    const [schedulesRecordsLoading, setSchedulesRecordsLoading] = useState(true);
     const [schedules, setSchedules] = useState<any[]>([]);
         const [isRecordAttendanceOpen, setIsRecordAttendanceOpen] = useState(false);
         const [fromDate, setFromDate] = useState<Date | null>(subMonths(new Date(), 1)); // Default: 1 month back
         const [toDate, setToDate] = useState<Date | null>(new Date()); // Default: today
         
       const hasFetched = useRef(false); 
    const fetchAttendanceSummary = async () => {
          if (!auth.user?.access_token || !classId) return;
          setAttendanceSummaryLoading(true);
         
          try {
            
            const data = await getAttendanceStatsSummary(auth.user.access_token, classId,
          format(fromDate, "yyyy-MM-dd"),
          format(toDate, "yyyy-MM-dd"));
         
            setAttendanceSummary(data);
            setAttendanceRecords(data.dailyAttendanceRates)
            
          } catch (error) {
            toast.error("Failed to load attendance summary");
          } finally {
            setAttendanceSummaryLoading(false);
            
          
          }
        };
    const fetchSchedules = async () => {
           try {
             setSchedulesRecordsLoading(true);
             const data = await getSchedulesByClassId(auth.user.access_token, classId);
             setSchedules(data || []);
             console.log("Schedules:", data);
             setSchedulesRecordsLoading(false);
           } catch (error) {
             console.error("Error fetching schedules:", error);
             setSchedulesRecordsLoading(false);
           }
         };
       useEffect(() => {
         if (auth?.user?.access_token && classId && !hasFetched.current) {
           hasFetched.current = true;
           fetchSchedules();
         }
       }, [auth?.user?.access_token, classId]);
      // Function to check if a schedule is expired
  const isScheduleExpired = (schedule) => {
    if (!schedule) return false;
    
    try {
      // Handle different schedule object structures
      if (schedule.date) {
        // This is a schedule from getSchedulesForDate
        const scheduleDate = new Date(schedule.date);
        const today = new Date();
        return isPast(scheduleDate) && !isSameDay(scheduleDate, today);
      } else if (schedule.startDate) {
        // This is a regular schedule object
        const scheduleDate = new Date(schedule.startDate);
        
        // If it's a single event, check if the date is in the past
        if (schedule.recurrenceType === "SINGLE") {
          // Create a date with the schedule's time
          const scheduleDateTime = new Date(scheduleDate);
          if (schedule.sessionEndTime) {
            const [hours, minutes] = schedule.sessionEndTime.split(':').map(Number);
            scheduleDateTime.setHours(hours, minutes, 0, 0);
          }
          return isPast(scheduleDateTime);
        } 
        // For recurring events, check if the end date is in the past
        else if (schedule.endDate) {
          const endDate = new Date(schedule.endDate);
          return isPast(endDate);
        }
      }
      
      return false;
    } catch (error) {
      console.error("Error checking if schedule is expired:", error);
      return false;
    }
  };
  
    return (
      <div className="max-w-6xl mx-auto px-4 py-6">
                  
                      
                         {attendanceSummaryLoading ? (
                      <div>Loading attendance summary...</div>
                    ) : attendanceSummary ? (
                      <AttendanceSummary
                       averageAttendance={Math.round(parseFloat(attendanceSummary.averageAttendanceRate))}
                       classesThisMonth={attendanceSummary.totalClasses}
                        studentsBelow75={attendanceSummary.totalAbsentRecords}
                        onRecordAttendance={() =>setIsRecordAttendanceOpen(true)}
                      />
                      
                    ) : (
                      <div>No attendance summary available.</div>
                    )}
                    {schedulesRecordsLoading ? (
                      <div className="text-center py-4">Loading...</div>
                    ) : schedules && schedules.length > 0 ? (
                      <div className=" border rounded-lg p-4 mt-3  bg-white">
                        <h3 className="font-semibold text-lg mb-3">
                          All Schedules
                        </h3>
                        <div className="space-y-2">
                          {schedules
                            .sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime())
                            .map(schedule => (
                            <div 
                              key={schedule.id}
                              className={`flex items-center justify-between p-3 border rounded ${isScheduleExpired(schedule) ? "text-gray-400 bg-gray-50" : "bg-white"}`}
                            >
                              <div>
                                <span className="font-medium">{schedule.classroomName}</span><br></br>
                                <span className="text-sm">
                                  {convertUTCToLocalTime(schedule.startDate,schedule.sessionStartTime,timezone).date} | {convertUTCToLocalTime(schedule.startDate,schedule.sessionStartTime,timezone).time} - {convertUTCToLocalTime(schedule.startDate,schedule.sessionEndTime,timezone).time}
                                </span>
                                {schedule.recurrenceType !== "SINGLE" && (
                                  <span className="text-sm text-gray-500 ml-2">
                                    (until {convertUTCToLocalTime(schedule.endDate,schedule.sessionEndTime,timezone).date})
                                  </span>
                                )}
                              </div>
                              <Button
                                className="bg-purple-600 hover:bg-purple-700 text-white"
                                  disabled={currentClass?.completed}

                                onClick={() => {
                                  let aDate=convertUTCToLocalTime(schedule.startDate,schedule.sessionStartTime,timezone).date;
                                   setSelectedScheduleDate(aDate);
                                  setIsRecordAttendanceOpen(true);
                                }}
                              >
                                Record Attendance
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-4 text-gray-500">No schedules available</div>
                    )}

                 <Card  className=" mt-6 p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold">Attendance History</h3>
                    </div>
                    <div className="flex flex-col gap-4 mb-4">
                      <div className="flex flex-col sm:flex-row gap-4">
                        <div className="flex-1">
                          <label className="block text-sm font-medium mb-1">From</label>
                          <input
                            type="date"
                            className="edu-form-field border rounded px-2 py-1 w-full"
                            value={fromDate ? format(fromDate, "yyyy-MM-dd") : ""}
                            onChange={e => setFromDate(e.target.value ? new Date(e.target.value) : null)}
                            max={toDate ? format(toDate, "yyyy-MM-dd") : undefined}
                          />
                        </div>
                        <div className="flex-1">
                          <label className="block text-sm font-medium mb-1">To</label>
                          <input
                            type="date"
                            className="edu-form-field border rounded px-2 py-1 w-full"
                            value={toDate ? format(toDate, "yyyy-MM-dd") : ""}
                            onChange={e => setToDate(e.target.value ? new Date(e.target.value) : null)}
                            min={fromDate ? format(fromDate, "yyyy-MM-dd") : undefined}
                          />
                        </div>
                      </div>
                      <Button
                        className="bg-purple-600 hover:bg-purple-700 text-white w-full sm:w-auto"
                        onClick={fetchAttendanceSummary}
                        disabled={!fromDate || !toDate}
                      >
                        Go
                      </Button>
                    </div>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Class</TableHead>
                          <TableHead>Present</TableHead>
                          <TableHead>Absent</TableHead>
                          <TableHead>Late</TableHead>
                          <TableHead>Percentage</TableHead>
                        </TableRow>
                      </TableHeader>
                    <TableBody>
                      {attendanceRecordsLoading ? (
                        <TableRow>
                          <TableCell colSpan={5}>Loading...</TableCell>
                        </TableRow>
                      ) : attendanceRecords && attendanceRecords.length > 0 ? (
                        attendanceRecords.map((record, index) => (
                          
                          <TableRow key={record.id || index}>
                            <TableCell>
                              {record.date ? (
                                <div>
                                  <div>{format(new Date(record.date), 'MMM dd, yyyy')}</div>
                                  <div className="text-xs text-gray-500">{format(new Date(record.date), 'EEEE')}</div>
                                </div>
                              ) : "-"}
                            </TableCell>
                            <TableCell>{record.classroomName ?? "-"}</TableCell>
                            <TableCell className="text-green-600">{record.presentStudents ?? "-"}</TableCell>
                            <TableCell className="text-red-600">{record.absentStudents ?? "-"}</TableCell>
                            <TableCell className="text-amber-600">{record.lateStudents ?? "-"}</TableCell>
                            <TableCell>{record.attendanceRate ?? "-"}</TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5}>No attendance records found for selected range.</TableCell>
                        </TableRow>
                      )}
                      </TableBody>
                    </Table>    
                
                  </Card>   
                
                  <Dialog open={isRecordAttendanceOpen} onOpenChange={setIsRecordAttendanceOpen}>
                  <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Record Attendance</DialogTitle>
                        <DialogDescription>
                          Mark attendance for class session on {selectedScheduleDate ? selectedScheduleDate : 'selected date'}
                        </DialogDescription>
                      </DialogHeader>
                      <RecordAttendance
                        classId={classId}
                        selectedDate={selectedScheduleDate}
                        onSuccess={() => {
                          setIsRecordAttendanceOpen(false);
                          fetchAttendanceSummary();
                        }}
                        onCancel={() => setIsRecordAttendanceOpen(false)}
                      />
                    </DialogContent>
                  </Dialog>  
                </div>   
    );
  };
  
  export default AttendanceTab;