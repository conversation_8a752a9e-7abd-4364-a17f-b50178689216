import { ChatMessage, User } from '@/types';

export interface ChatContact {
  id: string;
  name: string;
  role: string;
  avatar: string;
  lastMessage: string;
  lastMessageTime: Date;
  unreadCount: number;
  isOnline: boolean;
}

class ChatService {
  private baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

  async getContacts(userId: string): Promise<ChatContact[]> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/contacts/${userId}`);
      if (!response.ok) throw new Error('Failed to fetch contacts');
      return await response.json();
    } catch (error) {
      console.error('Error fetching contacts:', error);
      // Return mock data for now
      return this.getMockContacts();
    }
  }

  async getMessages(userId: string, contactId: string): Promise<ChatMessage[]> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/messages/${userId}/${contactId}`);
      if (!response.ok) throw new Error('Failed to fetch messages');
      return await response.json();
    } catch (error) {
      console.error('Error fetching messages:', error);
      return [];
    }
  }

  async sendMessage(message: Omit<ChatMessage, 'id' | 'timestamp' | 'read'>): Promise<ChatMessage> {
    // For now, just return a mock response since API doesn't exist yet
    const mockMessage: ChatMessage = {
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false,
      ...message
    };
    return mockMessage;
  }

  async markAsRead(messageId: string): Promise<void> {
    try {
      await fetch(`${this.baseUrl}/chat/read/${messageId}`, { method: 'PUT' });
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  }

  private getMockContacts(): ChatContact[] {
    return [
      {
        id: "2",
        name: "John Student",
        role: "student",
        avatar: "https://i.pravatar.cc/150?img=59",
        lastMessage: "Hello teacher, I have a question about the homework",
        lastMessageTime: new Date(),
        unreadCount: 2,
        isOnline: true
      },
      {
        id: "3",
        name: "Sarah Parent",
        role: "parent",
        avatar: "https://i.pravatar.cc/150?img=47",
        lastMessage: "My child is sick today and won't be attending class",
        lastMessageTime: new Date(Date.now() - 3600000),
        unreadCount: 0,
        isOnline: false
      },
      {
        id: "4",
        name: "Mike Student",
        role: "student",
        avatar: "https://i.pravatar.cc/150?img=68",
        lastMessage: "Thank you for the feedback on my project",
        lastMessageTime: new Date(Date.now() - 7200000),
        unreadCount: 1,
        isOnline: true
      }
    ];
  }
}

export const chatService = new ChatService();