import { Card, CardContent } from "@/components/ui/card";

import React from "react";
import { Button } from "@/components/ui/button";
import { Announcement } from "@/types";
import { useNavigate } from "react-router-dom";

interface AnnouncementItemProps {
  announcement: Announcement;
  getAnnouncementIcon: (iconType: string) => string;
  getIconColorClass: (color: string) => string;
  isLast: boolean;
}

export const AnnouncementItem: React.FC<AnnouncementItemProps> = ({
  announcement,
  getAnnouncementIcon,
  getIconColorClass,
  isLast
}) => {
  const navigate = useNavigate();
  const handleViewDetails = () => {
    navigate('/announcements', { 
      state: { selectedAnnouncementId: announcement.id }
    });
  };

  return (
    
<Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
  <div className="space-y-2">
      <div className="flex items-center">
        <span className={`${getIconColorClass(announcement.iconColor || '')} mr-2 text-sm sm:text-base`}>
          {getAnnouncementIcon(announcement.icon || '')}
        </span>
        <h3 className="font-semibold text-base sm:text-lg">{announcement.title.length > 50 ? `${announcement.title.substring(0, 50)}...` : announcement.title}</h3>
      </div>
      <div className="text-xs sm:text-sm text-gray-600">
         <div className="text-sm sm:text-base text-muted-foreground line-clamp-2 mb-2" dangerouslySetInnerHTML={{ __html: announcement.content }} />
      </div>
      <div className="flex justify-between items-center">
        <div className="text-xs text-gray-500">
          {announcement.updatedDate
            ? new Date(announcement.updatedDate).toISOString().split('T')[0]
            : new Date(announcement.createdDate).toISOString().split('T')[0]
          }
        </div>
        <Button
         variant="ghost"
                size="sm"
                className="h-7 text-xs"
          onClick={handleViewDetails}
        >
          View Details
        </Button>
      </div>
    </div>
    </CardContent>
    </Card>
  );
};
