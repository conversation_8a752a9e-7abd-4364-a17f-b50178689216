import { use<PERSON><PERSON><PERSON>, useN<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useApp } from "@/context/AppContext";
import { useAuth } from "react-oidc-context"; // Updated import
import { useEffect, useState,useRef } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Clock, Users, MapPin, Star, Calendar, DollarSign, BookOpen,Sparkles, GraduationCap } from "lucide-react";
import { getAllSyllabus } from "@/services/syllabusService";
import { Syllabus } from "@/types";
import { getProfile } from "@/services/profileService";
import { getTeacherReviews } from "@/services/reviewService";
import { getAboutData } from "@/services/aboutService";
import { getAllExperience } from "@/services/profileService";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { useUserRole } from "@/hooks/useUserRole";
import { getClassesFromBackend, ClassData  } from "@/services/classService";
import { downloadAndCreateImageUrl } from "@/utils/fileUpload";
import { getSchedulesByClassId } from "@/services/scheduleService";
import { getFeeStructuresForClassRoom }from "@/services/feeServices";
import { toast } from "sonner";


export const PublicPage = () => {
  const { classId } = useParams();
  const { classes } = useApp();
  const auth = useAuth();
  const [syllabusTopics, setSyllabusTopics] = useState<Syllabus[]>([]);
  const [teacherProfile, setTeacherProfile] = useState<any>(null);
  const [teacherReviews, setTeacherReviews] = useState<any[]>([]);
  const [teacherAbout, setTeacherAbout] = useState<any>(null);
  const [teacherExperience, setTeacherExperience] = useState<any[]>([]);
const { selectedRole } = useUserRole();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
 const [availableClasses, setAvailableClasses] = useState<ClassData[]>([]);
  const [posterImageUrl, setPosterImageUrl] = useState<string | null>(null);
  const [classSchedules, setClassSchedules] = useState<any[]>([]);
  
  const currentClass = availableClasses.find(c => c.id === classId);  const navigate = useNavigate();
   const [schedules, setSchedules] = useState([]);
   const hasFetchedFees = useRef(false);
 const [feeStructures, setFeeStructures] = useState<any[]>([]);
   const [isLoading, setIsLoading] = useState(true);
 const [refreshKey, setRefreshKey] = useState(0);
   useEffect(() => {
      const fetchFeeStructures = async () => {
        try {
          setIsLoading(true);
          const data = await getFeeStructuresForClassRoom(auth.user.access_token, classId);
          setFeeStructures(data);
        } catch (error) {
          toast.error("Failed to load fee structures");
        } finally {
          setIsLoading(false);
        }
      };
      if (auth.user?.access_token && classId) {
        if (refreshKey !== undefined) {
          // If refreshKey is provided, always fetch
          fetchFeeStructures();
        } else if (!hasFetchedFees.current) {
          // Otherwise, only fetch once
          hasFetchedFees.current = true;
          fetchFeeStructures();
        }
      }
    }, [auth.user?.access_token, classId, refreshKey]);

     
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const classes = await getClassesFromBackend(auth.user?.access_token);
        setAvailableClasses(classes.content || []);
        // Move the currentClass check after setting availableClasses
        const updatedCurrentClass = classes.content?.find(c => c.id === classId);
        if (!updatedCurrentClass) {
          setError("Class not found");
          setLoading(false);
          return;
        }
        console.log(updatedCurrentClass.description)
        const [syllabus, profile, reviews, about, experience] = await Promise.all([
          getAllSyllabus(auth.user.access_token,classId).catch(() => []),
          getProfile(auth.user.access_token).catch(() => null),
          getTeacherReviews(auth.user.access_token).catch(() => []),
          getProfile(auth.user.access_token).catch(() => null),
          getAllExperience(auth.user.access_token).catch(() => [])
        ]);
        
        console.log('Reviews data:', reviews);
        setSyllabusTopics(syllabus || []);
        setTeacherProfile(profile);
        setTeacherReviews(Array.isArray(reviews) ? reviews : []);


        setTeacherAbout(about);
        setTeacherExperience(Array.isArray(experience) ? experience : []);
        
        // Download poster image if posterFileId exists
        if (updatedCurrentClass?.posterFileId) {
          const imageUrl = await downloadAndCreateImageUrl(auth.user.access_token, updatedCurrentClass.posterFileId);
          setPosterImageUrl(imageUrl);
        }
        
        // Fetch class schedules
        const schedules = await getSchedulesByClassId(auth.user.access_token, classId).catch(() => ({ content: [] }));
        setClassSchedules(schedules?.content || []);
        setSchedules(schedules?.content || []);
      } catch (error) {
        console.error('Error fetching data:', error);
                setAvailableClasses([]);

        setError("Failed to load class data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();

  }, [classId, auth.user]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Loading Class Information</h1>
          <p className="text-muted-foreground">Please wait while we load the class details...</p>
        </div>
      </div>
    );
  }
  const averageRating = teacherReviews.length > 0 
    ? teacherReviews.reduce((sum, review) => sum + review.rating, 0) / teacherReviews.length
    : 0;
  if ( !currentClass) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Class Not Found</h1>
          <p className="text-muted-foreground">{error || "The class you're looking for doesn't exist."}</p>
          <Button 
            onClick={() => {
              if (selectedRole === "TEACHER") {
                navigate('/classes');
              } else {
                navigate('/student-dashboard');
              }    
            }}
            className="mt-4"
          >
            Return to Classes
          </Button>
        </div>
      </div>
    );
  }

  

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-gray-100">
          <div className="flex-grow">
            <DashboardHeader />
            </div>
    <div className="min-h-screen bg-background">
      {/* Hero Section with gradient background */}
        <section className="py-16 md:py-24 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-blue-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto relative z-10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
              <div className="max-w-2xl text-center md:text-left">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white leading-tight">
                  Transform <span className="text-yellow-300">Education</span> Through Innovation
                </h1>
                <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
                  Join thousands of educators and students in revolutionizing the learning experience
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <Button asChild size="lg" className="w-full sm:w-auto bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full shadow-lg">
                    <Link to="/register">Get Started Free</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="w-full sm:w-auto border-2 bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full">
                    <Link to="/subscription">View Pricing</Link>
                  </Button>
                </div>
                <div className="mt-12 flex flex-col sm:flex-row items-center justify-center md:justify-start gap-6">
                  <div className="flex -space-x-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="w-12 h-12 rounded-full border-4 border-purple-600 bg-white shadow-lg flex items-center justify-center text-purple-600 font-bold text-lg">
                        {String.fromCharCode(64 + i)}
                      </div>
                    ))}
                  </div>
                  <p className="text-lg text-white/90">
                    Joined by <span className="font-bold text-yellow-300">2000+</span> educators
                  </p>
                </div>
              </div>
              <div className="w-full md:w-2/5 relative">
                <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-transform duration-500">
                  <img 
                    src="https://images.unsplash.com/photo-1571260899304-425eee4c7efc?q=80&w=2070&auto=format&fit=crop" 
                    alt="EduConnect Platform" 
                    className="w-full h-64 md:h-96 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800">Modern Learning Experience</h3>
                    <p className="text-gray-600">Interactive tools for better engagement</p>
                  </div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-xl shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <p className="text-lg font-bold flex items-center gap-2">
                    <Sparkles className="h-5 w-5" />
                    AI-Powered Learning
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* About This Class */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  About This Class
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div 
                  className="text-muted-foreground leading-relaxed prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{
                    __html: currentClass?.description || "This comprehensive course is designed to provide students with a deep understanding of the subject matter through interactive lessons, practical exercises, and real-world applications. Our expert instructor brings years of experience to create an engaging learning environment."
                  }}
                />
              </CardContent>
            </Card>

            {/* Syllabus */}
            <Card>
              <CardHeader>
                <CardTitle>Course Syllabus</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {syllabusTopics.map((topic, index) => (
                    <div key={topic.id} className="flex items-start gap-4 p-4 border rounded-lg">
                      <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-semibold text-primary">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold mb-1">{topic.title}</h4>
                        <p className="text-muted-foreground text-sm">{topic.description}</p>
                        <Badge variant={topic.status === 'completed' ? 'default' : topic.status === 'ongoing' ? 'secondary' : 'outline'} className="mt-2">
                          {topic.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Teacher Profile */}
            <Card>
              <CardHeader>
                <CardTitle>Meet Your Instructor</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start gap-6">
                  <Avatar className="h-20 w-20">
                    <AvatarImage src={teacherProfile?.profilePicture} />
                    <AvatarFallback>{teacherProfile?.name?.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold mb-2">{teacherProfile?.name}</h3>
                    <p className="text-muted-foreground mb-4">{teacherProfile?.title}</p>
                    <p className="text-sm text-muted-foreground leading-relaxed mb-4">
                      {teacherAbout?.bio || "Experienced educator passionate about helping students achieve their academic goals through innovative teaching methods and personalized attention."}
                    </p>
                    
                    {/* Experience */}
                    {teacherExperience.length > 0 && (
                      <div className="mb-4">
                        <h4 className="font-semibold mb-2">Experience</h4>
                        <div className="space-y-2">
                          {teacherExperience.slice(0, 2).map((exp) => (
                            <div key={exp.id} className="text-sm">
                              <span className="font-medium">{exp.jobTitle}</span> at {exp.companyName}
                              <span className="text-muted-foreground ml-2">
                                ({exp.startYear} - {exp.currentlyWorking ? 'Present' : exp.endYear})
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="font-semibold">{averageRating.toFixed(1)}</span>
                        <span className="text-muted-foreground">({teacherReviews.length} reviews)</span>
                      </div>
                      <Button variant="outline" size="sm">
                        View Full Profile
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Reviews */}
            <Card>
              <CardHeader>
                <CardTitle>Student Reviews</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {teacherReviews.length > 0 ? teacherReviews.slice(0, 3).map((review) => (
                    <div key={review.id} className="border-b pb-6 last:border-b-0">
                      <div className="flex items-start gap-4">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback>{review.studentName.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-semibold">{review.studentName}</h4>
                            <div className="flex">
                              {[...Array(5)].map((_, i) => (
                                <Star 
                                  key={i} 
                                  className={`h-4 w-4 ${i < review.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} 
                                />
                              ))}
                            </div>
                          </div>
                          <h5 className="font-medium text-sm mb-1">{review.reviewTitle}</h5>
                          <p className="text-muted-foreground text-sm">{review.reviewText}</p>
                          <p className="text-xs text-muted-foreground mt-2">
                            {new Date(review.createdDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  )) : (
                    <p className="text-center text-muted-foreground py-4">No reviews yet</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Pricing */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Pricing
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {feeStructures.length > 0 ? (
                    feeStructures.map((fee) => (
                      <div key={fee.id} className="border rounded-lg p-4">
                        <div className="text-center py-4">
                          <div className="text-2xl font-bold mb-1">${fee.feeAmount}</div>
                          <div className="text-muted-foreground">{fee.paymentType}</div>
                        </div>
                        
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Fee Type:</span>
                            <span>{fee.paymentType}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Amount:</span>
                            <span>${fee.feeAmount}</span>
                          </div>
                          {fee.dueDate && (
                            <div className="flex justify-between">
                              <span>Due Date:</span>
                              <span>{new Date(fee.dueDate).toLocaleDateString()}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 border rounded-lg">
                      <div className="text-muted-foreground">No fee structure available</div>
                    </div>
                  )}
                  
                  <Button className="w-full" size="lg">
                    Enroll Now
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Schedule */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Class Schedule
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {schedules.length > 0 ? schedules.map((schedule, index) => (
                    <div key={schedule.id} className={`flex justify-between items-center py-2 ${index < classSchedules.length - 1 ? 'border-b' : ''}`}>
                      <span className="font-medium">{new Date(schedule.startDate).toLocaleDateString('en-US', { weekday: 'long' })}</span>
                      <span className="text-muted-foreground">{schedule.sessionStartTime} - {schedule.sessionEndTime}</span>
                    </div>
                  )) : (
                    <p className="text-center text-muted-foreground py-4">No schedules available</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Contact */}
            <Card>
              <CardHeader>
                <CardTitle>Have Questions?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full">
                    Message Teacher
                  </Button>
                  <Button variant="outline" className="w-full">
                    Schedule Free Consultation
                  </Button>
                  <Button variant="outline" className="w-full">
                    Download Brochure
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
    </div>
  );
};