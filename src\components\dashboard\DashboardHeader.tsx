import { useState } from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import React from "react";
import { Bell, User, Menu, ArrowLeft, MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "react-oidc-context";  // Use OIDC auth
import { UserRole } from "@/types";
import { getHomeUrl } from "@/utils/navigation";
import { generateAvatarUrl } from "@/lib/utils";
import { useUserRole } from "@/hooks/useUserRole";
import { toast } from "sonner"; 
import { useDispatch, useSelector } from "react-redux";
import { clearUserData } from '@/redux/userSlice';

export const DashboardHeader: React.FC<{ 
  variant?: "student" | "teacher" | "parent", 
  showBackButton?: boolean 
}> = ({ 
  variant = "teacher",
  showBackButton = false
}) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const auth = useAuth();  // Use OIDC auth
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isAuthenticated = auth.isAuthenticated;
  const dispatch = useDispatch();
  const userData = useSelector((state: any) => state.user.userData);
    
  // Get user from OIDC and Redux
  const user = auth.isAuthenticated ? {
    id: auth.user?.profile.sub || "",
    name: userData?.name || auth.user?.profile.name || "User",
    email: auth.user?.profile.email || "",
    role: (auth.user?.profile["custom:role"] as UserRole) ,
    avatar: generateAvatarUrl(userData?.name || auth.user?.profile.name || "User", "3498db")
  } : null;
 const { selectedRole } = useUserRole();  

  const handleProfileClick = () => {
    navigate('/profile');
  };
  const handleSwitchRoleClick=()=>{
    navigate('/role-selection');
  };
  const handleMembershipClick=()=>{
    navigate('/membershipPage');
  };

  const handleCreateClassClick = () => {
    navigate('/create-class');
  };
const getDashboardLink = () => {
    switch (selectedRole) {
      case UserRole.TEACHER:
        return "/teacher-dashboard";
      case UserRole.STUDENT:
        return "/student-dashboard";
      case UserRole.PARENT:
        return "/parent-dashboard";
      default:
        return "/dashboard";
    }
  };
  
  const getDashboardText = () => {
    switch (selectedRole) {
      case UserRole.TEACHER:
        return "Teacher Dashboard";
      case UserRole.STUDENT:
        return "Student Dashboard";
      case UserRole.PARENT:
        return "Parent Dashboard";
      default:
        return "Dashboard";
    }
  };
  const handleBackClick = () => {
    console.log(user.role)
    const homeUrl = getHomeUrl(user.role);
    navigate(homeUrl);
  };
    const handleLogout = async () => {
        await auth.removeUser();
        dispatch(clearUserData()); // Clears user data from Redux
        window.location.href = window.location.origin + "/logout";
      };
  
 const handleSignUp  = async () => {
   // window.location.href = "https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/signup?client_id=76u1v7el416ebllhpbhtqpmlh0&code_challenge=Cjh7j5XvSKwPZ5ahIhP5j2tuEvZiuoSm811Q62N0wFs&code_challenge_method=S256&redirect_uri=http%3A%2F%2Flocalhost%3A8081%2Flogin%2Foauth2%2Fcode%2Fcognito&response_type=code&scope=email+openid+phone&state=80e73e7091c04c30a0c4904373b2096f";
     setIsSubmitting(true);
    
    try {
      // Store form values in localStorage to access after redirect back from Cognito
    //  localStorage.setItem("registerFormData", JSON.stringify(form.getValues()));
      
      // Redirect to Cognito signup page
      await auth.signinRedirect({ prompt: "login" });
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };
 
  // Determine header background color based on variant
  const headerBgColor = variant === "student" ? "bg-blue-600" : "bg-white";
  const textColor = variant === "student" ? "text-white" : "text-gray-900";
  const iconColor = variant === "student" ? "text-white" : "text-gray-500";

  return (
    <header className={`${headerBgColor} shadow-sm px-4 sm:px-8 py-4 flex justify-between items-center sticky top-0 z-50 backdrop-blur-sm bg-white/80`}>
      {showBackButton ? (
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={handleBackClick}
          className="text-gray-600 hover:bg-gray-100"
        >
          <ArrowLeft className="h-6 w-6" />
        </Button>
      ) : (
        <h1 className={`text-xl sm:text-2xl font-bold text-edu-blue`}>
          <span className="text-edu-blue">Edu</span>
          <span className="text-edu-dark">Connect</span>
        </h1>
      )}
      
      <div className="flex items-center gap-3 sm:gap-6">
        {!isMobile && selectedRole === UserRole.TEACHER && (
          <Button 
                      className="bg-purple-600 hover:bg-purple-700 ml-auto flex items-center gap-2 rounded-full px-8"
            onClick={handleCreateClassClick}
          >
            Create Class
          </Button>
        )}
        <Link to="/chat" className="group">
          <MessageCircle className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600 group-hover:text-edu-blue transition-colors" />
        </Link>
        <Link to="/announcements" className="relative group">
          <Bell className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600 group-hover:text-edu-blue transition-colors" />
          <span className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full"></span>
        </Link>
       <div className="flex items-center gap-4">
          {isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
              <button >
                <User className="h-6 w-6 text-gray-500" />
              </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{user?.name}</p>
                    <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
                    <p className="text-xs leading-none text-muted-foreground capitalize">{selectedRole}</p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleProfileClick}>
                  Profile
                </DropdownMenuItem>
                {selectedRole === UserRole.TEACHER && (
                  <DropdownMenuItem onClick={handleMembershipClick}>
                    Membership
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={handleSwitchRoleClick}>
                  Switch Role
                </DropdownMenuItem>
                
                <DropdownMenuItem asChild>
                  <Link to="/blogs">Blogs</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/settingsPage">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>Log out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex items-center gap-2">
              
            </div>
          )}
        </div>      </div>
    </header>
  );
};
