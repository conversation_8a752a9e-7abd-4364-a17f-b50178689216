
import { ScheduleEvent } from "@/types";
import schedulesData from "@/data/schedules.json";

const expandedSchedulesData: any[] = [
];

// Add some fallback data in case no classes match the filter
const fallbackSchedules: any[] = [
];

const convertDates = (schedules: any[]): ScheduleEvent[] => {
  return schedules.map(schedule => ({
    ...schedule,
    startTime: new Date(schedule.startTime),
    endTime: new Date(schedule.endTime)
  }));
};

export async function createClassScheduleService(scheduleData: any, accessToken: string) {
 console.log("Creating class schedule with data:", scheduleData);
  
  const response = await fetch("/api/scheduleManagement/v1/schedules", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(scheduleData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }

  if (!response.ok) {
    throw new Error("Failed to create class schedule");
  }
  return response.json();
}
export const getAllSchedules = async (accessToken: string) => {
  const response = await fetch("/api/scheduleManagement/v1/schedules/upcoming", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch schedules");
  }
  const data = await response.json();
   // If your API returns an array of schedules, convert date strings to Date objects:
  return data.map((schedule: any) => ({
    ...schedule,
    startTime: new Date(schedule.startTime),
    endTime: new Date(schedule.endTime)
  }));
};

export const getAllSchedulesofStudent = async (accessToken: string) => {
  const response = await fetch('/api/notificationManagement/v1/stp-notifications', {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch schedules");
  }
  const data = await response.json();
   // If your API returns an array of schedules, convert date strings to Date objects:
  return data.map((schedule: any) => ({
    ...schedule,
    startTime: new Date(schedule.startTime),
    endTime: new Date(schedule.endTime)
  }));
};

export async function createClassFeeService(feeData: any, accessToken: string) {
  
 console.log("Creating class schedule with data:", feeData);
  

  const response = await fetch("/api/feeManagement/v1/fee-structures", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(feeData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to create class schedule");
  }
  return response.json();
}


export const getScheduleById = async (id: string): Promise<ScheduleEvent | undefined> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const allSchedules = [...schedulesData, ...expandedSchedulesData, ...fallbackSchedules];
      const schedule = allSchedules.find(s => s.id === id);
      resolve(schedule ? {
        ...schedule,
        startTime: new Date(schedule.startTime),
        endTime: new Date(schedule.endTime)
      } : undefined);
    }, 300);
  });
};
export const getSchedulesByClassId = async (accessToken : string,classId: string) => {
  const response = await fetch(`/api/scheduleManagement/v1/classrooms/${classId}/schedules?page=0&size=20`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch schedules");
  }
  const data = await response.json();
   
  // Check if data is an array, if not, check if it has a content property that might be an array
  const schedules = Array.isArray(data) ? data : (data.content || []);
  // Convert date strings to Date objects
  return schedules.map((schedule: any) => ({
    ...schedule,
    startTime: schedule.startTime ? new Date(schedule.startTime) : null,
    endTime: schedule.endTime ? new Date(schedule.endTime) : null
  }));

};

export const getSchedulesByTeacherId = async (
  teacherId: string, 
  classIds: string[]
): Promise<ScheduleEvent[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const allSchedules = [...schedulesData, ...expandedSchedulesData, ...fallbackSchedules];
      // If no class IDs provided, return all schedules
      const schedules = classIds.length > 0 
        ? allSchedules.filter(s => classIds.includes(s.classId))
        : allSchedules;
      console.log("getSchedulesByTeacherId returning schedules:", schedules.length);
      resolve(convertDates(schedules));
    }, 300);
  });
};

export const updateScheduleStatus = async (
  id: string, 
  status: any,
  accessToken: string
)=> {
  console.log("Updating schedule with data:", status);
  
  const response = await fetch(`/api/scheduleManagement/v1/schedules/${id}/status?status=${status}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  
  if (response.status === 401) {
    window.location.href = "/";
    return null;
  }
  
  if (!response.ok) {
    throw new Error("Failed to update schedule");
  }
  
  const data = await response.json();
  return data;
};

export const createScheduleEvent = async (
  scheduleEvent: Omit<ScheduleEvent, "id">
): Promise<ScheduleEvent> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newScheduleEvent: ScheduleEvent = {
        ...scheduleEvent,
        id: `sch-${Date.now()}`
      };
      
      // In a real app, this would save to backend
      console.log("Created new schedule event:", newScheduleEvent);
      resolve(newScheduleEvent);
    }, 300);
  });
};

export const updateScheduleEvent = async (
  id: string, 
  scheduleData: any,
  accessToken: string
)=> {
  console.log("Updating schedule with data:", scheduleData);
  
  const response = await fetch(`/api/scheduleManagement/v1/schedules/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(scheduleData)
  });
  
  if (response.status === 401) {
    window.location.href = "/";
    return null;
  }
  
  if (!response.ok) {
    throw new Error("Failed to update schedule");
  }
  
  const data = await response.json();
  return data;
};


export const deleteScheduleEvent = async (id: string, accessToken: string)=> {
  const response = await fetch(`/api/scheduleManagement/v1/schedules/${id}/cancel`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  
  if (response.status === 401) {
    window.location.href = "/";
    return false;
  }
  
  if (!response.ok) {
    throw new Error("Failed to delete schedule");
  }
  
  return true;
};
export const getTeacherNextClassAlert = async (accessToken : string) => {
  const response = await fetch(`/api/scheduleManagement/v1/schedules/teacher/next-upcoming`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch schedules");
  }
  const text = await response.text();
  return text ? JSON.parse(text) : null;
};
export const getStudentNextClassAlert = async (accessToken : string) => {
  const response = await fetch(`/api/scheduleManagement/v1/schedules/student/next-upcoming`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch schedules");
  }
  const text = await response.text();
  return text ? JSON.parse(text) : null;
};

export const getTimeZones = async (accessToken : string) => {
  const response = await fetch(`/api/scheduleManagement/v1/timezones`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch schedules");
  }
    return  response.json();

};
