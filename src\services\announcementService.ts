
import { Announcement } from "@/types";
import announcementsData from "@/data/announcements.json";

// Helper function to convert JSON date strings to Date objects
const convertDates = (announcements: any[]): Announcement[] => {
  return announcements.map(announcement => ({
    ...announcement,
    createdAt: new Date(announcement.createdAt),
    fileUrl: null, // Set fileUrl to null by default for all announcements
  }));
};
export async function getClassIdsAndNames(accessToken: string) {
  const response = await fetch("/api/classroomManagement/v1/classrooms/teacher/list", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to fetch class list");
  }
  const data = await response.json();
  // Assuming the response is an array of classes with id and className
  // Adjust the mapping if your API response structure is different
  return (Array.isArray(data) ? data : data.content || []).map((cls: any) => ({
    id: cls.id,
    className: cls.className
  }));
}
// GET: Fetch all announcements
export async function getAllAnnouncements(accessToken: string)
{
   const response = await fetch("/api/notificationManagement/v1/teacher/notifications", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok)
     throw new Error("Failed to fetch announcements");
  return response.json();
}

// GET: Fetch all announcements
export async function getTop5Announcements(accessToken: string)
{
   const response = await fetch("/api/notificationManagement/v1/top5", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok)
     throw new Error("Failed to fetch announcements");
  return response.json();
}

// POST: Create a new announcement
export async function createNewAnnouncement(accessToken: string,id: string, data: any)
 {
  const response = await fetch(`/api/notificationManagement/v1/classroom/${id}/notification`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(data)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) throw new Error("Failed to create announcement");
  return response.json();
}

// POST: Create a new announcement
export async function createNewBulkAnnouncement(accessToken: string, data: any)
 {
  const response = await fetch(`/api/notificationManagement/v1/teacher/bulk`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(data)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) throw new Error("Failed to create announcement");
  return response.json();
}

// Get announcement by ID
export const getAnnouncementById = async (id: string): Promise<Announcement | undefined> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const announcement = announcementsData.find(a => a.id === id);
      resolve(announcement ? {
        ...announcement,
        createdDate: new Date(announcement.createdAt),
        fileUrl: null, // Set fileUrl to null by default
      } : undefined);
    }, 300);
  });
};

// Get announcements by class ID
export const getAnnouncementsByClassId = async (classId: string): Promise<Announcement[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const announcements = announcementsData.filter(a => 
        a.classId === classId || a.classId === "all"
      );
      resolve(convertDates(announcements));
    }, 300);
  });
};


// Update an existing announcement
export async function updateExistingAnnouncement(accessToken: string,id: string, updateData: any) {
  const response = await fetch(`/api/notificationManagement/v1/notification/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(updateData)
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to update announcement");
  }
  return response.json();
}


// Delete an announcement
export async function deleteExistingAnnouncement(accessToken: string,id: string) {
  const response = await fetch(`/api/notificationManagement/v1/notification/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    }
  });
  if (response.status === 401) {
    window.location.href = "/";
    return;
  }
  if (!response.ok) {
    throw new Error("Failed to delete announcement");
  }
  return true;
}