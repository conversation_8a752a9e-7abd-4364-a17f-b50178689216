import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Search ,Sparkles} from 'lucide-react';
import Header from "@/components/layout/Header";
import { toast } from "sonner";
import Footer from "@/components/Footer"; // or your preferred toast library
import { useAuth } from "react-oidc-context";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState('');

  const faqCategories = [
    {
      category: 'Getting Started',
      color: 'bg-blue-500',
      faqs: [
        {
          question: 'How do I create my first class?',
          answer: 'To create your first class, navigate to the "Classes" section from your teacher dashboard and click "Create New Class". Fill in the class details including name, subject, description, and fee structure. You can then add students and set up your schedule.'
        },
        {
          question: 'What are the different user roles?',
          answer: 'Our platform supports three user roles: Teachers (who create and manage classes), Students (who enroll in classes and submit assignments), and Parents (who can monitor their child\'s progress and communicate with teachers).'
        },
        {
          question: 'How do I add students to my class?',
          answer: 'You can add students by sharing your class join code, manually adding them through the "Students" section, or by having them search for your class using your public profile.'
        }
      ]
    },
    {
      category: 'Classes & Scheduling',
      color: 'bg-green-500',
      faqs: [
        {
          question: 'How do I set up recurring class schedules?',
          answer: 'When creating a schedule, select the "Recurring" option and choose your preferred frequency (daily, weekly, monthly). You can set the number of occurrences or an end date for the recurring sessions.'
        },
        {
          question: 'Can I modify a class schedule after creating it?',
          answer: 'Yes, you can modify class schedules at any time. Students enrolled in the class will be automatically notified of any schedule changes via email and platform notifications.'
        },
        {
          question: 'How do I take attendance?',
          answer: 'During a scheduled class session, navigate to the "Attendance" section and mark students as present, absent, or late. You can also add notes for each student\'s attendance record.'
        }
      ]
    },
    {
      category: 'Payments & Billing',
      color: 'bg-orange-500',
      faqs: [
        {
          question: 'How do I set up fees for my classes?',
          answer: 'When creating a class, you can set up a fee structure including one-time enrollment fees, monthly fees, or per-session fees. You can also offer different payment plans and discounts.'
        },
        {
          question: 'When do I receive payments from students?',
          answer: 'Payments are processed securely through our payment gateway. Funds are typically transferred to your account within 2-3 business days after a successful payment.'
        },
        {
          question: 'Can I offer refunds to students?',
          answer: 'Yes, you can process refunds through the payment management section. Refunds are subject to your class refund policy and can be processed in full or partial amounts.'
        }
      ]
    },
    {
      category: 'Assignments & Communication',
      color: 'bg-purple-500',
      faqs: [
        {
          question: 'How do I create and distribute assignments?',
          answer: 'Navigate to your class detail page and use the "Assignments" tab to create new assignments. You can set due dates, attach files, and specify grading criteria. Students will be notified automatically when new assignments are posted.'
        },
        {
          question: 'Can parents communicate with teachers?',
          answer: 'Yes, parents can send messages to teachers through the platform\'s messaging system. Teachers can also schedule parent-teacher meetings and send progress updates.'
        },
        {
          question: 'How do students submit assignments?',
          answer: 'Students can view and submit assignments through their class dashboard. They can upload files, type responses, or submit links as required by the assignment format.'
        }
      ]
    },
    {
      category: 'Technical Support',
      color: 'bg-red-500',
      faqs: [
        {
          question: 'I forgot my password. How do I reset it?',
          answer: 'Click on "Forgot Password" on the login page and enter your email address. You\'ll receive a password reset link via email. Follow the instructions to create a new password.'
        },
        {
          question: 'The platform is running slowly. What should I do?',
          answer: 'First, check your internet connection. If the issue persists, try clearing your browser cache, updating your browser, or switching to a different browser. Contact support if problems continue.'
        },
        {
          question: 'Can I use the platform on mobile devices?',
          answer: 'Yes, our platform is fully responsive and works on all mobile devices and tablets. While we don\'t have a dedicated mobile app yet, the web version provides a mobile-optimized experience.'
        }
      ]
    },
    {
      category: 'Privacy & Security',
      color: 'bg-gray-500',
      faqs: [
        {
          question: 'How is my data protected?',
          answer: 'We use industry-standard encryption for data transmission and storage. All personal information is securely stored and we comply with GDPR, COPPA, and FERPA regulations.'
        },
        {
          question: 'Who can see my class information?',
          answer: 'Only enrolled students, their parents (if applicable), and you as the teacher can access class-specific information. Public profiles show only basic information that you choose to make visible.'
        },
        {
          question: 'Can I delete my account and data?',
          answer: 'Yes, you can request account deletion at any time. We will permanently delete all your personal data within 30 days, except for information we\'re legally required to retain.'
        }
      ]
    }
  ];

  const filteredFAQs = faqCategories.map(category => ({
    ...category,
    faqs: category.faqs.filter(faq =>
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.faqs.length > 0);
 const auth = useAuth();
    const [isSubmitting, setIsSubmitting] = useState(false);

const handleSignUp  = async () => {
   // window.location.href = "https://us-east-1cfpzwbr4p.auth.us-east-1.amazoncognito.com/signup?client_id=76u1v7el416ebllhpbhtqpmlh0&code_challenge=Cjh7j5XvSKwPZ5ahIhP5j2tuEvZiuoSm811Q62N0wFs&code_challenge_method=S256&redirect_uri=http%3A%2F%2Flocalhost%3A8081%2Flogin%2Foauth2%2Fcode%2Fcognito&response_type=code&scope=email+openid+phone&state=80e73e7091c04c30a0c4904373b2096f";
     setIsSubmitting(true);
    
    try {
      // Store form values in localStorage to access after redirect back from Cognito
    //  localStorage.setItem("registerFormData", JSON.stringify(form.getValues()));
      
      // Redirect to Cognito signup page
      await auth.signinRedirect({ prompt: "login" });
    } catch (error: any) {
      toast.error(`Registration failed: ${error.message}`);
      setIsSubmitting(false);
    }
  };
  
 
  return (
    <div className="min-h-screen bg-background ">
      <Header/>
       {/* Hero Section with gradient background */}
        <section className="py-16 md:py-24 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-purple-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-blue-600/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="container mx-auto relative z-10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
              <div className="max-w-2xl text-center md:text-left">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white leading-tight">
                  Transform <span className="text-yellow-300">Education</span> Through Innovation
                </h1>
                <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
                  Join thousands of educators and students in revolutionizing the learning experience
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <Button asChild size="lg" className="w-full sm:w-auto bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full shadow-lg">
                    <Link to="/register">Get Started Free</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="w-full sm:w-auto border-2 bg-white text-purple-600 hover:bg-yellow-300 hover:text-purple-700 transition-all duration-300 text-lg px-8 py-6 rounded-full">
                    <Link to="/subscription">View Pricing</Link>
                  </Button>
                </div>
                <div className="mt-12 flex flex-col sm:flex-row items-center justify-center md:justify-start gap-6">
                  <div className="flex -space-x-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="w-12 h-12 rounded-full border-4 border-purple-600 bg-white shadow-lg flex items-center justify-center text-purple-600 font-bold text-lg">
                        {String.fromCharCode(64 + i)}
                      </div>
                    ))}
                  </div>
                  <p className="text-lg text-white/90">
                    Joined by <span className="font-bold text-yellow-300">2000+</span> educators
                  </p>
                </div>
              </div>
              <div className="w-full md:w-2/5 relative">
                <div className="bg-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-transform duration-500">
                  <img 
                    src="https://images.unsplash.com/photo-1571260899304-425eee4c7efc?q=80&w=2070&auto=format&fit=crop" 
                    alt="EduConnect Platform" 
                    className="w-full h-64 md:h-96 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800">Modern Learning Experience</h3>
                    <p className="text-gray-600">Interactive tools for better engagement</p>
                  </div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-xl shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <p className="text-lg font-bold flex items-center gap-2">
                    <Sparkles className="h-5 w-5" />
                    AI-Powered Learning
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      <div className="max-w-4xl mx-auto mt-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">Frequently Asked Questions</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Find quick answers to common questions about our educational platform
          </p>
          
          {/* Search Bar */}
          <div className="relative max-w-2xl mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="edu-form-field pl-10 py-4"
            />
          </div>
        </div>

        {/* FAQ Categories */}
        <div className="space-y-8 p-4">
          {(searchQuery ? filteredFAQs : faqCategories).map((category, categoryIndex) => (
            <Card key={categoryIndex}>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full ${category.color}`}></div>
                  {category.category}
                  <Badge variant="secondary" className="ml-auto">
                    {category.faqs.length} questions
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="space-y-2">
                  {category.faqs.map((faq, faqIndex) => (
                    <AccordionItem key={faqIndex} value={`${categoryIndex}-${faqIndex}`} className="border rounded-lg px-4">
                      <AccordionTrigger className="text-left hover:no-underline">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="text-muted-foreground">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {searchQuery && filteredFAQs.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <p className="text-lg text-muted-foreground mb-4">
                No FAQs found matching "{searchQuery}"
              </p>
              <p className="text-sm text-muted-foreground">
                Try adjusting your search terms or browse the categories above.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Contact Support */}
        <Card className="mt-12 bg-primary/5 border-primary/20">
          <CardContent className="text-center py-8">
            <h3 className="text-xl font-semibold mb-4">Still have questions?</h3>
            <p className="text-muted-foreground mb-6">
              If you can't find the answer you're looking for, our support team is ready to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/help-center" className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors">
                Visit Help Center
              </a>
              <a href="mailto:<EMAIL>" className="px-6 py-2 border border-border rounded-md hover:bg-accent transition-colors">
                Contact Support
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
       <footer className="bg-gray-900 text-white py-8 md:py-12">
                    <Footer/>
                  </footer>
    </div>
  );
}