{"features": [{"id": "basic-access", "name": "Basic Access", "description": "Core platform features", "monthlyPrice": 0, "yearlyPrice": 0, "isFree": true, "isDefault": true}, {"id": "class-management", "name": "Class Management", "description": "Unlimited classes", "monthlyPrice": 0, "yearlyPrice": 0, "isFree": true, "isDefault": true}, {"id": "advanced-analytics", "name": "Advanced Analytics", "description": "Detailed insights and reports", "monthlyPrice": 15, "yearlyPrice": 150, "isFree": false, "isDefault": false}, {"id": "ai-assistant", "name": "AI Teaching Assistant", "description": "Auto question generation", "monthlyPrice": 25, "yearlyPrice": 250, "isFree": false, "isDefault": false}, {"id": "priority-support", "name": "Priority Support", "description": "24/7 dedicated support", "monthlyPrice": 10, "yearlyPrice": 100, "isFree": false, "isDefault": false}, {"id": "custom-branding", "name": "Custom Branding", "description": "White-label solution", "monthlyPrice": 30, "yearlyPrice": 300, "isFree": false, "isDefault": false}, {"id": "advanced-integrations", "name": "Advanced Integrations", "description": "Third-party tools", "monthlyPrice": 20, "yearlyPrice": 200, "isFree": false, "isDefault": false}, {"id": "student-portal", "name": "Student Portal", "description": "Dedicated student access", "monthlyPrice": 12, "yearlyPrice": 120, "isFree": false, "isDefault": false}, {"id": "parent-dashboard", "name": "Parent Dashboard", "description": "Parent progress tracking", "monthlyPrice": 8, "yearlyPrice": 80, "isFree": false, "isDefault": false}, {"id": "automated-grading", "name": "Automated Grading", "description": "AI-powered assessment", "monthlyPrice": 18, "yearlyPrice": 180, "isFree": false, "isDefault": false}]}